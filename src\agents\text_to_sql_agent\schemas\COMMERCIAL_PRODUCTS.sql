CREATE TABLE DWH_PUBLIC.COMMERCIAL_PRODUCTS (
	INTERNAL_CODE NVARCHAR2(50), -- Always use in OR with DWH_PUBLIC.COMMERCIAL_PRODUCTS.PRODUCT_NUMBER
	SHORT_DESCRIPTION NVARCHAR2(1000),
	LONG_DESCRIPTION CLOB,
	START_PRODUCTION_DATE TIMESTAMP (6),
	STOP_PRODUCTION_DATE TIMESTAMP (6),
	START_DISTRIBUTION_DATE TIMESTAMP (6),
	STOP_DISTRIBUTION_DATE TIMESTAMP (6),
	PRODUCTION_STATUS NUMBER(38, 0),
	PRODUCTION_STATUS_DESCRIPTION NVARCHAR2(50),
	DISTRIBUTION_STATUS NCHAR(1),
	DISTRIBUTION_STATUS_DESCRIPTION NVARCHAR2(40),
	PLANT_CODE NVARCHAR2(20),
	PLANT_DESCRIPTION NVARCHAR2(100),
	FACTORY_CODE NVARCHAR2(10),
	FACTORY_DESCRIPTION NVARCHAR2(60),
	CUSTOM_CLASS NVARCHAR2(40),
	<PERSON><PERSON><PERSON>_OF_PRODUCT NVARCHAR2(60),
	COUNTRY_OF_ORIGIN_ISO_CODE NVARCHAR2(8),
	COUNTRY_OF_ORIGIN_DECLARATION_EXPIRY_DATE TIMESTAMP (6),
	PREFERENTIAL_ORIGIN NVARCHAR2(2), -- Either 'P' for "preferential" or 'C' for "common"
	PREFERENTIAL_ORIGIN_PERCENTAGE_EU FLOAT(53),
	COO_ISO2 CHAR(2 BYTE),
	COUNTRY_OF_ORIGIN NVARCHAR2(100),
	PREFERENTIAL_ORIGIN_PERCENTAGE_EXTRA_EU FLOAT(53),
	PRODUCT_NUMBER NVARCHAR2(60), -- Always use in OR with DWH_PUBLIC.COMMERCIAL_PRODUCTS.INTERNAL_CODE
	FACTORY_MODEL NVARCHAR2(60),
	EXTERNAL_MODEL_NAME NVARCHAR2(60),
	LANGUAGE_GROUP NVARCHAR2(10),
	IRONER_FRONT_COLOR NVARCHAR2(200),
	WASHER_FRONT_COLOR NVARCHAR2(200),
	DRYER_FRONT_COLOR NVARCHAR2(100),
	IRONER_SIDE_COLOR NVARCHAR2(200),
	WASHER_SIDE_COLOR NVARCHAR2(200),
	DRYER_SIDE_COLOR NVARCHAR2(100),
	ELECTRICAL_CONNECTION_VOLTAGE NVARCHAR2(200),
	ELECTRICAL_CONNECTION_FREQUENCY NVARCHAR2(200),
	ELECTRICAL_CONNECTION_PHASE NVARCHAR2(200),
	HEATING_TYPE NVARCHAR2(64),
	WASHER_COINMETER NVARCHAR2(40),
	DRYER_COINMETER NVARCHAR2(40),
	DRYER_CONTROL_SYSTEM NVARCHAR2(100),
	IRONER_CONTROL_SYSTEM NVARCHAR2(200),
	WASHER_CONTROL_SYSTEM NVARCHAR2(200),
	DRYER_SOFTWARE_CODE NVARCHAR2(100),
	WASHER_SOFTWARE_CODE NVARCHAR2(200),
	WATER_CONNECTION_TEMPERATURE NVARCHAR2(20),
	IRONER_NUMBER_OF_IO_BOARDS FLOAT(126),
	WASHER_NUMBER_OF_IO_BOARDS FLOAT(126),
	FACTORY_PRODUCT_NUMBER NVARCHAR2(60),
	COMPANY_OWNER NVARCHAR2(40),
	BELSCODE NUMBER(3,0),
	SFATHER VARCHAR(25),
	PRODUCT_CAPACITY NUMBER, 
	CURRENT_CONSUMPTION NUMBER, 
	PRODUCT_DEPTH NUMBER, 
	MAXIMUM_CURRENT_CONSUMPTION NUMBER, 
	MINIMUM_CURRENT_CONSUMPTION NUMBER, 
	GAS_MAX_CONS NUMBER, 
	GAS_POWER NUMBER, 
	PRODUCT_HEIGHT NUMBER, 
	WC_MIN_PRESSURE NUMBER, 
	PRODUCT_WEIGHT NUMBER, 
	PRODUCT_WIDTH NUMBER, 
	CERTIFICATION_GROUP NVARCHAR2(25), 
	EC_PLUG_TYPE NVARCHAR2(50), 
	EC_PREDISPOSED_FOR NVARCHAR2(1000), 
	WATER_PROOF_INDEX NVARCHAR2(100), 
	GAS_DELIVERY NVARCHAR2(100), 
	GAS_DELIVERY_PRESSURE NVARCHAR2(50), 
	GAS_INLET_INCH NVARCHAR2(100), 
	SC_GENERATION NVARCHAR2(100), 
	SC_INLET NVARCHAR2(100), 
	WC_INLET_COLD NVARCHAR2(100), 
	WC_INLET_HOT NVARCHAR2(100), 
	WD_OUTLET NVARCHAR2(100), 
	WTYPE NVARCHAR2(100), 
	CONSTRAINT PK_COMMERCIAL_PRODUCTS PRIMARY KEY (INTERNAL_CODE),
  	USING INDEX  ENABLE,
	PRIMARY KEY ( INTERNAL_CODE )
)