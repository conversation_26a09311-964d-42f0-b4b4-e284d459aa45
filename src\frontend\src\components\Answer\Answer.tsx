import { useEffect, useMemo, useRef, useState } from "react";
import { Stack, IconButton, PrimaryButton, Label, Checkbox, on } from "@fluentui/react";
import DOMPurify from "dompurify";
import React from "react";
import styles from "./Answer.module.css";
import './Zoom.css';
import Zoom from 'react-medium-image-zoom';
import { MANUALS_MAPPING } from "../../api";
import { ChatResponse, getCitationFilePath, FeedbackType, Agents } from "../../api";
import { parseAnswerToHtml, parseStreamAnswerToHtml } from "./AnswerParser";
import { AnswerIcon } from "./AnswerIcon";
import { ErrorCircle20Regular, ThumbLike20Regular, ThumbDislike20Regular, ThumbLike20Filled, ThumbDislike20Filled } from "@fluentui/react-icons";

// Sub-components
import { StreamedAnswer } from './AnswerSubComponents/StreamedAnswer';
import { MultipleSelection } from './AnswerSubComponents/MultipleSelection';
import { SingleSelection } from './AnswerSubComponents/SingleSelection';
import { ImageGallery } from './AnswerSubComponents/ImageGallery';
import { AnswerActions } from './AnswerSubComponents/AnswerActions';
import { DownloadButton } from "./AnswerSubComponents/DownloadButton";
import { AnswerHeaderButtons } from "./AnswerSubComponents/AnswerHeadersButtons";
import { AnswerBotActions } from "./AnswerSubComponents/AnswerBotActions";
import { FollowupQuestion } from "./AnswerSubComponents/FollowupQuestions";
import { Citations, RetryButton } from "./AnswerSubComponents";

interface Props {
    chatResponse: ChatResponse | ReadableStreamDefaultReader;
    isSelected?: boolean;
    onCitationClicked: (filePath: string) => void;
    onThoughtProcessClicked: () => void;
    onSupportingContentClicked: () => void;
    onFollowupQuestionClicked?: (question: string) => void;
    showFollowupQuestions?: boolean;
    onRetryClicked?: () => void;
    onChangeAgent?: () => void;
    callAgent: (choice: Agents, document_types: Array<string> | undefined) => void;
    retryable: boolean;
    onFeedbackClicked?: (dialogId: string, feedback: FeedbackType) => void;
    onExcelClicked?: (dialogId: string) => void;
    conversationId: string;
    dialogId: string;
    selectedBot: string | number | null;
    onRetryWithoutPreview?: () => void;
    preview: boolean;
    setPreview: (flag: boolean) => void;
    onContext: (contextData: Object | ChatResponse) => void;

}

export const Answer = ({
    chatResponse,
    isSelected,
    onCitationClicked,
    onThoughtProcessClicked,
    onSupportingContentClicked,
    onFollowupQuestionClicked,
    showFollowupQuestions,
    onRetryClicked,
    onChangeAgent,
    callAgent,
    retryable,
    onFeedbackClicked,
    onExcelClicked,
    conversationId,
    dialogId,
    selectedBot,
    onRetryWithoutPreview,
    preview,
    setPreview,
    onContext
}: Props) => {
    const [isDownloading, setIsDownloading] = useState<boolean>(true);

    // Handle file download effect
    useEffect(() => {
        let isMounted = true; // Flag per verificare se il componente è montato

        if (isDownloading && !preview) {
            window.location.href = `/export-data/${conversationId}/${dialogId}`;
        }

        if (isMounted) {
            setPreview(true); // Aggiorna lo stato solo se il componente è montato
        }

        return () => {
            isMounted = false; // Imposta il flag a false quando il componente viene smontato
        };
    }, [isDownloading, preview, conversationId, dialogId, setPreview]);

    const handleDownloadClick = () => {
        setIsDownloading(true);
        if (onRetryWithoutPreview) {
            onRetryWithoutPreview();
        }
    };



    // For streaming responses
    if (chatResponse instanceof ReadableStreamDefaultReader) {
        return (
            <StreamedAnswer
                chatResponse={chatResponse}
                isSelected={isSelected}
                onThoughtProcessClicked={onThoughtProcessClicked}
                onSupportingContentClicked={onSupportingContentClicked}
                onFeedbackClicked={onFeedbackClicked}
                onExcelClicked={onExcelClicked}
                conversationId={conversationId}
                dialogId={dialogId}
                selectedBot={selectedBot}
                preview={preview}
                onChangeAgent={onChangeAgent}
                handleDownloadClick={handleDownloadClick}
                onContext={onContext}
            />
        );
    }

    // For regular responses
    const parsedAnswer = useMemo(() =>
        parseAnswerToHtml(chatResponse, onCitationClicked),
        [chatResponse, onCitationClicked]
    );

    const sanitizedAnswerHtml = DOMPurify.sanitize(parsedAnswer.answerHtml);

    // Send context to parent component
    onContext(chatResponse);

    return (
        <Stack
            id={chatResponse.dialog_id}
            className={`${styles.answerContainer} ${isSelected && styles.selected}`}
            verticalAlign="space-between"
        >
            <Stack.Item>
                <Stack horizontal>
                    <AnswerIcon />
                    <div>
                        <AnswerHeaderButtons
                            chatResponse={chatResponse}
                            onSupportingContentClicked={onSupportingContentClicked}
                            onThoughtProcessClicked={onThoughtProcessClicked}
                        ></AnswerHeaderButtons>
                    </div>
                </Stack>
            </Stack.Item>

            <Stack.Item grow>
                {chatResponse.selection ? (
                    <div className={styles.selectionContainer}>
                        <div
                            className={styles.answerText}
                            dangerouslySetInnerHTML={{ __html: sanitizedAnswerHtml }}
                        />

                        {chatResponse.selection.is_multiple ? (
                            <MultipleSelection
                                choices={chatResponse.selection.choices}
                                callAgent={callAgent}
                            />
                        ) : (
                            <SingleSelection
                                choices={chatResponse.selection.choices}
                                callAgent={callAgent}
                                sanitizedAnswerHtml={sanitizedAnswerHtml}
                            />
                        )}
                    </div>
                ) : (
                    <>
                        <div
                            className={styles.answerText}
                            dangerouslySetInnerHTML={{ __html: sanitizedAnswerHtml }}
                        />

                        <DownloadButton
                            chatResponse={chatResponse}
                        ></DownloadButton>

                        <RetryButton
                            retryable={retryable}
                            onRetryClicked={onRetryClicked}
                        ></RetryButton>
                    </>
                )}
            </Stack.Item>

            {/* Citations section */}
            <Citations
                parsedAnswer={parsedAnswer}
                onCitationClicked={onCitationClicked}
            ></Citations>

            {/* Follow-up questions section */}
            <FollowupQuestion
                parsedAnswer={parsedAnswer}
                onFollowupQuestionClicked={onFollowupQuestionClicked}
                showFollowupQuestions={showFollowupQuestions}
            ></FollowupQuestion>

            {/* Feedback and export buttons */}
            <AnswerActions
                onExcelClicked={onExcelClicked}
                selectedBot={selectedBot}
                chatResponse={chatResponse}
                conversationId={conversationId}
                dialogId={dialogId}
                onFeedbackClicked={onFeedbackClicked}
            />

            {/* Images section */}
            <ImageGallery images={chatResponse.images} selectedBot={selectedBot} />

            {/* Bot action buttons section */}
            <AnswerBotActions
                chatResponse={chatResponse}
                selectedBot={selectedBot}
                preview={preview}
                onChangeAgent={onChangeAgent}
                handleDownloadClick={handleDownloadClick}
            ></AnswerBotActions>
        </Stack>
    );
};