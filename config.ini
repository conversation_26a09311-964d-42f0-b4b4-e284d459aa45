[web]
flask_app = src
flask_debug = 1

[common]
run_mode = TEST
version_gitrev = 97f862
version_gittag = nn
version_release = 1.1.1
logout_url = https://electroluxprofessional.unily.com

[log]
loglevel = INFO
log_folder = .

[ad]
client_id = f583e81c-71f6-47b3-860b-f7503f95ba56
authority_uri = 7849ddb5-cc3f-42e6-b0f1-1102b2c2600c
redirect_uri = https://localhost:5000/cb/getAToken
ad_scope=.default
ad_endpoint_api=https://graph.microsoft.com/v1.0/me
ad_schema_callback=https

[db]
db_name =  complibot
db_host = eldbt0909.epr.electroluxprofessional.com
db_port = 1433
db_user = complibot_user
db_driver = ODBC Driver 17 for SQL Server

[txt2sql.oracle_oci]
oci_client_path = c:\
oci_dsn = dwhtest_high
oci_schema = DWH_PUBLIC

[txt2sql.llm]
version = 2024-05-01-preview
endpoint = 	https://epr002-oai-cmn-dev-weu-001.int.electroluxprofessional.com/
deployment = gpt-4o-mini

[txt2sql.embedding]
version = 2024-05-01-preview
endpoint = https://epr002-oai-cmn-dev-weu-001.int.electroluxprofessional.com/
deployment = text-embedding-3-large

[txt2sql.preliminary]
deployment= gpt-4o-mini


[rag.llm]
version = 2024-05-01-preview
endpoint = 	https://epr002-oai-cmn-dev-weu-001.int.electroluxprofessional.com/
deployment = gpt-4o-mini

[rag.embedding]
version = 2023-12-01-preview
endpoint = https://epr002-oai-cmn-dev-weu-001.int.electroluxprofessional.com/
deployment = text-embedding-ada-002


[rag_indexer.document_intelligence]
endpoint = https://epr-ai-rag-di-weu-dev-loader.cognitiveservices.azure.com/

[rag_indexer.llm]
version = 2023-12-01-preview
endpoint = https://epr002-oai-cmn-dev-weu-001.int.electroluxprofessional.com/
deployment = gpt-4o-mini
temperature = 0.25


[translator]
endpoint = https://api.cognitive.microsofttranslator.com

[translator.llm]
version = 2025-01-01-preview
endpoint = https://epr002-oai-cmn-dev-weu-001.int.electroluxprofessional.com/
deployment = gpt-4.1-mini


[image_generator]
version = 2025-04-01-preview
endpoint = 	https://epr002-oai-test-dev-plc-001.openai.azure.com/
deployment = gpt-image-1
temperature = 0.25


[compli_bot.txt2sql.azure_ai_search]
index = txt2sql-examples
endpoint = https://epr002-srch-cmn-all-weu-001.int.electroluxprofessional.com/

[call_center_bot.txt2sql.azure_ai_search]
index = txt2sql-examples
endpoint = https://epr002-srch-cmn-all-weu-001.int.electroluxprofessional.com/

[call_center_bot.rag.azure_ai_search]
index = it-documents-pride
endpoint = https://epr002-srch-cmn-all-weu-001.int.electroluxprofessional.com/

[call_center_bot.rag.azure_ai_search.param]
search_profile = hnsw
vector_algorithm = hnsw-cosine
semantic_configuration = semantic-rerank

[call_center_bot.rag.azure_blob_storage]
url = https://epr002stcmnallweu001.int.electroluxprofessional.com/
name = epr002stcmnallweu001
table_container = pride-tables-rag
image_container = pride-images-rag

[call_center_bot.rag_indexer.fetcher]
local_root =
pride_products_root =


[how_to_bot.rag.azure_ai_search]
index = how-to-bot-documents
endpoint = https://epr002-srch-cmn-all-weu-001.int.electroluxprofessional.com/

[how_to_bot.rag.azure_ai_search.param]
search_profile = hnsw
vector_algorithm = hnsw-cosine
semantic_configuration = semantic-rerank

[how_to_bot.rag_indexer.fetcher]
local_root =
pride_products_root =

[how_to_bot.rag.azure_blob_storage]
url = https://epr002stcmnallweu001.int.electroluxprofessional.com/
name = epr002stcmnallweu001
table_container = how-to-bot-tables-rag
image_container = how-to-bot-images-rag


[jdanallo.rag.azure_ai_search]
index = jde-documents
endpoint = https://epr002-srch-cmn-all-weu-001.int.electroluxprofessional.com/

[jdanallo.rag.azure_ai_search.param]
search_profile = vector-profile-jde
semantic_configuration = semantic-jde

[jdanallo.rag_indexer.fetcher]
local_root = C:\Users\<USER>\JDE_Documentation
pride_products_root =

[jdanallo.rag.azure_blob_storage]
url = https://epr002stcmnallweu001.int.electroluxprofessional.com/
name = epr002stcmnallweu001
table_container = jdallo-tables-rag
image_container = jdallo-images-rag


[seobot.rag.azure_ai_search]
endpoint = https://epr002-srch-cmn-all-weu-001.int.electroluxprofessional.com/
index = seo-bot-documents

[seobot.rag.azure_ai_search.param]
search_profile = vector-profile-seo
semantic_configuration = semantic-seo

[seobot.rag_indexer.fetcher]
local_root = C:\Users\<USER>\SEO_Documentation
pride_products_root =

[seobot.rag.azure_blob_storage]
url = https://epr002stcmnallweu001.int.electroluxprofessional.com/
name = epr002stcmnallweu001
table_container = seo-bot-tables-rag
image_container = seo-bot-images-rag