from openai import AzureOpenAI

from config.config import EproExcelLaConfig

class CustomClass:

    def __init__(self):
        self.cfg = EproExcelLaConfig()

        # Init client
        self.client = AzureOpenAI(
            api_key=self.cfg.translator_llm_key,
            api_version=self.cfg.translator_llm_version,
            azure_endpoint=self.cfg.translator_llm_endpoint,
        )

    def get_column(self, column):

        system_prompt = """
            You are a virtual assistant expert in elaborating user prompt.
            Your task is to understand which column contains the description to find the correct CN code for their products based on the descriptions provided.
            ou must pay attention on the request/prompt given by the user, if the column is'nt specified you have to understand it from the name of the columns.
            The CN code can be called harmonized system (HS), sistema armonizzato (SA), custom cluss (CC)
            In excel file commonly the name of the column with the description need for the generation of the custom class is description, desc, dsc, article_desc, description.
            Your task is to return just the needed column.
        """

        assistant_prompt = """
            User: Give me the custom class of the product by the description, choose the column from the following list: [prod, prod_id. prod_name, prod_description]
            prod_description

            User: Give me the custom class of the product, choose the column from the following list: [prod, prod_id. prod_name, description]
            description

            User: Give me the custom class of the article, choose the column from the following list: [id, general_info, dsc]
            dsc
        """

        messages = [{"role": "system", "content": system_prompt},
                    {"role": "user", "content": "Give me the custom class of the product by the description or short description, choose the column from the following list:" + str(column)},
                    {"role": "assistant", "content": assistant_prompt}]

        response = self.client.chat.completions.create(
            model=self.cfg.translator_llm_deployment,
            messages=messages,
            temperature=0,
            top_p=0.1,
        )
        
        return str(response.choices[0].message.content)

    def custom_class(self, data):
        """This function interprets the user's pormpt and returns a JSON file containing the information needed for translation.
        Args:
            request (str): The request in natural language posed by the user.
            columns (list): The list with the column ids.
            sample (DataFrame): A subset of the Excel elements used to identify the language of the file or column.
        
        Returns:
            Answer: An object containing all relevant values for the answer.

        """

        system_prompt = """
            You are a virtual assistant expert in classifying products according to the Combined Nomenclature (CN) of the European Union. 
            Your task is to help users find the correct CN code for their products based on the descriptions provided. 
            You must be precise, clear, and are not needed explanations.
            Your task is just to return the code of the custom class, nothing else.
            If the description passed isn't in english translate it in english and then generate the CN code.
            Consider this list of example to generate the codes: 84186900:POZZETTO COOL HEAD CF 708,84186900:POZZETTO COOL HEAD CF 708,84146000:CAPPA LFC 316 X,94032080:PENSILE CON PORTE SCORREVOLI H660 MM,94032080:TAVOLO DA LAVORO SENZA ALZATINA CON RIPIANO,94032080:PIANO LAVORO 10/10 CON ALZATINA,84146000:CAPPA LFC316X,85141980:FORNO ELETTRICO KOHHH00X,84186900:COMPLESSO CELLE ,84186900:IMPIANTO CELLA TN ,84186900:IMPIANTI CELLA BT ,94032080:ARMADIETTO SPOGLIATOIO CON DIVISORIO SPORCO/PULITO PROF.350MM,84186900:CELLA TN,94032080:RAMPA DI RISALITA SPECIALE REALIZZATA ALL'INTERNO DELLA CELLA,94032080:TAVOLO PRELAVAGGIO,94032080:TAVOLO AMBURGO,94032080:ARMADIO PULIZIA C/PORTE BATTENTI C/TETTO INCLINATO,84186900:TAVOLO FREEZER 3 CASSETTI - NO TOP,84146000:CAPPA CENTRALE INOX 304+FILTRI 320X220 - IN DUE PEZZI,84032080:SCAFFALATURA MOD. AL-2 PER CELLA BT GELO,84186900:COMPLESSO 4 CELLE 1 BT + 3 TN S10 CP HI 2030 DIM ESTERNE MM 5420 X 1430 X 2230H,84186900:IMPIANTO FRIGORIFERO MISASPLIT MS 6N + 10 MT TUBI,84186900:CELLA BT GELO DIM. ESTERNE  4830 X 3230 X 2630H,94032080:TAVOLO AMBURGO,84198998:CUCINA LINEARE COMPLETA DI ELETTRODOMESTICI,85144000:4 FUOCHI GAS KGS 6436 SX,94032080:ARMADIETTO SPOGLIATOIO CON DIVISORIO SPORCO/PULITO PROF.350MM,94032080:PIANO LAVORO 10/10 CON ALZATINA,94032080:TAVOLO ARMADIATO SU RUOTE CON ALZATINA E PORTE SCORREVOLI,94032080:TAVOLO USCITA SENZA ALZATINA, CON AGGANCIO, CON RIPIANO,84186900:ARMADIO VERTICALE CON PORTE SCORREVOLI H2000 MM,94032080:TAVOLO DA LAVORO SU RUOTE, SENZA ALZATINA CON RIPIANO,94032080:ROLL CONTAINER 1 BASE + 2 SPONDE LAT. + 2 CINGHIE,84186900:VETRINETTA REFRIGERATA 1200 x 335 x 230,84186900:FRIGO FREE LTB1AE24W0,84186900:CONGELATORE A POZZETTO CF 708,84198998:CUCINA LINEARE,85144000:4 FUOCHI GAS KGS 6436 SX,85141980:FORNO ELETTRICO KOHH04X,84186900:FRIGO FREE LTB1AE24W0,84186900:IMPIANTO FRIGORIFERO MISASPLIT MS 4P + 10 M TUBAZIONI,84186900:ARMADIO VERICALE NEUTRO 2 PORTE BATTENTE PER STOVIGLIE, 700MM - LAVAGGIO - PIANO 5,84186900:IMPIANTOFRIGORIFERO REMOTO ERMETICO MISAERM MEN 6 + 15 M TUBAZIONI,84186900:FRIGO FREESTANDING LTB1AE24WO,94032080:PENSILE CON 1 PORTA BATTENTE AD ANGOLO - PIANO TERRA - PREPARAZIONI,94032080:PENSILE CON 1 PORTA BATTENTE, 500 MM - PIANO TERRA - PREPARAZIONI,84186900:CELLA TN RIFIUTI DIM ESTERNE 3750 X 1350 X 2490H,94032080:Veletta su due lati in acciaio inox A304 H500 mm,94032080:TAVOLO AMBURGO,84186900:VETRINETTA REFRIGERATA 1200 x 335 x 230,84186900:CONSERVATORE BT CON COPERCHIO MOD. F500 ,84186900:CONSERVATORE BT CON COPERCHIO MOD. F500 ,94032080:MENSOLA A PONTE 2 LIVELLI RISCALDATA,94032080:TAVOLO ARMADIATO SU RUOTE CON ALZATINA E PORTE SCORREVOLI,94032080:MENSOLA A PONTE 2 LIVELLI NEUTRA,94032080:PIANO LAVORO 10/10 CON ALZATINA,85144000:4 FUOCHI GAS KGS 6436 SX,85141980:FORNO A GAS DA INCASSO F 13 GX,94032080:MENSOLA A PONTE 1 LIVELLO NEUTRA,94032080:MENSOLA REFRIGERATA GN 2150MM,94032080:MENSOLA A PONTE 1 LIVELLO NEUTRA,85141980:FORNO MICROONDE, 18LT, 1600W,94032080:MENSOLA REFRIGERATA GN 2150MM,84146000:CAPPA LFC 316 X,84146000:CAPPA A PARETE CUBICA 1200x1200,85144000:4 FUOCHI GAS KGS 6436 SX,84146000:CAPPA LFC 316 X,85141980:FORNO ELETTRICO KOHH04X,84186900:FRIGO FREE LTB1AE24W0,84186900:CELLA SINGOLA TN S6 CP Hi mm 2030 Dimensioni Esterne mm 2950 x 2550 x 2150 H,84186900:IMPIANTO FRIGORIFERO CELLA SINGOLA TN S6 CP Hi mm 2030,85144000:4-ZONE BUILT - IN-INDUCTION HOB -PNC 94949251 - modelEIB60424CK,84146000:CAPPA A PARETE CUBICA 1200x1200 ,85141980:FORNO PIZZA  ELETTRICO 2 CAMERE - 12 PIZZE - iD 105.65 D,84186900:CELLA  TN CARNI DIM ESTERNE 4750 X 2750 X 2490H,84198998:CUCINA LINEARE,84032080:PENSILE CON 2 PORTE SCORREVOLI 1000MM - CUCINA - PIANO TERRA,84186900:CELLE TN SA/FO+ORTOFRUTTA,94032080:SCAFFALI SA/FO/ORTOFR,84186900:CELLE TN CIBI  PRONTI 1 + CIBI PRONTI 2,94032080:PENSILE CON 2 PORTE SCORREVOLI, 1200MM - PIANO TERRA - PREPARAZIONI,94032080:PENSILE CON 2 PORTE SCORREVOLI, 1200MM - PIANO TERRA - PREPARAZIONI,84146000:CAPPA A PARETE CUBICA 2000x1100,84186900:Cella bt -20°dim. 2030*1630*2230 dim. est.-spec.- cerniera porta sx.offerta GC240193/A del 9.10.24,84186900:VETRINA REFRIGERATA 1200MM,94032080:TAVOLO SUPPORTO FORNO,85141980:FORNO ELETTRICO KOHH04X,94032080:TAVOLO DA LAVORO CON ALZATINA E TRAVERSE,94032080:TAVOLO PRELAVAGGIO,94032080:MENSOLA REFRIGERATA GN 2000MM,94032080:TAVOLO USCITA SENZA ALZATINA, CON AGGANCIO, CON RIPIANO,84186900:ARMADIO VERTICALE CON PORTE SCORREVOLI H2000 MM,94032080:BANCO LAVORO CON PIANO REFRIGERATO dim. 3000x800x900,94032080:TAVOLO SU GAMBE SENZA RIPIANO CON ALZ. POSTERIORE E LATERALE SX,94032080:TAVOLO AMBURGO ,94032080:TAVOLO ARMADIATO SU RUOTE CON ALZATINA CON PORTE SCORREVOLI E CASSETTIERA 3 CASSETTI,94032080:TAVOLO ENTRATA AD L PER LAVASTOVIGLIE CESTO TRASCINATO, FORI SBARAZZO CON VASCA CON ALZATINA - LAVAGGIO - PIANO TERRA,94032080:MENSOLA A PONTE 2 LIVELLI NEUTRA,94032080:PENSILE CON PORTE SCORREVOLI H660 MM,94032080:TAVOLO DA LAVORO SENZA ALZATINA CON RIPIANO,84186900:complesso 2 celle TN + BT S10 CP Hi mm 2030,84186900:FREEZER SOTTOTAVOLO 150 LT mod. N201X-R2 DIAMOND,84146000:CAPPA CENTRALE CUBICA A FLUSSO BILANCIATO 4800X2600,84146000:CAPPA A PARETE CUBICA 1600x1100,84146000:CAPPA CENTRALE CUBICA 1600x1400,84146000:CAPPA A PARETE CUBICA 1600x1200 ,94032080:VELETTA,84146000:CAPPA A PARETE PER FORNO CUBICA 2200X1600,94032080:VELETTA,84186900:CELLA BT,84186900:FRIGO LIBERA ISTALLAZIONE LTB1AE24W0,84198998:CUCINA LINEARE,85144000:4 FUOCHI A GAS KGS 6436 SX,84198998:CUCINA LINEARE,85144000:4 FUOCHI GAS KGS 6436 SX,84146000:CAPPA LFC316X,84146000:CAPPA LFC 316 X,85141980:FORNO ELETTRICO KOHH04X,84186900:FRIGO FREE LTB1AE24W0,84146000:CAPPA LFC 316 X,94032080:ARMADIO PULIZIA C/PORTE BATTENTI C/TETTO INCLINATO,84186900:COLD ROOM REFRIGERATION PLANTS - LEVEL B1,84186900:COLONNA IN ACCIAIO INOX H.600mm PER BILANCIA CODICE TX83I0,84186900:ARMADIO FRIGO PL 601 PTS,84385000:AFFETTATRICE VERTICALE 250MM,84186900:VETRINA REFRIGERATA 1200MM,84186900:IMPIANTO FRIGORIFEROREMOTO MISAERM MEP 7 + 15M TUBAZIONI,84186900:frigorifero 150lt stainless,94032080:ARMADIETTO SPOGLIATOIO A 3 POSTI CON TETTO INCLINATO.
        """
        # Assistant: Sure, I can help you find the correct CN code for your product. Based on the provided description, "Varningslampa Orange M Buzzer" might fall under the following category in the Combined Nomenclature:

        #     - **8531 10 30**: This code refers to "Electric sound or visual signaling apparatus (for example, bells, sirens, indicator panels, burglar or fire alarms), level indicators, pressure indicators, speed indicators, tachometers, and stroboscopes".

        #     This code is an example based on the provided description. For precise classification, it is always advisable to consult a customs expert or the specific applicable regulation. If you need further details or assistance, let me know!
        assistant_prompt = """
            User: I need a CN code for the following product with the description "Varningslampa Orange M Buzzer".
            8531 10 30
            User: I need a CN code for the following product with the description "TAVOLO DA LAVORO SENZA ALZATINA CON RIPIANO"
            94032080
            User: I need a CN code for the following product with the description "PIANO LAVORO 10/10 CON ALZATINA"
            94032080
        """

        cc = []
        for desc in data:
            messages = [{"role": "system", "content": system_prompt},
                        {"role": "user", "content": "I need a CN code for the following product with this description \"" + desc + "\""},
                        {"role": "assistant", "content": assistant_prompt}]

            response = self.client.chat.completions.create(
                model=self.cfg.translator_llm_deployment,
                messages=messages,
                temperature=0,
                top_p=0.1,
            )
            cc.append(str(response.choices[0].message.content))
        return cc