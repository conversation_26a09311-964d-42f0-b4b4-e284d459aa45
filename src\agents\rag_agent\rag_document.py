import asyncio
from argparse import Argument<PERSON><PERSON><PERSON>, Namespace
from typing import Iterator, <PERSON>, Tuple, Union
from enum import Enum

from langchain_core.documents.base import Document
from pydantic import BaseModel, Field
from langchain_openai import AzureOpenAI

from src.agents.rag_agent.llm_models.generator import AugmentedGenerator
from src.backend.contracts.chat_data import BotType
from src.agents.agent import Agent
from src.agents.answer import Answer

from src.agents.rag_agent.llm_models.metadata_extractor import MetadataExtractor
from src.agents.rag_agent.retriever import DocumentRetriever

from src.backend.contracts.chat_data import AgentName
from src.bots.conversation_session import ConversationSession
from src.common_tools.history.history import ConversationHistory
from src.agents.rag_agent.rag_utils import RAGUtils, check_metadata

from utils.clients import   AzureBlobClient
from utils.core import get_logger

from config.config import BlobStorageConfig

logger = get_logger(__file__)




class RAGDocumentAnswer(Answer):
    data: str = None
    source_file: str = ''
    confidence: float = 1.0
    explanation: str = ''
    metadata_gathered: dict = {}
    missing_metadata: list = []
    images: list = []
    status: str = ''
    agent_name: AgentName.RAG_DOCUMENT.value
    source_file_details: dict = {}

    def __init__(self, data: str) -> None:
        self.data = data

    def add_source_file(self, source_file: List[str]) -> None:
        self.source_file = source_file

    def add_explanation (self, explanation: str) -> None:
        self.explanation = explanation
        
    def add_metadata_gathered(self, metadata: dict) -> None:
        self.metadata_gathered = metadata
        
    def add_missing_metadata(self, missing_metadata: list) -> None:
        self.missing_metadata = missing_metadata
    
    def add_images(self, images: List[str]) -> None:
        self.images = images
    
    def add_status(self, status: str) -> None:
        self.status = status

    def add_source_file_details(self, source_file_details) -> None:
        self.source_file_details = source_file_details
        
    def to_json(self):
        return {
            "data": self.data,
            "source_file": self.source_file,
            "agent": self.agent_name,
            "confidence": 1.0,
            "explanation": self.explanation,
            "status": self.status,
            "missing_metadata": self.missing_metadata,
            "metadata_gathered": self.metadata_gathered,
            "source_file_details": self.source_file_details
        }
    
    
class rag_document(BaseModel):
    """"This tool allows users to download full documents. When the user requests a complete document or a programming file or an elechtrical schematic, it locates the specified file within a system and provides a direct download link. 
        It is best suited for cases where users need access to entire documents rather than just summaries or excerpts. DO NOT USE this tool if the query is searching information that could be
        inside a document. Typical queries are like that:

        - Can you provide me with the installation manual for the washing machine with serial number 456? Language: English, Edition: 10, Factory Model: WASH123.
        - Share the latest version of service manual for washer wh6-6
        - Can you give me the copy of the latest version of the installation manual for washer with pnc 9867910278 """
    
    query: str =  Field(description=" The question that requires a document")

class RAGDocument(Agent):
    def __init__(self, bot_name: str, name= AgentName.RAG.value, rag_llm= None, text_to_sql_llm=None, embedder_model= None, knowledge_base =None) -> None:
        """Initialize the  RAG agent that has two main attributes: the retriever and the generator.

        - The retreiver is responsible for searcing relevant chunk of texts in the Azure AI search index based on user question
        - The generator give in input the chunk retrieved to the LLM in order to get the answer of the user
        
        """
        super().__init__(name, bot_name)
        self.metadata_extractor = MetadataExtractor(rag_llm)
        self.retriever = DocumentRetriever(self.bot_name, knowledge_base)
        self.generator = AugmentedGenerator(self.bot_name, rag_llm)
        blob_config = BlobStorageConfig()
        self.storage_client = AzureBlobClient(blob_config, self.bot_name)
        self.rag_utils = RAGUtils(self.bot_name, self.retriever, self.generator, self.metadata_extractor, self.storage_client)
        

    async def retrieval_augmented_metadata(self, question: str, history: ConversationHistory = None, authorization: dict = None) -> Tuple[Union[Iterator[str], str], List[Document]]:
         return await self.rag_utils.retrieval_augmented_process(
            question=question, 
            agent_name=AgentName.RAG_DOCUMENT.name,
            history=history,
            use_rag=False,
            authorization = authorization
        )
    
    def ask(self, inquiry:str, history: ConversationHistory, conversation_session: ConversationSession = None, show_explanation:bool = False, authorization: dict = None) -> Tuple[RAGDocumentAnswer, str]:
        self.session = conversation_session
        
        self.session.checkpoint()         
        result = asyncio.run(
                self.retrieval_augmented_metadata(inquiry, history, authorization)
            )
    
        document_object = result["answer"]
        question_status = result["question_status"]

        self.session.checkpoint()
        
        if isinstance(document_object, tuple):
            document_object = document_object[0]
        
        rag_answer = self.build_rag_document_answer(document_object, show_explanation)

        rag_answer = check_metadata(rag_answer, document_object)   

        self.session.checkpoint()

        return rag_answer, question_status
                       

    def build_rag_document_answer(self, document_object: dict, show_explanation:bool = False) -> RAGDocumentAnswer:

        rag_answer = RAGDocumentAnswer(data=document_object["ANSWER"])
        rag_answer.add_source_file(document_object["DOCUMENT"])

        if "SOURCE_FILE_DETAILS" in document_object:
            rag_answer.add_source_file_details(document_object["SOURCE_FILE_DETAILS"])
            
        rag_answer.add_agent_name(self.agent_name)
        if show_explanation:
            rag_answer.add_explanation(document_object["EXPLANATION"])
        if "IMAGES" in document_object:
            unique_image_list = self.rag_utils._filter_duplicate_images(document_object)
            rag_answer.add_images(unique_image_list)

        return rag_answer


        
