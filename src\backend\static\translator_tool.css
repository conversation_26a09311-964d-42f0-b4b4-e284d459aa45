/* Translation Tool Styles */

/* ===================== ANIMATIONS ===================== */
@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(40px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes popIn {
  0% {
    opacity: 0;
    transform: scale(0.85);
  }
  80% {
    opacity: 1;
    transform: scale(1.05);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -400px 0;
  }
  100% {
    background-position: 400px 0;
  }
}

/* ===================== ANIMATION CLASSES ===================== */
.animate-fadeInUp {
  animation: fadeInUp 0.7s cubic-bezier(0.4,0,0.2,1) both;
}

.animate-fadeIn {
  animation: fadeIn 0.6s ease both;
}

.animate-popIn {
  animation: popIn 0.5s cubic-bezier(0.4,0,0.2,1) both;
}

.animate-shimmer {
  background: linear-gradient(90deg, #f8f9fa 25%, #e9ecef 50%, #f8f9fa 75%);
  background-size: 400px 100%;
  animation: shimmer 1.2s linear infinite;
}

/* ===================== APPLY ANIMATIONS TO ELEMENTS ===================== */

/* ===================== GLOBAL PAGE IMPROVEMENTS ===================== */

/* Enhanced main container */
.translation-container {
    animation: fadeInUp 0.7s cubic-bezier(0.4,0,0.2,1) both;
    min-height: 100vh;
    background: rgba(248, 251, 255, 0.3);
    padding: 2rem 0;
}

/* Enhanced main card with better shadow and spacing */
.translation-card {
    animation: popIn 0.5s cubic-bezier(0.4,0,0.2,1) both;
    background: #ffffff;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(36, 99, 159, 0.08);
    border: 1px solid rgba(36, 99, 159, 0.06);
    overflow: hidden;
    margin: 1rem;
}

/* Enhanced card header */
.card-header-custom {
    animation: fadeIn 0.8s 0.1s cubic-bezier(0.4,0,0.2,1) both;
    background: rgba(255, 255, 255, 0.95);
    border-bottom: 1px solid rgba(36, 99, 159, 0.08);
    padding: 3rem 2rem 2rem 2rem;
}

.card-header-custom h1 {
    color: var(--corporate-primary);
    font-weight: 800;
    letter-spacing: -0.02em;
    text-shadow: 0 2px 4px rgba(36, 99, 159, 0.1);
}

.card-header-custom p {
    color: #6c757d;
    font-size: 1.1rem;
    font-weight: 400;
}

/* Upload area: fade in from below, slightly delayed */
.upload-area {
    animation: fadeInUp 0.8s 0.15s cubic-bezier(0.4,0,0.2,1) both;
}

/* File info and loading spinner: fade in for smoothness */
.file-info {
    animation: fadeIn 0.7s 0.2s cubic-bezier(0.4,0,0.2,1) both;
}
.loading-spinner {
    animation: fadeIn 0.7s 0.2s cubic-bezier(0.4,0,0.2,1) both;
    display: none;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.loading-spinner.show {
    display: flex;
}

/* Language grid, excel options: fade in from below */
.language-grid, .excel-options {
    animation: fadeInUp 0.7s 0.2s cubic-bezier(0.4,0,0.2,1) both;
}

/* Buttons: pop in for interactivity */
.download-link-custom, .translate-link-custom, .remove-file-btn, #toggleAllColumns, .select-all-btn {
    animation: popIn 0.5s cubic-bezier(0.4,0,0.2,1) both;
}



/* Spinner: spin and fade in */
.loading-spinner .spinner {
    animation: spin 1s linear infinite, fadeIn 0.5s both;
}

/* Shimmer effect for loading states */
.loading-spinner.animate-shimmer {
    animation: shimmer 1.2s linear infinite, fadeIn 0.5s both;
}

/* Select2 dropdown: fade in for multi-select */
.select2-container--default .select2-selection--multiple {
    animation: fadeIn 0.5s cubic-bezier(0.4,0,0.2,1) both;
}

/* Excel options header: fade in for section header */
.excel-options-header {
    animation: fadeIn 0.6s cubic-bezier(0.4,0,0.2,1) both;
}

/* Base font family for all elements */
body, html, .translation-container, .card, .form-control, .btn {
    font-family: "Segoe UI", -apple-system, BlinkMacSystemFont, "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
}

:root{
    /* New color palette from design specs */
    --corporate-primary: #1E1F41;
    --corporate-secondary: #7B869C;
    --corporate-light: #DFE7EA;
    --golden-primary: #7D653F;
    --golden-secondary: #9E8664;
    --golden-light: #BFAF8F;
    --warm-grey-primary: #85827A;
    --warm-grey-secondary: #BFBAB0;
    --warm-grey-light: #DFD5D2;
    --deep-teal-primary: #24639F;
    --deep-teal-secondary: #526D70;
    --deep-teal-light: #BCD4D2;
    --sustainability-primary: #6E826F;
    --sustainability-secondary: #A8B580;
    --sustainability-light: #C4D69A;
}

/* Translation Tool Styles */
.translation-container {
    background: transparent;
    min-height: calc(100vh - 140px);
    padding: 2rem 0;
}

.translation-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(30, 31, 65, 0.1);
    border: 1px solid rgba(30, 31, 65, 0.08);
    overflow: hidden;
}

.card-header-custom {
    background: var(--corporate-primary);
    color: white;
    font-weight: 700;
    padding: 2rem;
    border: none;
    position: relative;
    overflow: hidden;
}

.header-icon-topright {
    position: absolute;
    top: 1.5rem;
    right: 2rem;
    font-size: 5rem;
    color: #fff;
    background: rgba(30,31,65,0.12);
    border-radius: 12px;
    padding: 0.5rem 0.7rem;
    box-shadow: 0 2px 8px rgba(30,31,65,0.08);
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
}

@media (max-width: 768px) {
    .header-icon-topright {
        top: 1rem;
        right: 1rem;
        font-size: 5rem;
        padding: 0.35rem 0.5rem;
    }
}

/* ===================== ENHANCED FILE UPLOAD AREA ===================== */

.upload-area {
    border: 3px dashed var(--corporate-secondary);
    border-radius: 16px;
    padding: 4rem 2rem;
    text-align: center;
    background: rgba(240, 248, 255, 0.5);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    animation: fadeInUp 0.8s 0.15s cubic-bezier(0.4,0,0.2,1) both;
}

.upload-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: rgba(36, 99, 159, 0.05);
    transition: left 0.5s ease;
    z-index: 0;
}

.upload-area:hover::before {
    left: 100%;
}

.upload-area:hover {
    border-color: var(--deep-teal-primary);
    background: rgba(240, 248, 255, 0.8);
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(36, 99, 159, 0.15);
}

.upload-area.dragover {
    border-color: var(--sustainability-primary);
    background: rgba(240, 248, 255, 0.9);
    transform: scale(1.02);
    box-shadow: 0 12px 40px rgba(36, 99, 159, 0.2);
}

.upload-icon {
    font-size: 4rem;
    color: var(--deep-teal-primary);
    margin-bottom: 1.5rem;
    position: relative;
    z-index: 1;
    transition: all 0.3s ease;
}

.upload-area:hover .upload-icon {
    transform: scale(1.1);
    color: var(--corporate-primary);
}

.upload-area h6 {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--corporate-primary);
    margin-bottom: 0.75rem;
    position: relative;
    z-index: 1;
}

.upload-area p {
    position: relative;
    z-index: 1;
    font-weight: 500;
}

.upload-area .small {
    font-size: 0.9rem;
    color: #6c757d;
}

/* Enhanced file info section */
.file-info {
    background: #ffffff;
    border: 2px solid rgba(36, 99, 159, 0.1);
    border-radius: 12px;
    padding: 1.5rem;
    margin-top: 1.5rem;
    box-shadow: 0 4px 20px rgba(36, 99, 159, 0.08);
    animation: fadeIn 0.7s 0.2s cubic-bezier(0.4,0,0.2,1) both;
    transition: all 0.3s ease;
}

.file-info:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 24px rgba(36, 99, 159, 0.12);
}

.file-info .fas {
    color: var(--corporate-primary);
    font-size: 1.5rem;
}

.file-info .fw-bold {
    color: var(--corporate-primary);
    font-size: 1.1rem;
    font-weight: 700;
}

.file-info .text-muted {
    color: #6c757d;
    font-weight: 500;
}

/* Enhanced form elements */
.form-control, .form-select {
    border: 2px solid rgba(36, 99, 159, 0.15);
    border-radius: 10px;
    padding: 0.875rem 1rem;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: #ffffff;
    box-shadow: 0 2px 8px rgba(36, 99, 159, 0.06);
}

.form-control:focus, .form-select:focus {
    border-color: var(--deep-teal-primary);
    box-shadow: 0 0 0 4px rgba(36, 99, 159, 0.12);
    transform: translateY(-1px);
    outline: none;
}

.form-control:hover, .form-select:hover {
    border-color: var(--corporate-primary);
    transform: translateY(-1px);
}

/* Enhanced labels */
.form-label {
    font-weight: 700;
    color: var(--corporate-primary);
    margin-bottom: 0.75rem;
    font-size: 1rem;
    letter-spacing: 0.02em;
}

/* ===================== ENHANCED LANGUAGE SELECTORS ===================== */

/* Enhanced language grid container */
.language-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin: 2rem 0;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 16px;
    border: 1px solid rgba(36, 99, 159, 0.1);
    box-shadow: 0 4px 20px rgba(36, 99, 159, 0.05);
}

/* Enhanced form labels */
.language-grid .form-label {
    color: var(--corporate-primary);
    font-weight: 700;
    font-size: 1rem;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    letter-spacing: 0.02em;
}

.language-grid .form-label::before {
    content: '';
    width: 4px;
    height: 20px;
    background: var(--corporate-primary);
    border-radius: 2px;
    margin-right: 0.75rem;
}

/* Enhanced source language selector */
#sourceLanguage, .form-select-custom {
    background: transparent;
    border: 2px solid rgba(36, 99, 159, 0.2);
    border-radius: 12px;
    padding: 1rem 1.25rem;
    font-size: 1rem;
    font-weight: 500;
    color: var(--corporate-primary);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 12px rgba(36, 99, 159, 0.08);
    min-height: 56px;
    height: 56px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    cursor: pointer;
    position: relative;
    min-height: 56px;
}

#sourceLanguage:focus, .form-select-custom:focus {
    outline: none;
    border-color: var(--deep-teal-primary);
    box-shadow: 0 0 0 4px rgba(36, 99, 159, 0.15);
    transform: translateY(-1px);
}

#sourceLanguage:hover, .form-select-custom:hover {
    border-color: var(--deep-teal-primary);
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(36, 99, 159, 0.12);
}

/* Enhanced target language selector with Select2 styling - match source selector exactly */
/* COMPLETE SELECT2 OVERRIDE - Load after Select2 default styles */
/* Reset and override all Select2 placeholder styling */
.select2-container--default .select2-selection--multiple {
    min-height: 56px !important;
    max-height: none !important;
    border: 2px solid rgba(36, 99, 159, 0.2) !important;
    border-radius: 12px !important;
    padding: 0.75rem 1.25rem !important;
    font-size: 1rem;
    background: transparent !important;
    box-shadow: 0 2px 12px rgba(36, 99, 159, 0.08) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Override Select2's default placeholder color completely */
.select2-container--default .select2-selection--multiple .select2-selection__rendered {
    color: #24639f !important;
    font-weight: 500 !important;
    font-size: 1rem !important;
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
    line-height: 1.5 !important;
    display: block !important;
    width: 100% !important;
    overflow: visible !important;
    white-space: normal !important;
}

/* Target the specific placeholder element that Select2 creates */
.select2-container--default .select2-selection--multiple .select2-selection__rendered li {
    color: #24639f !important;
    font-weight: 500 !important;
}

/* Override default Select2 placeholder with higher specificity */
.select2-container--default .select2-selection--multiple .select2-selection__rendered .select2-selection__placeholder {
    color: #24639f !important;
    opacity: 1 !important;
    font-weight: 500 !important;
    white-space: normal !important;
    overflow: visible !important;
    width: 100% !important;
    text-overflow: clip !important;
}

/* Additional overrides for any Select2 generated elements */
#targetLanguage + .select2-container--default .select2-selection--multiple .select2-selection__rendered * {
    color: #24639f !important;
}

.select2-container--default .select2-selection--multiple:focus-within,
.select2-container--default.select2-container--focus .select2-selection--multiple {
    border-color: var(--deep-teal-primary) !important;
    box-shadow: 0 0 0 4px rgba(36, 99, 159, 0.15) !important;
    transform: translateY(-1px);
}

.select2-container--default:hover .select2-selection--multiple {
    border-color: var(--deep-teal-primary) !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(36, 99, 159, 0.12) !important;
}

/* Enhanced Select2 choice styling */
.select2-container--default .select2-selection--multiple .select2-selection__choice {
    background: var(--corporate-primary);
    border: none;
    border-radius: 8px;
    color: white;
    font-weight: 600;
    font-size: 0.9rem;
    padding: 0.4rem 0.75rem;
    margin: 0 !important;
    box-shadow: 0 2px 8px rgba(36, 99, 159, 0.2);
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    height: 28px;
    gap: 0.5rem;
    flex-direction: row-reverse;
    flex-shrink: 0;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice:hover {
    background: var(--deep-teal-primary);
    transform: translateY(-1px);
    box-shadow: 0 3px 12px rgba(36, 99, 159, 0.3);
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1rem;
    margin-right: 0.5rem;
    margin-left: 0;
    border: none;
    background: none;
    transition: color 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;
    line-height: 1;
    order: -1;
    float: none;
    position: relative;
    top: 0;
    left: 0;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
    color: white;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
}

/* Ensure proper spacing in selection area */
.select2-container--default .select2-selection--multiple .select2-selection__rendered {
    padding: 0 !important;
    margin: 0 !important;
    min-height: 24px;
    display: flex !important;
    align-items: flex-start !important;
    flex-wrap: wrap !important;
    gap: 0.25rem !important;
    width: 100%;
    overflow: visible !important;
}

/* Fix placeholder and search field color */
.select2-container--default .select2-selection--multiple .select2-selection__placeholder {
    color: var(--corporate-primary) !important;
    font-weight: 500 !important;
    font-size: 1rem !important;
    line-height: 1.5 !important;
    margin: 0 !important;
    opacity: 1 !important;
}

/* Additional override for Select2 placeholder */
.select2-container .select2-selection--multiple .select2-selection__placeholder {
    color: var(--corporate-primary) !important;
}

/* Override any default Select2 placeholder styling */
#targetLanguage + .select2-container .select2-selection__placeholder {
    color: var(--corporate-primary) !important;
}

/* Force placeholder color for all Select2 elements */
.select2-container--default .select2-selection__placeholder,
.select2-container .select2-selection__placeholder,
.select2-selection__placeholder,
#targetLanguage + .select2-container .select2-selection--multiple .select2-selection__placeholder,
.select2-container--default .select2-selection--multiple .select2-selection__placeholder,
.select2-container--default .select2-selection--single .select2-selection__placeholder {
    color: var(--corporate-primary) !important;
    opacity: 1 !important;
    font-weight: 500 !important;
}

/* Target the specific rendered placeholder text */
.select2-container .select2-selection .select2-selection__rendered {
    color: var(--corporate-primary) !important;
}

.select2-container--default .select2-search--inline .select2-search__field {
    border: none;
    outline: none;
    font-size: 1rem;
    font-weight: 500;
    margin: 0;
    min-width: 150px;
    color: var(--corporate-primary) !important;
    background: transparent !important;
}

/* Enhanced Select2 dropdown */
.select2-container--default .select2-dropdown {
    border: 2px solid rgba(36, 99, 159, 0.2);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(36, 99, 159, 0.15);
    background: #ffffff;
    margin-top: 4px;
}

.select2-container--default .select2-results__option {
    padding: 0.75rem 1rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background: rgba(36, 99, 159, 0.1);
    color: var(--corporate-primary);
}

.select2-container--default .select2-results__option[aria-selected="true"] {
    background: var(--corporate-primary);
    color: white;
}

/* Enhanced placeholder styling - match source selector */

/* Force all text in Select2 to use corporate color */
.select2-container--default .select2-selection--multiple .select2-selection__rendered {
    color: var(--corporate-primary) !important;
}

/* Override any default text colors that might affect placeholder */
#targetLanguage + .select2-container * {
    color: var(--corporate-primary) !important;
}

/* AGGRESSIVE PLACEHOLDER FIX - Force dark text */
.select2-container .select2-selection .select2-selection__rendered,
.select2-container .select2-selection .select2-selection__rendered *,
.select2-container--default .select2-selection--multiple .select2-selection__rendered,
.select2-container--default .select2-selection--multiple .select2-selection__rendered *,
span.select2-selection__rendered,
li.select2-selection__choice,
.select2-selection__placeholder,
.select2-container .select2-selection__placeholder,
.select2-container--default .select2-selection__placeholder {
    color: #24639f !important;
    opacity: 1 !important;
}

/* Target by attribute and content */
[title="Select target language(s)"] {
    color: #24639f !important;
}

/* Use hex color instead of CSS variable for maximum compatibility */
.select2-container--default .select2-selection--multiple .select2-selection__placeholder {
    color: #24639f !important;
    opacity: 1 !important;
    font-weight: 500 !important;
}


/* Enhanced excel options container */
.excel-options {
    background: #ffffff;
    border: 2px solid rgba(36, 99, 159, 0.1);
    border-radius: 16px;
    padding: 2rem;
    margin-top: 2rem;
    box-shadow: 0 6px 24px rgba(36, 99, 159, 0.08);
    animation: fadeInUp 0.7s 0.2s cubic-bezier(0.4,0,0.2,1) both;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.excel-options::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--corporate-primary);
}

.excel-options:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 32px rgba(36, 99, 159, 0.12);
}

/* Enhanced excel options header */
.excel-options-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid rgba(36, 99, 159, 0.1);
}

.excel-options-header h5 {
    margin: 0;
    color: var(--corporate-primary);
    font-weight: 700;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    letter-spacing: 0.02em;
}

.excel-options-header h5 i {
    color: var(--deep-teal-primary);
    margin-right: 0.75rem;
    font-size: 1.4rem;
}

/* Enhanced checkboxes */
.form-check-label {
    white-space: normal !important;
    word-break: break-word;
    overflow-wrap: anywhere;
    font-size: 0.95rem;
    font-weight: 500;
    color: #495057;
    cursor: pointer;
    transition: color 0.2s ease;
}

.form-check-label:hover {
    color: var(--corporate-primary);
}

.column-checkbox {
    margin: 0.75rem 0;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.column-checkbox:hover {
    background: rgba(36, 99, 159, 0.05);
}

.column-checkbox input[type="checkbox"] {
    margin-right: 0.75rem;
    transform: scale(1.3);
    accent-color: var(--corporate-primary);
    cursor: pointer;
}

/* Enhanced loading spinner */
.loading-spinner {
    background: transparent;
    border: none;
    border-radius: 12px;
    padding: 2rem;
    margin-top: 1.5rem;
    text-align: center;
    animation: fadeIn 0.7s 0.2s cubic-bezier(0.4,0,0.2,1) both;
    display: none;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
}

.loading-spinner.show {
    display: flex;
}

.loading-spinner .spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(36, 99, 159, 0.2);
    border-top: 4px solid var(--corporate-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
    display: block;
}

.loading-text {
    color: var(--corporate-primary);
    font-weight: 600;
    font-size: 1.1rem;
}




.status-text {
    color: var(--corporate-primary);
    font-weight: 500;
    margin-top: 0.5rem;
}

/* Version display styles */
#runningEnvDisplay {
    font-size: 0.75rem;
    font-weight: bold;
    letter-spacing: 0.5px;
}

#runningEnvDisplay.bg-secondary {
    background-color: var(--epr-spartan-blue) !important;
}

#versionDisplay {
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.8) !important;
    transition: color 0.2s ease;
}

#versionDisplay:hover {
    color: white !important;
    text-decoration: underline !important;
}

/* Environment-specific colors */
#runningEnvDisplay[data-env="LOCAL"] {
    background-color: var(--epr-green) !important;
}

#runningEnvDisplay[data-env="DEVEL"] {
    background-color: var(--epr-gold) !important;
    color: var(--epr-blue) !important;
}

#runningEnvDisplay[data-env="STAGING"] {
    background-color: var(--epr-danger) !important;
}

#runningEnvDisplay[data-env=""] {
    background-color: var(--epr-blue) !important;
}

/* Translator-specific styles */
.translation-container #runningEnvDisplay {
    position: fixed;
    top: 10px;
    right: 10px;
    z-index: 1050;
}

/* ===================== ENHANCED RESPONSIVE DESIGN ===================== */

@media (max-width: 768px) {
    .language-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        padding: 1rem;
    }

    .translation-container {
        padding: 1rem 0;
    }

    .translation-card {
        margin: 0.5rem;
        border-radius: 16px;
    }

    .card-header-custom {
        padding: 2rem 1.5rem 1.5rem 1.5rem;
    }

    .card-header-custom h1 {
        font-size: 2rem !important;
    }

    .upload-area {
        padding: 2.5rem 1rem;
        border-radius: 12px;
    }

    .upload-icon {
        font-size: 3rem;
    }

    .excel-options {
        padding: 1.5rem;
        margin-top: 1.5rem;
    }

    .excel-options-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .excel-options-header h5 {
        font-size: 1.1rem;
    }

    #toggleAllColumns {
        align-self: flex-start;
        min-width: 120px;
        padding: 0.6rem 1.2rem;
        font-size: 0.85rem;
    }

    .translate-link-custom {
        padding: 0.875rem 2rem;
        font-size: 1rem;
    }

    .select2-container--default .select2-selection--multiple {
        min-height: 48px !important;
        max-height: 48px !important;
        border-radius: 10px !important;
        padding: 0.75rem 1rem !important;
    }

    #sourceLanguage, .form-select-custom {
        min-height: 48px;
        border-radius: 10px;
        padding: 0.75rem 1rem;
    }

    .form-control, .form-select {
        padding: 0.75rem 0.875rem;
        border-radius: 8px;
    }
}

@media (max-width: 576px) {
    .translation-card {
        margin: 0.25rem;
    }

    .card-header-custom {
        padding: 1.5rem 1rem;
    }

    .card-header-custom h1 {
        font-size: 1.75rem !important;
    }

    .language-grid {
        padding: 0.75rem;
    }

    .excel-options {
        padding: 1rem;
    }

    .upload-area {
        padding: 2rem 0.75rem;
    }

    .translate-link-custom {
        padding: 0.75rem 1.5rem;
        font-size: 0.95rem;
        width: 100%;
        justify-content: center;
    }
}

/* Toggle All Columns Button Styles */
#toggleAllColumns {
    transition: all 0.3s ease;
    border: 2px solid var(--warm-grey-secondary, #BFBAB0);
    color: black;
    background-color:  whitesmoke;
    border-radius: 8px;
    font-weight: 500;
    padding: 8px 16px;
    font-size: 0.9rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    min-width: 130px;
}

#toggleAllColumns:hover {
    background-color: var(--corporate-light);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

#toggleAllColumns:active {
    transform: translateY(0px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#toggleAllColumns i {
    transition: transform 0.2s ease;
    font-size: 1rem;
}

#toggleAllColumns:hover i {
    transform: scale(1.1);
}

/* Excel Options Header */
.excel-options-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.excel-options-header h5 {
    margin: 0;
    flex-grow: 1;
}





#progressPercent {
    font-size: 1rem;
    font-weight: 700;
    color: var(--corporate-primary, #1E1F41);
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    padding: 0.25rem 0.75rem;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
    min-width: 60px;
    text-align: center;
}

/* Loading Spinner Styles */
.loading-spinner {
    display: none;
    text-align: center;
    padding: 20px;
    background-color: transparent;
    border: none;
    border-radius: 8px;
    margin-top: 15px;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 150px;
}

.loading-spinner.show {
    display: flex;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(36, 99, 159, 0.2);
    border-top: 4px solid var(--corporate-primary, #1E1F41);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
    display: block;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: #6c757d;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Download Link Custom Styles */
.download-link-custom {
    background: var(--corporate-primary, #1E1F41);
    color: #fff;
    border: none;
    border-radius: 8px;
    font-size: 1.15rem;
    font-weight: 600;
    padding: 0.85rem 2.5rem;
    box-shadow: 0 2px 8px rgba(30, 31, 65, 0.10);
    transition: background 0.2s, transform 0.15s, box-shadow 0.15s;
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    margin: 0 auto;
    letter-spacing: 0.01em;
}

.download-link-custom:hover, .download-link-custom:focus {
    background: #23244a;
    color: #fff;
    transform: translateY(-2px) scale(1.03);
    box-shadow: 0 4px 16px rgba(30, 31, 65, 0.16);
    text-decoration: none;
}

.download-link-custom i {
    font-size: 1.3rem;
}

.download-link-wrapper {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 2rem;
}

/* Start Translation Button Custom Style (Enhanced) */
.translate-link-custom {
    background: var(--corporate-primary);
    color: #fff;
    border: none;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 700;
    padding: 1rem 2.5rem;
    box-shadow: 0 6px 20px rgba(36, 99, 159, 0.25);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    margin: 0 auto;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    opacity: 1;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.translate-link-custom::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.translate-link-custom:hover::before {
    width: 1000px;
    height: 300px;
}

.translate-link-custom:hover, .translate-link-custom:focus {
    background: var(--deep-teal-primary);
    color: #fff;
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 8px 32px rgba(36, 99, 159, 0.3);
    text-decoration: none;
}

.translate-link-custom:disabled,
.translate-link-custom[disabled] {
    background: var(--warm-grey-primary);
    color: #e0e0e0;
    opacity: 0.7;
    cursor: not-allowed;
    box-shadow: none;
    transform: none;
}

.translate-link-custom i {
    font-size: 1.4rem;
    position: relative;
    z-index: 1;
}


/* Custom Select All Button Styles */
.select-all-btn {
    border-radius: 1.5rem;
    font-size: 1rem;
    transition: background 0.2s, box-shadow 0.2s;
    box-shadow: 0 2px 8px rgba(0,0,0,0.07);
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    background: var(--warm-grey-secondary);
    border: none;
}
.select-all-btn:hover, .select-all-btn:focus {
    background: var(--warm-grey-primary);
    box-shadow: 0 4px 16px rgba(37,99,235,0.15);
}

/* Remove File Button Warm Gray Style */
.remove-file-btn {
    background: var(--corporate-light);
    color: var(--corporate-primary);
    border: 1px solid var(--warm-grey-primary);
    border-radius: 6px;
    font-size: 0.95rem;
    font-weight: 500;
    padding: 0.4rem 0.8rem;
    margin-left: 1rem;
    transition: background 0.2s, color 0.2s, box-shadow 0.2s;
    box-shadow: 0 2px 6px rgba(133, 130, 122, 0.08);
    cursor: pointer;
}

.remove-file-btn:hover, .remove-file-btn:focus {
    background: var(--warm-grey-primary);
    color: #fff;
    box-shadow: 0 4px 12px rgba(133, 130, 122, 0.15);
    text-decoration: none;
}

/* ===================== PROGRESS BAR STYLES ===================== */
#progressContainer {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  animation: fadeInUp 0.5s ease-out;
}

#progressContainer .progress {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  overflow: hidden;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

#progressBar {
  background: linear-gradient(90deg, var(--deep-teal-primary), #20b2aa) !important;
  transition: width 0.3s ease-in-out;
  position: relative;
  overflow: hidden;
}

#progressBar::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

#progressText {
  font-weight: 600;
  color: var(--deep-teal-primary);
}

#progressDetails {
  font-style: italic;
  color: #666;
  min-height: 1.2em;
}

/* ===================== PREVIEW SECTION STYLES ===================== */

/* Enhanced preview section with better styling */
#previewSection {
  background: rgba(240, 248, 255, 0.8);
  border: 1px solid rgba(36, 99, 159, 0.15);
  border-radius: 16px;
  padding: 2rem;
  margin-top: 2rem;
  box-shadow: 0 8px 32px rgba(36, 99, 159, 0.08);
  backdrop-filter: blur(10px);
  animation: fadeInUp 0.6s ease-out;
}

#previewSection .excel-options-header {
  background: var(--corporate-primary);
  color: white;
  padding: 1rem 1.5rem;
  margin: -2rem -2rem 1.5rem -2rem;
  border-radius: 16px 16px 0 0;
  box-shadow: 0 4px 16px rgba(36, 99, 159, 0.2);
}

#previewSection .excel-options-header h5 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
}

#previewSection .excel-options-header i {
  font-size: 1.5rem;
  margin-right: 0.75rem;
  opacity: 0.9;
}

.header-decoration {
  position: absolute;
  top: 50%;
  right: 2rem;
  transform: translateY(-50%);
  width: 40px;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
}

#previewDescription {
  font-size: 1rem;
  color: #495057;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 10px;
  border-left: 4px solid var(--corporate-primary);
}

/* Enhanced table container */
.preview-table-container {
  max-height: 500px;
  overflow-y: auto;
  border-radius: 12px;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.12);
  margin: 1.5rem 0;
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.08);
}

.preview-table-container table {
  margin-bottom: 0;
  border-radius: 12px;
  overflow: hidden;
}

.preview-table-container tbody tr {
  transition: all 0.2s ease;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.preview-table-container tbody tr:hover {
  background: rgba(36, 99, 159, 0.08);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(36, 99, 159, 0.1);
}

.preview-table-container tbody td {
  padding: 1rem 0.875rem;
  font-size: 0.9rem;
  vertical-align: top;
  word-break: break-word;
  max-width: 300px;
  border-right: 1px solid rgba(0, 0, 0, 0.08);
  line-height: 1.5;
}

.preview-table-container tbody td:last-child {
  border-right: none;
}

.preview-table-container tbody td:first-child {
  font-weight: 600;
  color: var(--corporate-primary);
  background: rgba(36, 99, 159, 0.03);
}

.preview-table-container tbody td:nth-child(2) {
  font-weight: 500;
  color: var(--deep-teal-primary);
}

.preview-table-container tbody td:nth-child(3) {
  color: #495057;
  font-style: italic;
}

.preview-table-container tbody td:nth-child(n+4) {
  color: var(--sustainability-primary);
  font-weight: 500;
  background: rgba(110, 130, 111, 0.02);
}

/* Enhanced table header */
.preview-table-container .table-dark th {
  background: var(--corporate-primary);
  border-color: transparent;
  color: white;
  font-weight: 600;
  font-size: 0.95rem;
  padding: 1rem 0.875rem;
  border-right: 1px solid rgba(255, 255, 255, 0.15);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  position: sticky;
  top: 0;
  z-index: 10;
}

.preview-table-container .table-dark th:last-child {
  border-right: none;
}

/* Enhanced column separator styling */
.preview-table-container .column-separator td {
  background: #f8f9fa !important;
  border-top: 2px solid var(--corporate-secondary) !important;
  border-right: none !important;
  font-weight: 600;
  color: var(--corporate-primary);
  padding: 0.875rem;
  text-align: center;
  font-size: 0.85rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.preview-table-container .column-separator:hover td {
  background: #f8f9fa !important;
}

.preview-info {
  background: rgba(110, 130, 111, 0.1);
  border: 1px solid var(--sustainability-secondary);
  border-radius: 12px;
  padding: 1rem 1.25rem;
  margin: 1rem 0;
  box-shadow: 0 2px 8px rgba(110, 130, 111, 0.1);
}

/* Enhanced preview actions */
.preview-actions {
  background: rgba(255, 255, 255, 0.5);
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 0 0 12px 12px;
  padding: 2rem;
  margin: 1.5rem -2rem -2rem -2rem;
  text-align: center;
}

#newPreviewBtn {
  background: var(--warm-grey-primary);
  border: none;
  color: white;
  font-weight: 600;
  padding: 0.75rem 2rem;
  border-radius: 10px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(133, 130, 122, 0.2);
  margin-right: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 0.85rem;
}

#newPreviewBtn:hover {
  background: #8a857a;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(133, 130, 122, 0.3);
}

#continueTranslationBtn {
  background: var(--sustainability-primary);
  border: none;
  color: white;
  font-weight: 700;
  padding: 0.875rem 2.5rem;
  border-radius: 10px;
  transition: all 0.3s ease;
  box-shadow: 0 6px 20px rgba(110, 130, 111, 0.25);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 0.9rem;
}

#continueTranslationBtn:hover {
  background: #5a7a5b;
  transform: translateY(-3px);
  box-shadow: 0 8px 28px rgba(110, 130, 111, 0.35);
}

#previewBtn {
  background: var(--golden-primary);
  border: none;
  color: white;
  font-weight: 600;
  padding: 0.75rem 2rem;
  border-radius: 10px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(125, 101, 63, 0.2);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 0.85rem;
}

#previewBtn:hover {
  background: var(--golden-secondary);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(125, 101, 63, 0.3);
}

#previewBtn:disabled {
  background: var(--warm-grey-secondary);
  color: #999;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* ===================== RESPONSIVE DESIGN ===================== */
@media (max-width: 768px) {
  .translation-card {
    margin: 1rem;
    border-radius: 12px;
  }

  .card-header-custom h1 {
    font-size: 2rem !important;
  }

  .language-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .column-checkbox {
    margin-bottom: 0.5rem;
  }

  .excel-options-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .select-all-btn {
    align-self: flex-start;
  }

  #progressContainer {
    padding: 1rem;
  }

  .preview-actions {
    flex-direction: column;
    gap: 1rem;
  }

  .preview-actions .btn {
    width: 100%;
    margin: 0 !important;
  }

  .preview-table-container tbody td {
    max-width: 200px;
    font-size: 0.8rem;
  }

  /* Word preview responsive adjustments */
  .word-preview-content {
    max-height: 400px;
    padding: 0.75rem;
  }

  .word-preview-loading {
    padding: 2rem;
  }
}

/* ===================== ENHANCED WORD/POWERPOINT/PDF DOCUMENT PREVIEW ===================== */

/* Main container with premium styling */
.word-preview-container {
  background: #ffffff;
  border: 1px solid rgba(36, 99, 159, 0.15);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  margin-top: 1.5rem;
  backdrop-filter: blur(10px);
  animation: fadeInUp 0.6s ease-out;
}

/* Language selector with enhanced styling */
.word-preview-container .mt-3 {
  background: rgba(36, 99, 159, 0.05);
  padding: 1.5rem;
  margin: 0 !important;
  position: relative;
}

.language-selector-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--corporate-primary);
}

.word-preview-container .form-label {
  color: var(--corporate-primary);
  font-weight: 600;
  margin-bottom: 0.5rem;
  display: inline-block;
}

#wordPreviewLanguageSelect {
    min-width: 200px;
    border: 2px solid rgba(36, 99, 159, 0.2);
    border-radius: 10px;
    padding: 0.75rem 1rem;
    font-size: 0.95rem;
    font-weight: 600;
    background: white;
    color: var(--corporate-primary);
    box-shadow: 0 2px 8px rgba(36, 99, 159, 0.1);
    transition: all 0.3s ease;
    margin-left: 1rem;
    cursor: pointer;
}

#wordPreviewLanguageSelect:focus {
    border-color: var(--deep-teal-primary);
    box-shadow: 0 0 0 3px rgba(36, 99, 159, 0.15);
    outline: none;
    transform: translateY(-1px);
}

#wordPreviewLanguageSelect:hover {
    border-color: var(--deep-teal-primary);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(36, 99, 159, 0.15);
}/* Enhanced loading state */
.word-preview-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  background: rgba(240, 248, 255, 0.8);
  color: var(--corporate-primary);
}

.word-preview-loading .spinner-border {
  width: 3rem;
  height: 3rem;
  border-width: 0.3rem;
  border-color: var(--corporate-primary);
  border-right-color: transparent;
  margin-bottom: 1.5rem;
  animation: spin 1s linear infinite;
}

.word-preview-loading p {
  font-size: 1.1rem;
  font-weight: 500;
  margin: 0;
  text-align: center;
  color: var(--corporate-primary);
}

/* Enhanced preview content */
.word-preview-content {
  max-height: 700px;
  overflow-y: auto;
  padding: 2rem;
  background: #ffffff;
  position: relative;
}

/* Premium document styling */
.word-preview-content .docx-preview {
  font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
  line-height: 1.7;
  color: #2c3e50;
  font-size: 1rem;
  max-width: 100%;
  margin: 0 auto;
}

.word-preview-content .docx-preview p {
  margin-bottom: 1.2rem;
  text-align: justify;
  text-justify: inter-word;
}

.word-preview-content .docx-preview h1,
.word-preview-content .docx-preview h2,
.word-preview-content .docx-preview h3,
.word-preview-content .docx-preview h4,
.word-preview-content .docx-preview h5,
.word-preview-content .docx-preview h6 {
  color: var(--corporate-primary);
  margin-top: 2rem;
  margin-bottom: 1rem;
  font-weight: 700;
  line-height: 1.3;
}

.word-preview-content .docx-preview h1 {
  font-size: 2rem;
  border-bottom: 3px solid var(--corporate-primary);
  padding-bottom: 0.5rem;
}

.word-preview-content .docx-preview h2 {
  font-size: 1.6rem;
  color: var(--deep-teal-primary);
}

.word-preview-content .docx-preview h3 {
  font-size: 1.3rem;
  color: var(--sustainability-primary);
}

/* Enhanced table styling */
.word-preview-content .docx-preview table {
  border-collapse: collapse;
  width: 100%;
  margin: 2rem 0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.word-preview-content .docx-preview table td,
.word-preview-content .docx-preview table th {
  border: 1px solid rgba(0, 0, 0, 0.1);
  padding: 1rem;
  text-align: left;
  transition: background-color 0.2s ease;
}

.word-preview-content .docx-preview table th {
  background: var(--corporate-primary);
  color: white;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 0.9rem;
}

.word-preview-content .docx-preview table tbody tr:hover {
  background-color: rgba(36, 99, 159, 0.05);
}

.word-preview-content .docx-preview table tbody tr:nth-child(even) {
  background-color: rgba(240, 248, 255, 0.3);
}

/* Enhanced list styling */
.word-preview-content .docx-preview ul,
.word-preview-content .docx-preview ol {
  margin: 1rem 0;
  padding-left: 2rem;
}

.word-preview-content .docx-preview li {
  margin-bottom: 0.5rem;
  line-height: 1.6;
}

/* Code and special text styling */
.word-preview-content .docx-preview code {
  background: rgba(36, 99, 159, 0.1);
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 0.9rem;
}

.word-preview-content .docx-preview blockquote {
  border-left: 4px solid var(--sustainability-primary);
  margin: 1.5rem 0;
  padding: 1rem 2rem;
  background: rgba(110, 130, 111, 0.05);
  border-radius: 0 8px 8px 0;
  font-style: italic;
}

/* Custom scrollbar with enhanced design */
.word-preview-content::-webkit-scrollbar {
  width: 12px;
}

.word-preview-content::-webkit-scrollbar-track {
  background: #f1f3f5;
  border-radius: 6px;
}

.word-preview-content::-webkit-scrollbar-thumb {
  background: var(--corporate-primary);
  border-radius: 6px;
  border: 2px solid #f1f3f5;
}

.word-preview-content::-webkit-scrollbar-thumb:hover {
  background: var(--deep-teal-primary);
}

/* Add subtle animation to preview content */
.word-preview-content .docx-preview {
  animation: fadeIn 0.8s ease-out;
}

/* ===================== ADDITIONAL PREVIEW ENHANCEMENTS ===================== */

/* Add shimmer effect for table rows during loading */
.preview-table-container tbody tr.loading {
  background: #f8f9fa;
  background-size: 400px 100%;
  animation: shimmer 1.2s linear infinite;
}

/* Enhanced hover effects for interactive elements */
.preview-table-container tbody tr {
  cursor: pointer;
}

.preview-table-container tbody tr:hover td:first-child {
  background: rgba(36, 99, 159, 0.1);
}

/* Status indicators for different document types */
.document-type-indicator {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-left: 1rem;
}

.document-type-indicator.word {
  background: #2b579a;
  color: white;
}

.document-type-indicator.powerpoint {
  background: #d24726;
  color: white;
}

.document-type-indicator.pdf {
  background: #dc3545;
  color: white;
}

/* Success/completion indicators */
.preview-success-indicator {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 40px;
  height: 40px;
  background: var(--sustainability-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  box-shadow: 0 4px 16px rgba(110, 130, 111, 0.3);
  animation: popIn 0.5s ease-out;
}

/* Pulse animation for loading states */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.preview-loading .spinner-border {
  animation: spin 1s linear infinite, pulse 2s ease-in-out infinite;
}

/* Enhanced transitions for showing/hiding preview */
#previewSection {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

#previewSection[style*="block"] {
  opacity: 1;
  transform: translateY(0);
}

/* Floating action button style for preview actions */
.preview-actions .btn {
  position: relative;
  overflow: hidden;
}

.preview-actions .btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.preview-actions .btn:hover::before {
  width: 300px;
  height: 300px;
}

/* Additional responsive improvements */
@media (max-width: 576px) {
  #previewSection {
    margin: 1rem -1rem;
    border-radius: 12px;
  }

  #previewSection .excel-options-header {
    margin: -2rem -1rem 1.5rem -1rem;
    padding: 1rem;
  }

  .preview-actions {
    margin: 1.5rem -1rem -2rem -1rem;
    padding: 1.5rem 1rem;
  }

  .preview-actions .btn {
    width: 100%;
    margin-bottom: 0.75rem;
  }

  .preview-actions .btn:last-child {
    margin-bottom: 0;
  }
}

/* Enhanced responsive design for document preview */
@media (max-width: 768px) {
  .word-preview-content {
    max-height: 500px;
    padding: 1rem;
  }

  .word-preview-content .docx-preview {
    font-size: 0.9rem;
  }

  .word-preview-content .docx-preview h1 {
    font-size: 1.6rem;
  }

  .word-preview-content .docx-preview h2 {
    font-size: 1.3rem;
  }

  .word-preview-content .docx-preview h3 {
    font-size: 1.1rem;
  }

  .word-preview-loading {
    padding: 2rem 1rem;
  }

  #wordPreviewLanguageSelect {
    min-width: 150px;
    margin-left: 0.5rem;
  }

  .word-preview-container .mt-3 {
    padding: 1rem;
  }
}

/* FINAL OVERRIDE - Load last to ensure precedence */
/* Target Select2 placeholder with maximum specificity */
body .language-grid #targetLanguage + .select2-container--default .select2-selection--multiple .select2-selection__rendered,
body .language-grid #targetLanguage + .select2-container .select2-selection .select2-selection__rendered,
body #targetLanguage + .select2-container .select2-selection__placeholder,
body .select2-container[data-select2-id] .select2-selection__placeholder {
    color: #24639f !important;
    opacity: 1 !important;
    font-weight: 500 !important;
}

/* Nuclear option - target all possible Select2 text elements */
#targetLanguage + .select2-container .select2-selection__rendered {
    color: #24639f !important;
}

/* Override any potential inline styles */
.select2-selection__rendered[style] {
    color: #24639f !important;
}

/* ULTIMATE OVERRIDE - Highest specificity possible */
body .translation-container #targetLanguage + .select2-container--default .select2-selection--multiple .select2-selection__rendered,
body .translation-container #targetLanguage + .select2-container--default .select2-selection--multiple .select2-selection__rendered *,
body .translation-container #targetLanguage + .select2-container .select2-selection__placeholder {
    color: #24639f !important;
    opacity: 1 !important;
    font-weight: 500 !important;
    font-size: 1rem !important;
}

/* Force override any Select2 default colors */
.select2-container .select2-selection__rendered {
    color: #24639f !important;
}

/* Ensure proper text positioning */
#targetLanguage + .select2-container .select2-selection--multiple {
    display: flex;
    align-items: center;
}

#targetLanguage + .select2-container .select2-selection__rendered {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    min-height: 20px;
    padding: 0.25rem 0;
}

/* Fix positioning and alignment of Select2 placeholder */
.select2-container--default .select2-selection--multiple .select2-selection__rendered {
    padding: 0.5rem 0 !important;
    margin: 0 !important;
    line-height: 1.5 !important;
}

.select2-container--default .select2-selection--multiple .select2-selection__placeholder {
    color: #24639f !important;
    opacity: 1 !important;
    font-weight: 500 !important;
    font-size: 1rem !important;
    margin: 0 !important;
    padding: 0 !important;
}