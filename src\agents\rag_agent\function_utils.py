from src.common_tools.history.history import ConversationHistory
from typing import Iterator, <PERSON>, <PERSON><PERSON>, Union
from src.backend.contracts.chat_data import AgentName
from src.agents.rag_agent.document_type_filter import DocumentType, DocumentTypeNameMapping, reverse_mapping_document_type

def is_serial_number(product_code: str) -> bool:
    code_type = product_code.split("*")
    if len(code_type) == 1:
        return False
    else:
        return product_code != "missing" and code_type[1] == 'serial_number'

def is_new_chat_interaction(history: ConversationHistory, agent_name: str) -> bool:
    return len(history.messages) == 0 or history.messages[-1]["agent"] == AgentName.TEXT_TO_SQL.name or (history.messages[-1]["status"] == "complete" and history.messages[-1]["agent"] == agent_name)

def get_missing_metadata(metadata_extracred: dict) -> List[str]:

    if "product_code" not in metadata_extracred:
        metadata_extracred["product_code"] = "missing"
    if "edition" in metadata_extracred and metadata_extracred["edition"] == "missing":
        del metadata_extracred["edition"]

    missing_metadata = [key for key, value in metadata_extracred.items() if value == "missing"]
    return missing_metadata

def get_initial_user_inquiry(history: ConversationHistory):

    user_queries_history = [message for message in history.messages if message["role"] == "user" and message["agent"] != AgentName.TEXT_TO_SQL.name and message["question_status"] == "query"]
    return user_queries_history[-1]["content"] if user_queries_history else []


def check_missing_metadata(missing_metadata: list, document_metadata: dict, document_types = []) -> Tuple:

    product_code = document_metadata["product_code"]

    if "product_code" in missing_metadata and document_metadata["document_number"].lower() != "missing":
        missing_metadata.remove("product_code")
    if "document_number" in missing_metadata and document_metadata["product_code"].lower() != "missing":
        missing_metadata.remove("document_number")
    #check sul food qui
    if len(document_types) > 0:
        intermediate_answer = f"Where would you like to search?"
        return build_intermediate_answer(intermediate_answer, missing_metadata, document_metadata, document_types)
    if len(missing_metadata) > 0:
        intermediate_answer = f"Which {", ".join(missing_metadata).replace("_", " ")} would you like to search for? {"'Product code' could be PNC, Factory Model or Internal Code" if "product_code" in missing_metadata else ""}"
    else:
        if is_serial_number(product_code):
            intermediate_answer = f"In order to give a more precise answer, provide the internal code or the product number to which it refers the {product_code.split("*")[1]} requested"
        else:
            return ()

    return build_intermediate_answer(intermediate_answer,missing_metadata,document_metadata)



def build_intermediate_answer(intermediate_answer: str, missing_metadata: dict, document_metadata: dict, document_types= []) -> Tuple:
    docs = []
    response = {"ANSWER": intermediate_answer, "DOCUMENT": docs, "EXPLANATION": 'metadata-incomplete', "CHUNKS": [], "METADATA_GATHERED": document_metadata, "MISSING_METADATA": missing_metadata, "STREAM": False }
    if len(document_types) >0 :
        document_type_mapping_list = []
        for doc_type in document_types:
            key = reverse_mapping_document_type(doc_type)
            value = DocumentTypeNameMapping[key].value
            document_type_mapping_list.append({key: value})
        response["DOCUMENT_TYPES"] = document_type_mapping_list
    return (response,docs)