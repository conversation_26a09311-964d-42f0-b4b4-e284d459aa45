from typing import List, Tuple
import ast 
from langchain_core.documents.base import Document
from langchain_core.runnables.config import run_in_executor

from src.agents.rag_agent.storage import get_rag_storage
from src.agents.rag_agent.document_type_filter import DocumentType, build_filter
from src.common_tools.history.history import ConversationHistory
from src.backend.contracts.chat_data import BotType
from utils.core import get_logger
from utils.clients import DatabaseClient
from utils.exceptions import DocumentNotFoundException

logger = get_logger(__file__)


class DocumentRetriever():
    def __init__(self, bot_name:str, knowledge_base) -> None:

        rag_storage = knowledge_base
        self.bot_name = bot_name
        self.retrieval_function = rag_storage.semantic_hybrid_search_with_score_and_rerank
        self.retrival_function_without_score = rag_storage.hybrid_search

    async def search(
        self, query: str, max_k: int, history: ConversationHistory, metadata: dict,  relevancy_threshold: float = 0.75
    ) -> List[Document]:
        logger.debug(f'Fetching relevant documents for query "{query}"...')
        to_translate = 'None'
        
        highlights = []
        # document_types = []

        if self.bot_name != BotType.CALL_CENTER_BOT.name:
            filter = None
            highlights.extend(await self._azure_search(query, None, filter, relevancy_threshold, max_k)) 
        
        if self.bot_name == BotType.CALL_CENTER_BOT.name:
            
            document_types = metadata["document_type"]
            # document_types.append(metadata["document_type"])
            

            for type in document_types:
                filter = self._build_filter_azure_search(metadata, type)
                highlights.extend(await self._azure_search(query,type, filter, relevancy_threshold, max_k)) 
            
        return {'highlights': highlights, 'to_translate': to_translate}
    
    def build_initial_language_query(elf, language_filter, product_code:str, document_type, edition = None, document_number = None, isString = False):
        document_type_supported = DocumentType[document_type]
        document_type_supported_list = ",".join([f"'{element}'" for element in document_type_supported])
        document_type_filter = f"AND DOCUMENT_TYPE IN ({document_type_supported_list})"

        query_db = f"""SELECT DOCUMENT_EDITION,LANGUAGE_DESCR, FILE_PATH, DOCUMENT_TYPE_DESCR, DOCUMENT_DATE FROM DWH_PUBLIC.COMMERCIAL_PRODUCTS_DOCUMENTS 
                        WHERE {f"(INTERNAL_CODE = '{product_code}' OR PRODUCT_NUMBER = '{product_code}' OR FACTORY_MODEL = '{product_code}')" if product_code.lower() != "missing" else f"DOCUMENT_NUMBER='{document_number}'"}  
                        {document_type_filter}
                        {f"AND DOCUMENT_EDITION='{edition}'" if edition else ''} 
                        {language_filter}""" 
        
        return query_db



    def build_language_query(self, language_filter, product_code:str, document_type, edition = None, document_number = None, isString = False):

        document_type_supported = DocumentType[document_type]
        document_type_supported_list = ",".join([f"'{element}'" for element in document_type_supported])
        document_type_filter = f"AND DOCUMENT_TYPE IN ({document_type_supported_list})"

        if not isString:
            query_db = f"""SELECT DOCUMENT_EDITION,LANGUAGE_DESCR, FILE_PATH, DOCUMENT_TYPE_DESCR, DOCUMENT_DATE FROM DWH_PUBLIC.COMMERCIAL_PRODUCTS_DOCUMENTS 
                            WHERE {f"(INTERNAL_CODE = '{product_code}' OR PRODUCT_NUMBER = '{product_code}' OR FACTORY_MODEL = '{product_code}')" if product_code.lower() != "missing" else f"DOCUMENT_NUMBER='{document_number}'"}  
                            {document_type_filter}
                            {f"AND DOCUMENT_EDITION='{edition}'" if edition else ''} 
                            {language_filter}
                            {f"ORDER BY CAST(DOCUMENT_EDITION AS DECIMAL(10,2))  DESC" if document_type != 'SPC' else f"ORDER BY DOCUMENT_DATE DESC"} """ 
        else:
            query_db = f"""SELECT DOCUMENT_EDITION,LANGUAGE_DESCR, FILE_PATH, DOCUMENT_TYPE_DESCR, DOCUMENT_DATE FROM DWH_PUBLIC.COMMERCIAL_PRODUCTS_DOCUMENTS 
                            WHERE {f"(INTERNAL_CODE = '{product_code}' OR PRODUCT_NUMBER = '{product_code}' OR FACTORY_MODEL = '{product_code}')" if product_code.lower() != "missing" else f"DOCUMENT_NUMBER='{document_number}'"}  
                            {document_type_filter}
                            {f"AND DOCUMENT_EDITION='{edition}'" if edition else ''} 
                            {language_filter}
                            {f"ORDER BY DOCUMENT_DATE DESC"} """
        
        
        return query_db
    
    async def get_document_image_list(self, document_type: str, product_code: str, document_number: str, document_json: dict):

        if product_code != "MISSING":
            filter = f"""(internal_code/any(code: code eq '{product_code}') or product_number/any(code: code eq '{product_code}') or factory_model/any(code: code eq '{product_code}'))"""
        else :
            filter = f"document_number/any(number: number eq '{document_number}')"

        filter += f" and document_edition eq '{document_json["document_edition"]}'"
        filter += build_filter(document_type)
        
        filter += f" and (language_description eq '{document_json["language"]}')"

        document_chunks: List[Document] = await run_in_executor(
            None,
            self.retrival_function_without_score,
            query="*",
            k=1000,
            filters=filter
        )


        document_image_list = []
        for document_chunk in document_chunks:
            if "images" in document_chunk.metadata:
                document_image_list.extend(ast.literal_eval(document_chunk.metadata["images"]))
        
        return list(set(document_image_list))

    def _attempt_fallbacks(self, product_code: str, document_type: str, edition: str, document_number: str, filters: Tuple[str,str], db_client: DatabaseClient):
        """
        Attempt to fetch a document using a sequence of language filters.

        :param product_code: str, product identifier.
        :param document_type: str, type of document.
        :param edition: str, specific edition requested (can be None).
        :param document_number: str, document number for query.
        :param filters: list of tuples (filter_query, description)
        :return: tuple (edition, language, to_switch_product_code_and_document_number) or (None, None, to_switch_product_code_and_document_number ) if not found.
        """


        """first series of attempt is tryed with keeping the document number and prodcut code guessed by the AI, 
           if it gives no result, the AI misunderstood product code with document number so we retry the fallback with product code and 
           document_nubmer variables inverted, if this second attempt give result, we return true in the third boolean, so that the filter 
           in the azure search can be adjusted properly. """
        for language_filter_query, description in filters:
            query = self.build_initial_language_query(language_filter=language_filter_query, product_code=product_code, document_type=document_type, edition=edition, document_number=document_number, isString=False)
            results = db_client.execute(query, numrows=None)
            documents_wo_header = results[1:]
            isString = self.check_document_editon(documents_wo_header)
            
            query = self.build_language_query(language_filter=language_filter_query, product_code=product_code, document_type=document_type, edition=edition, document_number=document_number, isString=isString)
            results = db_client.execute(query, numrows=None)
            if results and len(results) > 1:
                return results[1][0], results[1][1], False
            
        #second attempt with value inverted      
        for language_filter_query, description in filters:
            query = self.build_initial_language_query(language_filter=language_filter_query, product_code=document_number, document_type=document_type, edition=edition, document_number=product_code, isString=False)
            results = db_client.execute(query, numrows=None)
            documents_wo_header = results[1:]
            isString = self.check_document_editon(documents_wo_header)

            query = self.build_language_query(language_filter=language_filter_query, product_code=product_code, document_type=document_type, edition=edition, document_number=document_number, isString=isString)
            results = db_client.execute(query, numrows=None)
        
            if results and len(results) > 1:
                return results[1][0], results[1][1], True 
        return None, None, False  # Return nothing if all fallbacks fail
    
    def check_document_editon(self, documents):
        isString = False
        for row in documents:
            edition = row[0].strip()
            if not edition.isdigit():
                isString = True
                break
        return isString

    def _build_filter_azure_search(self, metadata, document_type) :

        product_code = metadata["product_code"].upper()
        language = metadata["language_description"][0].upper() + metadata["language_description"][1:]
        document_number = metadata["document_number"].upper()

        db_client = DatabaseClient()

        if "edition" in metadata:
            edition = metadata["edition"]
        else:
            edition = None

        if document_type == "Other": #Salta tutti i controlli a DB se è stato selezionato "Altro" rendendo la ricerca più flessibile con meno filtri
            filter = f"""((internal_code/any(code: code eq '{product_code}' or code eq '{document_number}') or product_number/any(code: code eq '{product_code}' or code eq '{document_number}') or factory_model/any(code: code eq '{product_code}' or code eq '{document_number}')) or document_number/any(number: number eq '{document_number}' or number eq '{product_code}'))"""
            filter += f" and (language_description eq '{language}' or language_description eq 'Multilingual' or language_description eq 'Common')"
            filter += f" and document_edition eq '{edition}'" if edition else ""
            return filter
            
        query_language_filter = f"AND (LANGUAGE_DESCR = '{language}' OR LANGUAGE_DESCR = '{language} US')"
        query_english_filter = "AND (LANGUAGE_DESCR = 'English' OR LANGUAGE_DESCR = 'English US')"
        query_multilingual_filter = "AND (LANGUAGE_DESCR = 'Multilingual' OR LANGUAGE_DESCR = 'Common')"
        # Attempt to fetch the document in sequence: requested -> English -> Multilingual
        edition, language, to_switch_product_code_and_document_number = self._attempt_fallbacks(
            product_code, document_type, edition, document_number,
            [(query_language_filter, "requested language"), (query_english_filter, "English"), (query_multilingual_filter, "Multilingual")],
            db_client
        )

        edition_hb, language_hb = None, None
        if not edition:  # Se non viene trovato il docmento e non è un tipo documento associato ad Handbook, allora lancia errore, altrimenti cerca negli handbook
            if document_type not in {'SM', 'IN', 'SMA', 'OM'}:
                raise DocumentNotFoundException(
                    f"Sorry, the document for product you are asking for is not found in the database. "
                    f"Please check if the product code provided is correct."
                )
            else:
                edition_hb, language_hb, to_switch_product_code_and_document_number = self._attempt_fallbacks(
                    product_code, DocumentType.HB.name, None, document_number,
                    [(query_language_filter, "requested language"), (query_english_filter, "English"), (query_multilingual_filter, "Multilingual")],
                    db_client
                )
                if not edition_hb:
                    raise DocumentNotFoundException(
                        f"Sorry, the document for product you are asking for is not found in the database. "
                        f"Please check if the product code provided is correct."
                    ) 

        if to_switch_product_code_and_document_number:
            product_code, document_number = document_number, product_code  #invert product code and document number, it happens when the AI misunderstood this two kind of codes
        
        if product_code != "MISSING":
            filter = f"""(internal_code/any(code: code eq '{product_code}') or product_number/any(code: code eq '{product_code}') or factory_model/any(code: code eq '{product_code}'))"""
        else :
            filter = f"document_number/any(number: number eq '{document_number}')"
            
            
    
        if edition is not None:
            #if document_type is SM, we need to search in SM and SMA folder, this logic is managed inside the string formatting of the queries.
            filter += f" and document_edition eq '{edition}'"
            filter += build_filter(document_type)
        elif edition_hb is not None and edition is None:
            filter += f" and document_edition eq '{edition_hb}'"
            filter += build_filter(DocumentType.HB.name)


        filter += f" and (language_description eq '{language}' or language_description eq '{language_hb}' or language_description eq 'Multilingual' or language_description eq 'Common')"
        db_client.shutdown()

        return filter
    
    async def _azure_search(self,query, document_type, filter, relevancy_threshold, max_k):
        
        
        if document_type == DocumentType.SPC.name:
            max_k = 99

        highlights: List[Tuple[Document, float]] = await run_in_executor(
            None,
            self.retrieval_function,
            query,
            k=max_k,
            filters=filter
        )
        highlights = [
            document for document, _, score in highlights if score > relevancy_threshold
        ]            

        logger.info(
            f"{len(highlights)} documents have a relevancy score greater than, or equal to, {relevancy_threshold}."
        )
        logger.debug(
            f"The relevant documents selected, with their scores, are:\n{highlights}"
        )

        return highlights