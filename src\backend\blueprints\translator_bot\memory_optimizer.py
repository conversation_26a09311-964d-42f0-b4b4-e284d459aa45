"""
Memory optimization framework for translation handlers.
Provides common memory management functionality that can be used across all file format handlers.
"""

import gc
import os
import psutil
import time
from typing import Dict, List, Optional, Any
from utils.core import get_logger

logger = get_logger(__file__)


class MemoryMonitorContext:
    """Context manager for monitoring memory usage during operations"""
    
    def __init__(self, operation_name: str, handler):
        self.operation_name = operation_name
        self.handler = handler
        self.start_memory = 0
        self.peak_memory = 0
        
    def __enter__(self):
        self.start_memory = self.handler._get_memory_usage_mb()
        self.peak_memory = self.start_memory
        logger.debug(f"Starting {self.operation_name} (Memory: {self.start_memory:.1f}MB)")
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        end_memory = self.handler._get_memory_usage_mb()
        self.peak_memory = max(self.peak_memory, end_memory)
        
        if exc_type is None:
            logger.debug(f"Completed {self.operation_name} (Memory: {self.start_memory:.1f}MB -> {end_memory:.1f}MB, Peak: {self.peak_memory:.1f}MB)")
        else:
            logger.error(f"Failed {self.operation_name} (Memory: {self.start_memory:.1f}MB -> {end_memory:.1f}MB, Peak: {self.peak_memory:.1f}MB)")
        
        # Force cleanup if memory usage increased significantly
        if end_memory > self.start_memory + 50:  # More than 50MB increase
            self.handler._force_garbage_collection()
    
    def update_peak(self):
        """Update peak memory usage"""
        current_memory = self.handler._get_memory_usage_mb()
        self.peak_memory = max(self.peak_memory, current_memory)


class MemoryOptimizationMixin:
    """
    Mixin class providing memory optimization capabilities for translation handlers.
    Can be inherited by Excel, Word, PowerPoint, and PDF handlers.
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._memory_threshold_mb = 500  # Memory threshold for cleanup
        self._batch_size_adaptive = True  # Enable adaptive batch sizing
        self._performance_history = []  # Track performance for adaptive optimization
        self._file_size_factor = 1.0  # Scaling factor based on file size
        
    def _get_memory_usage_mb(self) -> float:
        """Get current memory usage in MB"""
        try:
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except Exception:
            return 0.0

    def _force_garbage_collection(self):
        """Force garbage collection and log memory usage"""
        memory_before = self._get_memory_usage_mb()
        gc.collect()
        memory_after = self._get_memory_usage_mb()
        logger.debug(f"Memory cleanup: {memory_before:.1f}MB -> {memory_after:.1f}MB (freed {memory_before - memory_after:.1f}MB)")

    def _get_adaptive_batch_size(self, default_size: int = 50, operation_type: str = "general") -> int:
        """Calculate adaptive batch size based on available memory, system load, and file characteristics"""
        if not self._batch_size_adaptive:
            return default_size
        
        try:
            memory_usage = self._get_memory_usage_mb()
            memory_info = psutil.virtual_memory()
            available_memory = memory_info.available / 1024 / 1024
            memory_percent = memory_info.percent
            
            # Base size adjustment based on memory pressure
            size_factor = 1.0
            
            # Aggressive reduction for high memory usage
            if memory_usage > self._memory_threshold_mb * 1.5:
                size_factor *= 0.25  # Very small batches
            elif memory_usage > self._memory_threshold_mb:
                size_factor *= 0.5   # Small batches
            elif memory_percent > 85:
                size_factor *= 0.6   # Reduced batches for high system memory usage
            elif available_memory < 500:  # Less than 500MB available
                size_factor *= 0.4
            elif available_memory < 1000:  # Less than 1GB available
                size_factor *= 0.7
            
            # Adjust based on file size factor
            size_factor *= self._file_size_factor
            
            # Operation-specific adjustments
            operation_factors = {
                "table": 0.6,      # Tables are more memory intensive
                "paragraph": 0.8,  # Paragraph processing is moderate
                "cell": 0.7,       # Cell processing is memory intensive
                "notes": 0.9,      # Notes processing is lighter
                "header": 0.9,     # Header processing is lighter
                "footer": 0.9,     # Footer processing is lighter
            }
            size_factor *= operation_factors.get(operation_type, 1.0)
            
            # Apply historical performance adjustments
            if self._performance_history:
                avg_performance = sum(self._performance_history) / len(self._performance_history)
                if avg_performance > 2.0:  # Slow performance, reduce batch size
                    size_factor *= 0.8
                elif avg_performance < 0.5:  # Fast performance, can increase batch size
                    size_factor *= 1.2
            
            # Calculate final batch size
            adaptive_size = max(5, int(default_size * size_factor))
            
            logger.debug(f"Adaptive batch size: {adaptive_size} (default: {default_size}, factor: {size_factor:.2f}, memory: {memory_usage:.1f}MB, available: {available_memory:.1f}MB)")
            
            return adaptive_size
            
        except Exception as e:
            logger.warning(f"Error calculating adaptive batch size: {e}")
            return default_size

    def _update_file_size_factor(self, file_size_mb: float):
        """Update the file size factor based on file characteristics"""
        # Large files need smaller batches
        if file_size_mb > 100:  # > 100MB
            self._file_size_factor = 0.4
        elif file_size_mb > 50:  # > 50MB
            self._file_size_factor = 0.6
        elif file_size_mb > 20:  # > 20MB
            self._file_size_factor = 0.8
        else:
            self._file_size_factor = 1.0
        
        logger.debug(f"File size factor: {self._file_size_factor:.2f} (file size: {file_size_mb:.1f}MB)")

    def _record_performance(self, operation_time: float):
        """Record performance metrics for adaptive optimization"""
        self._performance_history.append(operation_time)
        # Keep only recent history
        if len(self._performance_history) > 10:
            self._performance_history.pop(0)

    def _memory_monitor_context(self, operation_name: str):
        """Context manager for monitoring memory usage during operations"""
        return MemoryMonitorContext(operation_name, self)

    def _get_file_size_mb(self, file_path: str) -> float:
        """Get file size in MB"""
        try:
            return os.path.getsize(file_path) / 1024 / 1024
        except Exception:
            return 0.0

    def _should_cleanup_memory(self, current_iteration: int, cleanup_frequency: int = 10) -> bool:
        """Determine if memory cleanup should be performed"""
        if current_iteration % cleanup_frequency == 0:
            return True
        
        # Also cleanup if memory usage is high
        return self._get_memory_usage_mb() > self._memory_threshold_mb

    def _adaptive_cleanup_frequency(self, total_items: int, base_frequency: int = 10) -> int:
        """Calculate adaptive cleanup frequency based on total items"""
        if total_items > 1000:
            return max(5, base_frequency // 4)  # More frequent cleanup for large files
        elif total_items > 500:
            return max(7, base_frequency // 2)
        elif total_items > 100:
            return max(8, int(base_frequency * 0.8))
        else:
            return base_frequency

    def _process_with_memory_optimization(self, items: List[Any], process_func, operation_name: str, 
                                        batch_size: int = 50, operation_type: str = "general") -> List[Any]:
        """
        Generic method to process items with memory optimization.
        
        Args:
            items: List of items to process
            process_func: Function to process each batch of items
            operation_name: Name of the operation for logging
            batch_size: Default batch size
            operation_type: Type of operation for adaptive sizing
            
        Returns:
            List of processed results
        """
        if not items:
            return []
            
        results = []
        adaptive_batch_size = self._get_adaptive_batch_size(batch_size, operation_type)
        cleanup_frequency = self._adaptive_cleanup_frequency(len(items))
        
        with self._memory_monitor_context(f"{operation_name} ({len(items)} items)") as monitor:
            for i in range(0, len(items), adaptive_batch_size):
                batch_start_time = time.time()
                batch = items[i:i+adaptive_batch_size]
                
                try:
                    batch_results = process_func(batch)
                    results.extend(batch_results)
                    
                    # Record performance
                    batch_time = time.time() - batch_start_time
                    self._record_performance(batch_time)
                    
                    # Update memory monitoring
                    if i % 3 == 0:
                        monitor.update_peak()
                    
                    # Adaptive cleanup
                    if self._should_cleanup_memory(i // adaptive_batch_size, cleanup_frequency):
                        self._force_garbage_collection()
                        
                    # Dynamic batch size adjustment
                    if i > 0 and i % (adaptive_batch_size * 2) == 0:
                        new_batch_size = self._get_adaptive_batch_size(batch_size, operation_type)
                        if new_batch_size != adaptive_batch_size:
                            logger.debug(f"Adjusting batch size mid-operation: {adaptive_batch_size} -> {new_batch_size}")
                            adaptive_batch_size = new_batch_size
                            
                except Exception as e:
                    logger.error(f"Error processing batch {i//adaptive_batch_size + 1}: {e}")
                    # Continue with next batch on error
                    continue
        
        logger.info(f"Completed {operation_name}: processed {len(items)} items, got {len(results)} results")
        return results

    def _initialize_memory_optimization(self, file_path: str):
        """Initialize memory optimization settings based on file characteristics"""
        file_size_mb = self._get_file_size_mb(file_path)
        self._update_file_size_factor(file_size_mb)
        
        initial_memory = self._get_memory_usage_mb()
        logger.info(f"Memory optimization initialized - File: {file_size_mb:.1f}MB, Memory: {initial_memory:.1f}MB, Factor: {self._file_size_factor:.2f}")
