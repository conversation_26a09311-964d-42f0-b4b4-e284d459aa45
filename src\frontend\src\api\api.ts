import { BotChangeRequest, BotChangeResponse, ChatRequest, ChatResponse, ChatStopResponse, FeedbackRequest, SearchSettings, UserProfile, VersionDisplay } from "./models";

export class ChatResponseError extends Error {
    public retryable: boolean;

    constructor(message: string, retryable: boolean) {
        super((message = message));
        this.message = message;
        this.retryable = retryable;
    }
}


export async function chatApi(options: ChatRequest): Promise<any> {
    const response = await fetch("/chat", {
        method: "POST",
        headers: {
            "Content-Type": "application/json"
        },
        body: JSON.stringify({
            user_id: options.userID,
            conversation_id: options.conversationID,
            dialog_id: options.dialogID,
            dialog: options.dialog,
            overrides: {
                show_explanation: options.overrides?.showExplanation,
                show_sql: options.overrides?.showSQL,
                top: options.overrides?.top,
                temperature: options.overrides?.temperature,
                suggest_followup_questions: options.overrides?.suggestFollowupQuestions,
                answer_type: "html"
            },
            preview: options.preview,
            agent_to_call: options.agent_to_call,
            document_types: options.document_types,
            images: options.images
        })
    });


    // if (response.body instanceof ReadableStream){
    if(response.headers.get("Content-Type") == "text/event-stream"){

        // const reader = response.body?.pipeThrough(new TextDecoderStream()).getReader();
        const reader = response.body?.getReader()
        console.log(reader instanceof ReadableStreamDefaultReader)

        return reader
    } else{


        console.log(response)

        const parsedResponse: ChatResponse = await response.json();

        // if (parsedResponse.selection?.is_multiple) { // add other option
        //     parsedResponse.selection.choices.push("Other")
        // }

        if (response.status > 299 || !response.ok) {
            throw new ChatResponseError(parsedResponse.error ?? "An unknown error occurred.", parsedResponse.show_retry ?? false);
        }

        return parsedResponse;
    }
}



export async function chatStop(conversation_id: string): Promise<ChatStopResponse> {
    const response = await fetch(`/chat/${conversation_id}`, {
        method: "DELETE",
        headers: {
            "Content-Type": "application/json"
        },
    });

    const parsedResponse: ChatStopResponse = await response.json();
    if (response.status > 299 || !response.ok) {
        throw new ChatResponseError(parsedResponse.status ?? "An unknown error occurred.", false);
    }

    return parsedResponse;
}

export async function botChange(options: BotChangeRequest): Promise<BotChangeResponse> {
    const response = await fetch("/bot-change", {
        method: "POST",
        headers: {
            "Content-Type": "application/json"
        },
        body: JSON.stringify({
            bot: options.botType,
        })
    });

    const parsedResponse: BotChangeResponse = await response.json();
    if (response.status > 299 || !response.ok) {
        throw new ChatResponseError(parsedResponse.status ?? "An unknown error occurred.", false);
    }

    return parsedResponse;
}

export async function getAllUsers(): Promise<UserProfile[]> {
    const response = await fetch("/user-profiles", {
        method: "GET"
    });

    if (response.status === 401) {
        window.location.href = "/";
    } else if (response.status > 299 || !response.ok) {
        throw Error("Received error response when fetching user profiles.");
    }

    const userProfiles: UserProfile[] = await response.json();

    return userProfiles;
}


export async function uploadFileApi(file: File): Promise<void> {
    const reader = new FileReader();

    reader.onload = async () => {
        const base64File = reader.result?.toString().split(',')[1];

        const jsonPayload = JSON.stringify({
            fileName: file.name,
            fileType: file.type,
            fileContent: base64File
        });

        const response: Response = await fetch("/upload", {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: jsonPayload
        });

        if (response.status > 299 || !response.ok) {
            throw Error("Received error response uploading file.");
        }
    };

    reader.readAsDataURL(file);
}


export async function clearChatSession(conversationID: string): Promise<void> {
    const response = await fetch(`/chat-sessions/${conversationID}`, {
        method: "DELETE"
    });

    if (response.status > 299 || !response.ok) {
        throw Error(`Received error response when attemping to clear chat session: ${await response.text()}.`);
    }
}


export async function getSearchSettings(): Promise<SearchSettings> {
    const response = await fetch("/search-settings", {
        method: "GET"
    })

    if (response.status > 299 || !response.ok) {
        throw Error("Received error response when fetching search settings.");
    }

    const searchSettings: SearchSettings = await response.json();
    return searchSettings;
}

export async function getBotList(): Promise<{key: string, text: string}[]> {
    const response = await fetch("/bot-list", {
        method: "GET"
    })

    if (response.status > 299 || !response.ok) {
        throw Error("Received error response when fetching search settings.");
    }

    const botMap: [key: string] = await response.json();
    return Object.entries(botMap).map(([key, text]) => ({
        key,
        text
      }));
}

export function getCitationFilePath(citation: string | undefined): string {
    return `/content/${citation}`;
}


export async function getVersionDisplay (): Promise<VersionDisplay > {
    const response = await fetch("/version", {
        method: "GET"
    })

    if (response.status > 299 || !response.ok) {
        throw Error("Received error response when fetching version display.");
    }

    const versionDisplay: VersionDisplay = await response.json();
    return versionDisplay;
}


export async function getChangelogPreview(): Promise<string> {
    const response = await fetch("/changelog-preview");
    if (!response.ok) throw new Error("Errore nel recupero del changelog");
    const data = await response.json();
    return data.preview;
}


export async function feedbackApi(request: FeedbackRequest): Promise<Response> {
    const response: Response = await fetch("/chat-feedback", {
        method: "POST",
        headers: {
            "Content-Type": "application/json"
        },
        body: JSON.stringify({
            user_id: request.userID,
            conversation_id: request.conversationID,
            dialog_id: request.dialogID,
            feedback: request.feedback,
        })
    });

    if (response.status > 299 || !response.ok) {
        throw Error("Received error response when giving feedback.");
    }
    return response
}

