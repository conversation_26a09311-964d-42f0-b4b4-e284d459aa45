import React from "react";
import styles from  "../Answer.module.css"
import Zoom from 'react-medium-image-zoom';

interface Props {
    images: string[] | undefined;
    selectedBot: string | number | null;
}

export const ImageGallery = ({ images, selectedBot }: Props) => {
    
    if (!images || images.length === 0) {
        return null;
    }

    // if(selectedBot != "CREATOR_BOT"){
    //    return (
    //     <div className={styles.imageClass}>
    //         {images.map((url:string) => (
    //             <Zoom>
    //             <img
    //                 src={url}
    //                 className={styles.imageFitContain}
    //                 loading="lazy" // Enable lazy loading
    //             />
    //             </Zoom>
    //         ))}
    //     </div>
    // ); 
    // }else{
    //     return (
    //     <div className={styles.imageClass}>
    //         <Zoom>
    //             <img
    //                 src={images[0]}
    //                 className={styles.imageFitContain}
    //             />
    //             </Zoom>
    //     </div>
    // );
    // }
    return (
        <div className={styles.imageClass}>
            {images.map((url:string) => (
                <Zoom>
                <img
                    src={url}
                    className={styles.imageFitContain}
                    loading="lazy" // Enable lazy loading
                />
                </Zoom>
            ))}
        </div>
    ); 
    
};