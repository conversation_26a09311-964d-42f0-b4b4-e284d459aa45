import base64
import json
import os
import re
import time
import traceback
from typing import Dict, List, Optional, Tuple

from docx import Document
from docx.text.paragraph import Paragraph
from docx.oxml.ns import qn
from lxml import etree
import xml.etree.ElementTree as ET
from src.agents.eproexcella_agent.models.testual import TextTranslator
from src.backend.blueprints.translator_bot.pdf_utils import EnhancedTextMerger
from src.backend.blueprints.translator_bot.translator import Translator
from src.backend.blueprints.translator_bot.memory_optimizer import MemoryOptimizationMixin
from utils.core import get_logger
from pdf2docx import Converter

logger = get_logger(__file__)

class AdvancedRTFDetector:
    """
    Advanced RTF content detector that searches in multiple locations
    where RTF content might be embedded in DOCX files.
    """
    
    def __init__(self, logger):
        self.logger = logger
        
        # RTF detection patterns
        self.rtf_patterns = [
            re.compile(r'\{\\rtf1.*?\}', re.DOTALL | re.IGNORECASE),
            re.compile(r'\\rtf1\\ansi.*?(?=\}|\Z)', re.DOTALL | re.IGNORECASE),
            re.compile(r'rtf1\\ansi.*?par\s*\}', re.DOTALL | re.IGNORECASE)
        ]
        
        # Common locations where RTF might be stored
        self.rtf_locations = [
            './/w:t',  # Regular text elements
            './/w:instrText',  # Field instruction text
            './/w:fldSimple',  # Simple fields
            './/w:object',  # Embedded objects
            './/w:embedBold',  # Bold embedded content
            './/w:textbox',  # Textboxes
            './/w:txbxContent',  # Textbox content
            './/v:textbox',  # VML textboxes
            './/o:OLEObject',  # OLE objects
            './/w:docPartPr',  # Document part properties
            './/w:altChunk'  # Alternative format chunks
        ]

    def scan_document_thoroughly(self, document) -> List[Tuple]:
        """
        Perform thorough scan of document for RTF content.
        Returns list of (element, location_type, rtf_content) tuples.
        """
        rtf_findings = []
        
        try:
            # Scan document body
            rtf_findings.extend(self._scan_element_tree(document._element, "body"))
            
            # Scan headers and footers
            for section in document.sections:
                if section.header:
                    rtf_findings.extend(
                        self._scan_element_tree(section.header._element, "header")
                    )
                if section.footer:
                    rtf_findings.extend(
                        self._scan_element_tree(section.footer._element, "footer")
                    )
            
            # Scan document properties and settings
            try:
                # Access document part relationships
                doc_part = document.part
                if hasattr(doc_part, 'rels'):
                    for rel in doc_part.rels.values():
                        if hasattr(rel, 'target_part'):
                            rtf_findings.extend(
                                self._scan_document_part(rel.target_part, "related_part")
                            )
            except Exception as e:
                self.logger.debug(f"Could not scan document relationships: {e}")
            
        except Exception as e:
            self.logger.error(f"Error in thorough document scan: {e}")
            
        return rtf_findings

    def _scan_element_tree(self, element, location_type: str) -> List[Tuple]:
        """Scan an XML element tree for RTF content."""
        findings = []
        
        try:
            # Check all possible RTF storage locations
            for xpath in self.rtf_locations:
                try:
                    elements = element.xpath(xpath)
                    for elem in elements:
                        findings.extend(self._check_element_for_rtf(elem, location_type, xpath))
                except Exception as e:
                    self.logger.debug(f"XPath {xpath} failed: {e}")
            
            # Also do a raw text search on the entire element
            try:
                element_str = ET.tostring(element, encoding='unicode', method='xml')
                rtf_matches = self._find_rtf_in_text(element_str)
                if rtf_matches:
                    findings.append((element, f"{location_type}_raw", rtf_matches))
            except Exception as e:
                self.logger.debug(f"Raw element scan failed: {e}")
                
        except Exception as e:
            self.logger.debug(f"Element tree scan failed: {e}")
            
        return findings

    def _check_element_for_rtf(self, element, location_type: str, xpath: str) -> List[Tuple]:
        """Check a specific element for RTF content."""
        findings = []
        
        try:
            # Check element text
            if hasattr(element, 'text') and element.text:
                rtf_matches = self._find_rtf_in_text(element.text)
                if rtf_matches:
                    findings.append((element, f"{location_type}_{xpath}", rtf_matches))
            
            # Check element attributes
            if hasattr(element, 'attrib'):
                for attr_name, attr_value in element.attrib.items():
                    if isinstance(attr_value, str):
                        rtf_matches = self._find_rtf_in_text(attr_value)
                        if rtf_matches:
                            findings.append((element, f"{location_type}_attr_{attr_name}", rtf_matches))
            
            # Check if element might contain base64 encoded RTF
            if hasattr(element, 'text') and element.text:
                decoded_content = self._try_decode_base64(element.text)
                if decoded_content:
                    rtf_matches = self._find_rtf_in_text(decoded_content)
                    if rtf_matches:
                        findings.append((element, f"{location_type}_base64", rtf_matches))
                        
        except Exception as e:
            self.logger.debug(f"Element check failed: {e}")
            
        return findings

    def _find_rtf_in_text(self, text: str) -> Optional[List[str]]:
        """Find RTF content in text using multiple patterns."""
        if not text:
            return None
            
        rtf_matches = []
        for pattern in self.rtf_patterns:
            matches = pattern.findall(text)
            rtf_matches.extend(matches)
            
        return rtf_matches if rtf_matches else None

    def _try_decode_base64(self, text: str) -> Optional[str]:
        """Try to decode text as base64."""
        try:
            if len(text) > 20 and len(text) % 4 == 0:
                decoded = base64.b64decode(text).decode('utf-8', errors='ignore')
                return decoded if '\\rtf' in decoded else None
        except Exception:
            pass
        return None

    def _scan_document_part(self, part, location_type: str) -> List[Tuple]:
        """Scan a document part for RTF content."""
        findings = []
        
        try:
            if hasattr(part, '_blob'):
                blob_data = part._blob.decode('utf-8', errors='ignore')
                rtf_matches = self._find_rtf_in_text(blob_data)
                if rtf_matches:
                    findings.append((part, f"{location_type}_blob", rtf_matches))
        except Exception as e:
            self.logger.debug(f"Document part scan failed: {e}")
            
        return findings

class EnhancedRTFTranslator:
    """
    Enhanced RTF translator with better text extraction and reconstruction.
    """
    
    def __init__(self, logger):
        self.logger = logger
        
    def extract_text_from_rtf(self, rtf_content: str) -> List[str]:
        """
        Extract translatable text from RTF with improved parsing.
        """
        texts = []
        
        try:
            # Remove RTF header and control tables
            clean_rtf = self._clean_rtf_headers(rtf_content)

            clean_rtf = re.sub(r'\\~', ' ', clean_rtf)
            
            # Extract text between control sequences
            # This pattern looks for text that's not part of RTF control words
            text_pattern = re.compile(
                r'(?:\\[a-zA-Z]+\d*\s*)?([^\\{}]+?)(?=\\[a-zA-Z]|\}|$)',
                re.MULTILINE | re.DOTALL
            )
            
            matches = text_pattern.findall(clean_rtf)
            
            for match in matches:
                text = match.strip()
                if self._is_translatable_text(text):
                    texts.append(text)
                    
            # Also try a simpler extraction method as fallback
            if not texts:
                # Look for text after common RTF formatting commands
                simple_pattern = re.compile(r'\\f\d+\\fs\d+\s+([^\\}]+)', re.IGNORECASE)
                simple_matches = simple_pattern.findall(rtf_content)
                for match in simple_matches:
                    text = match.strip()
                    if self._is_translatable_text(text):
                        texts.append(text)
                        
        except Exception as e:
            self.logger.error(f"Error extracting RTF text: {e}")
            
        return texts

    def _clean_rtf_headers(self, rtf_content: str) -> str:
        """Remove RTF headers and control tables."""
        clean = rtf_content
        
        # Remove common RTF header elements
        patterns_to_remove = [
            r'\{\\rtf1\\ansi\\ansicpg\d+\\deff\d+[^}]*\}',
            r'\{\\fonttbl[^}]*\}',
            r'\{\\colortbl[^}]*\}',
            r'\{\\\*\\generator[^}]*\}',
            r'\\viewkind\d+',
            r'\\uc\d+',
        ]
        
        for pattern in patterns_to_remove:
            clean = re.sub(pattern, '', clean, flags=re.IGNORECASE)
            
        return clean

    def _is_translatable_text(self, text: str) -> bool:
        """Determine if text should be translated."""
        if not text or len(text.strip()) < 3:
            return False
            
        text = text.strip()
        
        # Skip RTF control words
        if re.match(r'^\\[a-zA-Z]+\d*$', text):
            return False
            
        # Skip pure numbers or formatting
        if re.match(r'^[\d\s\-\./:\\]+$', text):
            return False
            
        # Skip single characters or very short strings
        if len(text) < 3:
            return False
            
        # Must contain at least one letter
        if not re.search(r'[a-zA-Z]', text):
            return False
            
        return True

    def reconstruct_rtf(self, original_rtf: str, translations: Dict[str, str]) -> str:
        """Reconstruct RTF with translations."""
        result = original_rtf
        
        try:
            for original, translated in translations.items():
                if original and translated and original.strip() != translated.strip():
                    # Use more precise replacement to avoid breaking RTF structure
                    escaped_original = re.escape(original.strip())
                    result = re.sub(
                        escaped_original,
                        translated.strip(),
                        result,
                        count=1  # Replace only first occurrence to be safe
                    )
        except Exception as e:
            self.logger.error(f"Error reconstructing RTF: {e}")
            return original_rtf
            
        return result


class RTFContentHandler:
    """
    Handler for RTF (Rich Text Format) content embedded in Word documents.
    Extracts, translates, and reconstructs RTF sections while preserving formatting.
    """
    
    def __init__(self):
        
        # RTF control words that contain translatable text
        self.rtf_text_pattern = re.compile(
            r'\\[a-z]+\d*\s*([^\\{}]+?)(?=\\|\}|$)',
            re.IGNORECASE | re.DOTALL
        )
        
        # Pattern to identify RTF blocks
        self.rtf_block_pattern = re.compile(
            r'\{\\rtf1.*?\}',
            re.DOTALL | re.IGNORECASE
        )
        
        # Common RTF control sequences to preserve
        self.preserve_sequences = {
            r'\\par\s*': '\n',  # Paragraph breaks
            r"\\rquote\s*": "'",  # Right single quote
            r"\\lquote\s*": "'",  # Left single quote
            r"\\rdblquote\s*": '"',  # Right double quote
            r"\\ldblquote\s*": '"',  # Left double quote
            r'\\tab\s*': '\t',  # Tab
            r'\\line\s*': '\n',  # Line break
        }

    def detect_rtf_content(self, element) -> List[Tuple]:
        """
        Detect RTF content in XML elements.
        Returns list of (element, rtf_content) tuples.
        """
        rtf_elements = []
        
        try:
            # Search in element text
            if hasattr(element, 'text') and element.text:
                rtf_matches = self.rtf_block_pattern.findall(element.text)
                if rtf_matches:
                    rtf_elements.append((element, element.text, rtf_matches))
            
            # Search in child elements recursively
            for child in element:
                rtf_elements.extend(self.detect_rtf_content(child))
                
        except Exception as e:
            self.logger.debug(f"Error detecting RTF content: {e}")
            
        return rtf_elements

    def extract_translatable_text_from_rtf(self, rtf_content: str) -> List[str]:
        """
        Extract translatable text from RTF content.
        Removes RTF control codes but preserves actual text content.
        """
        translatable_texts = []
        
        try:
            # Remove RTF header and font/color tables
            rtf_clean = re.sub(r'\{\\rtf1[^}]*\}', '', rtf_content)
            rtf_clean = re.sub(r'\{\\fonttbl[^}]*\}', '', rtf_clean)
            rtf_clean = re.sub(r'\{\\colortbl[^}]*\}', '', rtf_clean)
            rtf_clean = re.sub(r'\{\\\*\\generator[^}]*\}', '', rtf_clean)
            
            # Replace common RTF sequences with readable equivalents
            for pattern, replacement in self.preserve_sequences.items():
                rtf_clean = re.sub(pattern, replacement, rtf_clean)
            
            # Extract text content while preserving structure
            # This pattern captures text that's not part of control words
            text_pattern = re.compile(
                r'(?:\\[a-z]+\d*\s*)?([^\\{}]+?)(?=\\|\}|$)',
                re.IGNORECASE
            )
            
            matches = text_pattern.findall(rtf_clean)
            for match in matches:
                text = match.strip()
                if text and len(text) > 2 and not text.isdigit():
                    # Filter out obvious control sequences and formatting
                    if not re.match(r'^[a-z]+\d*$', text, re.IGNORECASE):
                        translatable_texts.append(text)
        
        except Exception as e:
            self.logger.error(f"Error extracting text from RTF: {e}")
            
        return translatable_texts

    def reconstruct_rtf_with_translation(self, original_rtf: str, translations: Dict[str, str]) -> str:
        """
        Reconstruct RTF content with translated text while preserving formatting.
        """
        try:
            reconstructed = original_rtf
            
            # Replace original text with translations
            for original, translated in translations.items():
                if original.strip() and translated.strip():
                    # Use word boundary matching to avoid partial replacements
                    pattern = re.escape(original.strip())
                    reconstructed = re.sub(
                        rf'\b{pattern}\b',
                        translated.strip(),
                        reconstructed,
                        flags=re.IGNORECASE
                    )
            
            return reconstructed
            
        except Exception as e:
            self.logger.error(f"Error reconstructing RTF: {e}")
            return original_rtf

    def should_translate_rtf_text(self, text: str) -> bool:
        """
        Determine if RTF text content should be translated.
        """
        if not text or len(text.strip()) < 3:
            return False
            
        text = text.strip()
        
        # Skip if it's just numbers or formatting
        if text.isdigit() or re.match(r'^[\d\s\-\./:\\]+$', text):
            return False
            
        # Skip if it's just RTF control words
        if re.match(r'^[a-z]+\d*$', text, re.IGNORECASE):
            return False
            
        # Skip URLs, emails, etc.
        if re.search(r'http[s]?://', text) or '@' in text:
            return False
            
        return True

