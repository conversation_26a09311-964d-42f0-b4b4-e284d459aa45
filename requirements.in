# requirements.in

art
azure-ai-ml
azure-core
azure-identity
azure-search-documents
Flask
Flask_Login
Flask-Migrate
Flask_SocketIO
flask_session
flask_sqlalchemy
httpx
numpy
openai
oracledb
pandas
pyodbc
pymssql
sqlparse
tqdm
truststore
Werkzeug
tenacity
langchain
langchain-community
langchain-core
langchain-openai
langchain-text-splitters
tiktoken
azure-ai-documentintelligence
concurrent-log-handler
xlsxwriter
azure-storage-blob
tabulate

PyMuPDF
PyPDF2
docx2pdf
openpyxl
pip-system-certs
pip-tools
wfastcgi
marshmallow
apispec
apispec-webframeworks
markdown
sqlglot
python-semantic-release
python-docx
python-pptx
PyPDF2
reportlab
pdfplumber
pypdf
pdf2docx
python-certifi-win32
pypandoc
striprtf