
import base64
import json
import os
import re
import shutil
import tempfile
import time
import traceback
from copy import deepcopy
from typing import Dict, List, Optional, Tuple
from zipfile import ZipFile
import zipfile

from docx import Document
from docx.text.paragraph import Paragraph
from docx.oxml.ns import qn
from lxml import etree
import xml.etree.ElementTree as ET
from src.agents.eproexcella_agent.models.testual import TextTranslator
from src.backend.blueprints.translator_bot.pdf_utils import EnhancedTextMerger
from src.backend.blueprints.translator_bot.rtf_utils import AdvancedRTFDetector, EnhancedRTFTranslator, RTFContentHandler
from src.backend.blueprints.translator_bot.translator import Translator
from src.backend.blueprints.translator_bot.memory_optimizer import MemoryOptimizationMixin
from utils.core import get_logger
from pdf2docx import Converter

logger = get_logger(__file__)


class WordHandler(MemoryOptimizationMixin):
    """
    Enhanced Word document handler with improved PDF fragmentation handling and memory optimization.
    Public methods kept unchanged: translate_document, write_result_to_file,
    convert_to_word.
    """

    def __init__(self, docx_path: str, file_context, source_language: str = 'auto'):
        super().__init__()
        self.docx_path = docx_path
        self.file_context = file_context
        self.source_language = source_language
        self.original_name = ''
        self.text_merger = EnhancedTextMerger()
        self.rtf_handler = RTFContentHandler()
        self.is_pdf_converted = False
        self._initialize_memory_optimization(docx_path)

        # Precompile regexes used across methods
        self.url_pattern = re.compile(
            r"http[s]?://(?:[a-zA-Z0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+"
        )
        self.email_pattern = re.compile(
            r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b"
        )
        self.phone_pattern = re.compile(r"[\+]?[1-9]?[0-9]{7,15}")
        self.file_path_pattern = re.compile(r"[A-Za-z]:\\(?:[^\\/:*?\"<>|\r\n]+\\)*[^\\/:*?\"<>|\r\n]*")
        self.bullet_pattern = re.compile(r"^[\u2022\u2023\u25E6\u2043\u2219\-\*]\s+")
        self.numbered_pattern = re.compile(r"^\d+[\.\)]\s+")
        self.lettered_pattern = re.compile(r"^[a-zA-Z][\.\)]\s+")
        self.only_numbers_pattern = re.compile(r"^[\d\s\-\./:\\]+$")

        # Document properties
        self.document_language = "en"  # default, possibly updated later
        self._language_cached = False

    # ---------------------- Translation Helpers ---------------------- #
    def translate_all_texts_ultra_optimized(self, document: str, target_language: str) -> int:
        """
        Traduci tutti i <w:t> normali con ottimizzazione estrema.
        Riduce le chiamate API al minimo assoluto usando mega-batch intelligenti.
        """
        import tempfile, shutil
        translated_count = 0
        
        try:
            # Estrai document.xml
            with ZipFile(self.docx_path, "r") as zin:
                xml_bytes = zin.read("word/document.xml")
            tree = etree.fromstring(xml_bytes)
            namespaces = {"w": "http://schemas.openxmlformats.org/wordprocessingml/2006/main"}
            
            # Trova tutti i testi e prepara MEGA-BATCH
            nodes = tree.xpath("//w:t", namespaces=namespaces)
            texts_to_translate = []
            node_mapping = []
            
            # Raccogli tutti i testi da tradurre in un'unica passata
            for node in nodes:
                if node.text and node.text.strip() and not self._should_not_translate(node.text):
                    texts_to_translate.append(node.text.strip())
                    node_mapping.append(node)
            
            if not texts_to_translate:
                logger.info("Nessun testo da tradurre trovato")
                return 0
            
            total_texts = len(texts_to_translate)
            logger.info(f"Trovati {total_texts} testi da tradurre")
            
            # STRATEGIA ULTRA-OTTIMIZZATA: Analisi intelligente del contenuto
            total_chars = sum(len(text) for text in texts_to_translate)
            avg_length = total_chars / total_texts
            
            # Determina strategia ottimale basata sul contenuto
            if total_chars < 100000:  # Documento piccolo - UNA SOLA chiamata
                optimal_strategy = "single_mega_batch"
                batch_size = total_texts
            elif avg_length > 200:  # Testi lunghi - batch medi
                optimal_strategy = "medium_batches"
                batch_size = min(100, total_texts // 2)  # Max 2 chiamate
            else:  # Testi corti - mega batch enormi
                optimal_strategy = "ultra_large_batches"
                batch_size = min(2000, total_texts)  # Batch giganti
            
            logger.info(f"Strategia: {optimal_strategy}, batch_size: {batch_size}")
            logger.info(f"Caratteri totali: {total_chars}, lunghezza media: {avg_length:.1f}")
            
            # Esegui traduzione con strategia ottimale
            translated_texts = self._execute_optimal_translation(
                texts_to_translate, target_language, batch_size, optimal_strategy
            )
            
            if len(translated_texts) != total_texts:
                logger.error(f"Mismatch: {len(translated_texts)} traduzioni vs {total_texts} testi originali")
                return 0
            
            # Applica TUTTE le traduzioni in blocco
            for i, translated_text in enumerate(translated_texts):
                if translated_text.strip():  # Solo se la traduzione non è vuota
                    node_mapping[i].text = translated_text
                    translated_count += 1
                else:
                    # Mantieni testo originale se traduzione fallisce
                    node_mapping[i].text = texts_to_translate[i]
            
            # Serializza e salva in una volta sola
            success = self._save_optimized_xml(tree, document)
            
            if success:
                num_api_calls = (total_texts + batch_size - 1) // batch_size
                logger.info(f"✅ Tradotti {translated_count} testi con solo {num_api_calls} chiamate API!")
                logger.info(f"Riduzione chiamate: {total_texts} → {num_api_calls} (-{(1-num_api_calls/total_texts)*100:.1f}%)")
                return translated_count
            else:
                logger.error("Errore nel salvataggio del file")
                return 0
                
        except Exception as e:
            logger.error(f"Errore durante traduzione ultra-ottimizzata: {e}")
            return 0

    def _execute_optimal_translation(self, texts_to_translate: list, target_language: str, 
                                batch_size: int, strategy: str) -> list:
        """Esegue la traduzione con la strategia ottimale determinata."""
        total_texts = len(texts_to_translate)
        
        if strategy == "single_mega_batch":
            logger.info("🚀 Eseguendo traduzione in SINGOLO MEGA-BATCH")
            return self._batch_translate(texts_to_translate, target_language)
        
        else:
            # Batch multipli ma ottimizzati
            num_batches = (total_texts + batch_size - 1) // batch_size
            logger.info(f"🔄 Eseguendo traduzione in {num_batches} batch ottimizzati")
            
            all_translations = []
            
            for i in range(0, total_texts, batch_size):
                batch_num = i // batch_size + 1
                end_idx = min(i + batch_size, total_texts)
                batch = texts_to_translate[i:end_idx]
                
                logger.info(f"Batch {batch_num}/{num_batches}: traducendo {len(batch)} testi...")
                
                try:
                    batch_translated = self._batch_translate(batch, target_language)
                    all_translations.extend(batch_translated)
                    
                except Exception as e:
                    logger.error(f"Errore nel batch {batch_num}: {e}")
                    # Fallback: mantieni testi originali per questo batch
                    all_translations.extend(batch)
            
            return all_translations

    def _save_optimized_xml(self, tree, document: str) -> bool:
        """Salva l'XML modificato in modo ottimizzato con gestione errori robusta."""
        try:
            # Serializza il nuovo XML
            new_xml = etree.tostring(
                tree, xml_declaration=True, encoding="UTF-8", standalone="yes"
            )
            
            # Crea file temporaneo sicuro
            with tempfile.NamedTemporaryFile(delete=False, suffix=".docx") as tmp:
                temp_path = tmp.name
            
            # Copia tutto il DOCX sostituendo solo document.xml
            with ZipFile(document, "r") as zin:
                with ZipFile(temp_path, "w", compression=zipfile.ZIP_DEFLATED) as zout:
                    for item in zin.infolist():
                        if item.filename != "word/document.xml":
                            zout.writestr(item, zin.read(item.filename))
                        else:
                            zout.writestr("word/document.xml", new_xml)
            
            # Sostituisci atomicamente il file originale
            shutil.move(temp_path, document)
            return True
            
        except Exception as e:
            logger.error(f"Errore nel salvataggio XML: {e}")
            # Pulisci file temporaneo se esiste
            if 'temp_path' in locals() and os.path.exists(temp_path):
                try:
                    os.remove(temp_path)
                except:
                    pass
            return False

    def translate_all_texts_with_smart_analysis(self, document: str, target_language: str) -> int:
        """
        Versione ancora più intelligente che analizza il documento prima di decidere la strategia.
        """
        import tempfile, shutil
        
        try:
            # Pre-analisi rapida del documento
            analysis = self._analyze_document_content(document)
            
            if not analysis['translatable_texts']:
                logger.info("Nessun testo da tradurre trovato nell'analisi preliminare")
                return 0
            
            logger.info(f"📊 Analisi documento:")
            logger.info(f"   • Testi traducibili: {analysis['total_texts']}")
            logger.info(f"   • Caratteri totali: {analysis['total_chars']:,}")
            logger.info(f"   • Lunghezza media: {analysis['avg_length']:.1f} char/testo")
            logger.info(f"   • Testi corti (<50 char): {analysis['short_texts']}")
            logger.info(f"   • Testi lunghi (>500 char): {analysis['long_texts']}")
            
            # Decisione strategia ultra-intelligente
            strategy = self._determine_optimal_strategy(analysis)
            logger.info(f"🎯 Strategia selezionata: {strategy['name']}")
            logger.info(f"   • Batch size: {strategy['batch_size']}")
            logger.info(f"   • Chiamate API previste: {strategy['estimated_calls']}")
            
            # Esegui con strategia ottimale
            return self._execute_smart_translation(document, target_language, strategy, analysis)
            
        except Exception as e:
            logger.error(f"Errore nella traduzione intelligente: {e}")
            return 0

    def _analyze_document_content(self, document: str) -> dict:
        """Analizza rapidamente il contenuto per determinare la strategia ottimale."""
        with ZipFile(document, "r") as zin:
            xml_bytes = zin.read("word/document.xml")
        
        tree = etree.fromstring(xml_bytes)
        namespaces = {"w": "http://schemas.openxmlformats.org/wordprocessingml/2006/main"}
        nodes = tree.xpath("//w:t", namespaces=namespaces)
        
        translatable_texts = []
        for node in nodes:
            if node.text and node.text.strip() and not self._should_not_translate(node.text):
                translatable_texts.append(node.text.strip())
        
        if not translatable_texts:
            return {'translatable_texts': [], 'total_texts': 0}
        
        lengths = [len(text) for text in translatable_texts]
        total_chars = sum(lengths)
        
        return {
            'translatable_texts': translatable_texts,
            'total_texts': len(translatable_texts),
            'total_chars': total_chars,
            'avg_length': total_chars / len(translatable_texts),
            'short_texts': sum(1 for l in lengths if l < 50),
            'long_texts': sum(1 for l in lengths if l > 500),
            'min_length': min(lengths),
            'max_length': max(lengths)
        }

    def _determine_optimal_strategy(self, analysis: dict) -> dict:
        """Determina la strategia ottimale basata sull'analisi del contenuto."""
        total_texts = analysis['total_texts']
        total_chars = analysis['total_chars']
        avg_length = analysis['avg_length']
        
        # Strategia 1: Documento piccolissimo - UNA chiamata sola
        if total_chars < 10000:
            return {
                'name': 'SINGLE_MEGA_BATCH',
                'batch_size': total_texts,
                'estimated_calls': 1
            }
        
        # Strategia 2: Documento piccolo - Massimo 2 chiamate
        elif total_chars < 50000:
            batch_size = (total_texts + 1) // 2  # Dividi in 2 batch
            return {
                'name': 'DUAL_LARGE_BATCH',
                'batch_size': batch_size,
                'estimated_calls': 2
            }
        
        # Strategia 3: Molti testi corti - Batch giganti
        elif analysis['short_texts'] > total_texts * 0.7:  # 70% testi corti
            batch_size = min(1500, total_texts // 2)
            return {
                'name': 'ULTRA_LARGE_BATCH',
                'batch_size': batch_size,
                'estimated_calls': (total_texts + batch_size - 1) // batch_size
            }
        
        # Strategia 4: Molti testi lunghi - Batch medi
        elif analysis['long_texts'] > total_texts * 0.3:  # 30% testi lunghi
            batch_size = min(200, total_texts // 3)
            return {
                'name': 'MEDIUM_BATCH',
                'batch_size': batch_size,
                'estimated_calls': (total_texts + batch_size - 1) // batch_size
            }
        
        # Strategia 5: Documento bilanciato - Batch grandi
        else:
            batch_size = min(800, total_texts // 3)
            return {
                'name': 'BALANCED_LARGE_BATCH',
                'batch_size': batch_size,
                'estimated_calls': (total_texts + batch_size - 1) // batch_size
            }

    def _should_not_translate(self, text: str) -> bool:
        """Decide whether text should be left untouched."""
        if not text or not text.strip():
            return True
        t = text.strip()
        if self.url_pattern.search(t) or self.email_pattern.search(t) or self.phone_pattern.search(t):
            return True
        if self.file_path_pattern.search(t) or self.only_numbers_pattern.match(t):
            return True
        if len(t) <= 2:
            return True
        if t.isupper() and len(t.split()) == 0:
            return True
        return False

    def _preserve_formatting_elements(self, text: str) -> Tuple[str, Dict]:
        """
        Replace elements that must be preserved (URLs, emails, phones) with placeholders.
        Returns cleaned text and mapping to restore later.
        """
        preserved = {}
        clean = text
        counter = 0

        for pat, tag in ((self.url_pattern, "URL"), (self.email_pattern, "EMAIL"), (self.phone_pattern, "PHONE")):
            for match in pat.finditer(clean):
                placeholder = f"__{tag}_{counter}__"
                preserved[placeholder] = match.group()
                clean = clean.replace(match.group(), placeholder)
                counter += 1

        return clean, preserved

    def _restore_formatting_elements(self, translated_text: str, preserved: Dict) -> str:
        result = translated_text
        for ph, orig in preserved.items():
            result = result.replace(ph, orig)
        return result

    def _batch_translate(self, texts: List[str], target_lang: str, batch_size: int = 50) -> List[str]:
        """
        Translate list of texts with memory optimization. Preserves items that should not be translated.
        Uses Translator().submit_to_gpt which returns JSON mapping '1': '...', etc.
        """
        if not texts:
            return []

        # Use adaptive batch sizing
        adaptive_batch_size = self._get_adaptive_batch_size(batch_size, "paragraph")
        logger.debug(f"Using adaptive batch size: {adaptive_batch_size} (requested: {batch_size})")

        all_trans = []
        translator = Translator()
        prompt = translator.get_default_prompt(target_lang, self.source_language)

        with self._memory_monitor_context(f"Word batch translation ({len(texts)} texts)") as monitor:
            # Process in adaptive batches to limit request sizes and memory usage
            for i in range(0, len(texts), adaptive_batch_size):
                batch_start_time = time.time()
                batch = texts[i : i + adaptive_batch_size]
                clean_batch = []
                preserved_batch = []

                # Prepare clean batch with placeholders
                for text in batch:
                    if self._should_not_translate(text):
                        clean_batch.append(text)
                        preserved_batch.append({})
                    else:
                        clean, pres = self._preserve_formatting_elements(text)
                        clean_batch.append(clean)
                        preserved_batch.append(pres)

                # Map to numbered keys as expected by translator
                dict_data = {str(j + 1): t for j, t in enumerate(clean_batch)}

                try:
                    response = translator.submit_to_gpt(dict_data, prompt, self.file_context, file_extension="docx")
                    parsed = json.loads(response) if isinstance(response, str) else response

                    for idx in range(len(clean_batch)):
                        key = str(idx + 1)
                        translated = parsed.get(key, clean_batch[idx])
                        if preserved_batch[idx]:
                            translated = self._restore_formatting_elements(translated, preserved_batch[idx])
                        all_trans.append(translated)

                    # Clear intermediate variables to free memory
                    del dict_data, clean_batch, preserved_batch
                    if 'parsed' in locals():
                        del parsed

                except Exception as exc:
                    logger.error("Translation batch error: %s", exc)
                    # fallback: append originals
                    all_trans.extend(batch)

                # Record performance and update monitoring
                batch_time = time.time() - batch_start_time
                self._record_performance(batch_time)

                if i % 3 == 0:
                    monitor.update_peak()

                # Force cleanup every few batches if memory usage is high
                if i % (adaptive_batch_size * 3) == 0 and self._get_memory_usage_mb() > self._memory_threshold_mb:
                    self._force_garbage_collection()

        return all_trans

    # ---------------------- Multi-language handling---------------------- #
    def translate_document_multi_language(self, target_languages: List[str], is_pdf: bool) -> Dict[str, Document]:
        """
        Optimized multi-language translation that loads the document once
        and creates copies for each language to reduce memory usage and I/O overhead.
        """
        results = {}
        original_document = None

        try:
            initial_memory = self._get_memory_usage_mb()
            logger.info(f"Starting multi-language Word translation for {len(target_languages)} languages (Memory: {initial_memory:.1f}MB)")

            # Load the original document once
            logger.info(f"Loading original document: {self.docx_path}")
            original_document = Document(self.docx_path)

            load_memory = self._get_memory_usage_mb()
            logger.info(f"Original document loaded (Memory: {load_memory:.1f}MB)")

            for lang_index, target_language in enumerate(target_languages):
                logger.info(f"Processing language {lang_index + 1}/{len(target_languages)}: {target_language}")

                try:
                    # Create a copy of the document for this language
                    lang_document = self._create_document_copy(original_document)

                    # Translate the copy
                    translated_document = self._translate_document_in_place(lang_document, target_language, is_pdf)

                    if translated_document:
                        results[target_language] = translated_document
                        logger.info(f"Successfully translated document for {target_language}")
                    else:
                        logger.error(f"Failed to translate document for {target_language}")
                        results[target_language] = None

                    # Force cleanup after each language
                    self._force_garbage_collection()
                    current_memory = self._get_memory_usage_mb()
                    logger.info(f"Completed {target_language} (Memory: {current_memory:.1f}MB)")

                except Exception as e:
                    logger.error(f"Error translating to {target_language}: {e}")
                    results[target_language] = None

            final_memory = self._get_memory_usage_mb()
            logger.info(f"Multi-language Word translation complete. Memory: {initial_memory:.1f}MB -> {final_memory:.1f}MB")

            return results

        except Exception as e:
            logger.error(f"Error in multi-language Word translation: {e}")
            return {}
        finally:
            # Cleanup original document
            if original_document:
                del original_document
            self._force_garbage_collection()

    def _create_document_copy(self, original_document):
        """
        Create a copy of the document by saving to a temporary file and reloading.
        This is more memory efficient than deep copying the object structure.
        """
        import tempfile

        try:
            # Create a temporary file for the copy
            with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as temp_file:
                temp_path = temp_file.name

            # Save the original to the temporary file
            original_document.save(temp_path)

            # Load the copy from the temporary file
            copy_document = Document(temp_path)

            # Clean up the temporary file
            try:
                os.unlink(temp_path)
            except Exception as e:
                logger.warning(f"Could not delete temporary file {temp_path}: {e}")

            return copy_document

        except Exception as e:
            logger.error(f"Error creating document copy: {e}")
            return None

    def _translate_document_in_place(self, document, target_language: str, is_pdf: bool):
        """
        Translate a document object in place without reloading from file.
        """
        try:
            self.is_pdf_converted = is_pdf

            paragraph_count = len(document.paragraphs)
            table_count = len(document.tables)
            textbox_count = self._count_textboxes_simple(document)

            logger.info(f"Translating document for {target_language}: {paragraph_count} paragraphs, {table_count} tables, {textbox_count} textboxes")

            with self._memory_monitor_context(f"In-place translation for {target_language}") as monitor:
                # Translate paragraphs and tables
                self.translate_with_smart_chunking(document.paragraphs, document.tables, target_language)
                monitor.update_peak()
                

                # Translate textboxes
                self._translate_document_textboxes_enhanced(document, target_language)
                monitor.update_peak()

                # Translate headers and footers
                self._translate_headers_footers(document, target_language)
                monitor.update_peak()

                # Translate RTF content
                rtf_count = self._detect_and_translate_rtf_advanced(document, target_language)
                monitor.update_peak()

            return document

        except Exception as e:
            logger.error(f"Error in in-place translation for {target_language}: {e}")
            return None

    # ---------------------- RTF Handling ---------------------- #
    def _detect_and_translate_rtf_advanced(self, document, target_language: str) -> int:
        """
        Advanced RTF detection and translation method.
        Add this to your WordHandler class.
        """
        translated_count = 0
        
        try:
            # Initialize advanced RTF detector
            rtf_detector = AdvancedRTFDetector(logger)
            rtf_translator = EnhancedRTFTranslator(logger)
            
            logger.info("Starting advanced RTF content scan...")
            
            # Perform thorough scan
            rtf_findings = rtf_detector.scan_document_thoroughly(document)
            
            logger.info(f"Advanced scan found {len(rtf_findings)} RTF locations")
            
            for element, location_type, rtf_matches in rtf_findings:
                try:
                    logger.info(f"Processing RTF content in {location_type}")
                    
                    for rtf_content in rtf_matches:
                        # Log what we found (truncated for readability)
                        logger.debug(f"RTF content preview: {rtf_content[:200]}...")
                        
                        # Extract translatable text
                        translatable_texts = rtf_translator.extract_text_from_rtf(rtf_content)
                        
                        if not translatable_texts:
                            logger.debug("No translatable text found in RTF content")
                            continue
                            
                        logger.info(f"Found {len(translatable_texts)} translatable text segments")
                        logger.debug(f"Texts to translate: {translatable_texts}")
                        
                        # Filter texts that should not be translated
                        texts_to_translate = [
                            text for text in translatable_texts 
                            if not self._should_not_translate(text)
                        ]
                        
                        if not texts_to_translate:
                            logger.debug("All texts filtered out as non-translatable")
                            continue
                            
                        # Translate
                        translations = self._batch_translate(texts_to_translate, target_language)
                        
                        # Create mapping
                        translation_map = dict(zip(texts_to_translate, translations))
                        logger.info(f"Translation mapping: {translation_map}")
                        
                        # Reconstruct RTF
                        translated_rtf = rtf_translator.reconstruct_rtf(rtf_content, translation_map)
                        
                        # Update element based on location type
                        self._update_element_with_translated_rtf(
                            element, location_type, rtf_content, translated_rtf
                        )
                        
                        translated_count += 1
                        
                except Exception as e:
                    logger.error(f"Error processing RTF in {location_type}: {e}")
                    continue
                    
        except Exception as e:
            logger.error(f"Error in advanced RTF detection: {e}")
            
        return translated_count

    def _update_element_with_translated_rtf(self, element, location_type: str, original_rtf: str, translated_rtf: str):
        """
        Update element with translated RTF content based on where it was found.
        Add this helper method to your WordHandler class.
        """
        try:
            if 'attr_' in location_type:
                # RTF was in an attribute
                attr_name = location_type.split('_')[-1]
                if hasattr(element, 'attrib') and attr_name in element.attrib:
                    element.attrib[attr_name] = element.attrib[attr_name].replace(
                        original_rtf, translated_rtf
                    )
            elif 'blob' in location_type:
            # RTF si trovava in un "blob" di una parte del documento (es. un oggetto OLE)
                if hasattr(element, '_blob'):
                    logger.info(f"Aggiornamento di un content_blob per l'elemento: {element.partname}")
                    # I blob sono in bytes, quindi dobbiamo codificare le stringhe prima di sostituire
                    original_bytes = original_rtf.encode('windows-1252', errors='ignore')
                    translated_bytes = translated_rtf.encode('windows-1252', errors='ignore')
                    
                    # Sostituisce i bytes nel blob
                    element._blob = element._blob.replace(original_bytes, translated_bytes)
                else:
                    logger.warning(f"location_type era '{location_type}' ma l'elemento non ha l'attributo _blob.")

            elif 'base64' in location_type:
                # RTF was base64 encoded
                if hasattr(element, 'text') and element.text:
                    # This is complex - might need special handling
                    logger.debug("Base64 RTF update not implemented")
            else:
                # RTF was in element text
                if hasattr(element, 'text') and element.text:
                    element.text = element.text.replace(original_rtf, translated_rtf)
                    
        except Exception as e:
            logger.error(f"Error updating element with translated RTF: {e}")

    # ---------------------- Paragraphs, Tables, Headers ---------------------- #
    def translate_tables_ultra_optimized(self, tables, target_lang: str):
        """Translate all tables in one massive batch while preserving exact layout."""
        if not tables:
            return
        
        # Raccogli TUTTO il testo di TUTTE le tabelle in un singolo batch
        all_table_data = []
        texts_to_translate = []
        
        for table_idx, table in enumerate(tables):
            table_data = {'table_idx': table_idx, 'table': table, 'cells_data': []}
            
            for row_idx, row in enumerate(table.rows):
                for col_idx, cell in enumerate(row.cells):
                    for para_idx, paragraph in enumerate(cell.paragraphs):
                        if not paragraph.text.strip() or self._is_toc_or_index(paragraph):
                            continue
                        
                        cell_data = {
                            'row_idx': row_idx,
                            'col_idx': col_idx,
                            'para_idx': para_idx,
                            'paragraph': paragraph,
                            'runs_data': []
                        }
                        
                        for run_idx, run in enumerate(paragraph.runs):
                            txt = run.text or ""
                            if txt.strip() and not self._should_not_translate(txt):
                                clean, preserved = self._preserve_formatting_elements(txt)
                                cell_data['runs_data'].append({
                                    'run_idx': run_idx,
                                    'run': run,
                                    'clean_text': clean,
                                    'preserved': preserved,
                                    'original_text': txt,
                                    'batch_idx': len(texts_to_translate)  # Indice nel batch globale
                                })
                                texts_to_translate.append(clean)
                        
                        if cell_data['runs_data']:
                            table_data['cells_data'].append(cell_data)
            
            if table_data['cells_data']:
                all_table_data.append(table_data)
        
        if not texts_to_translate:
            return
        
        # UNA SOLA chiamata API per TUTTE le tabelle
        logger.info(f"Translating {len(texts_to_translate)} table texts in single batch")
        translations = self._batch_translate(texts_to_translate, target_lang)
        
        # Applica le traduzioni usando gli indici di batch
        self._apply_table_translations(all_table_data, translations)

    def translate_entire_document_optimized(self, paragraphs, tables, target_lang: str):
        """Translate the entire document in the fewest possible API calls."""
        logger.info("Using ultra-optimized document-wide translation")
        
        # Raccogli TUTTO il testo traducibile del documento
        all_texts = []
        document_map = []
        
        # 1. Raccogli tutti i paragrafi
        paragraph_data = self._collect_paragraph_texts(paragraphs, all_texts)
        if paragraph_data:
            document_map.append(('paragraphs', paragraph_data))
        
        # 2. Raccogli tutte le tabelle
        table_data = self._collect_table_texts(tables, all_texts)
        if table_data:
            document_map.append(('tables', table_data))
        
        if not all_texts:
            return
        
        # Dividi in mega-batch per rispettare i limiti delle API
        self._process_mega_batches(all_texts, document_map, target_lang)

    def _collect_paragraph_texts(self, paragraphs, all_texts: list):
        """Collect all paragraph texts for mega-batch translation."""
        paragraph_data = []
        
        for para_idx, paragraph in enumerate(paragraphs):
            if not paragraph.text.strip():
                continue
            
            if self._is_toc_or_index(paragraph):
                # I TOC li traduciamo separatamente per preservare i field codes
                continue
            
            runs = paragraph.runs
            if not runs:
                continue
            
            para_data = {
                'para_idx': para_idx,
                'paragraph': paragraph,
                'runs_data': []
            }
            
            for run_idx, run in enumerate(runs):
                txt = run.text or ""
                if txt.strip() and not self._should_not_translate(txt):
                    clean, preserved = self._preserve_formatting_elements(txt)
                    para_data['runs_data'].append({
                        'run_idx': run_idx,
                        'run': run,
                        'clean_text': clean,
                        'preserved': preserved,
                        'original_text': txt,
                        'batch_idx': len(all_texts)
                    })
                    all_texts.append(clean)
            
            if para_data['runs_data']:
                paragraph_data.append(para_data)
        
        return paragraph_data

    def _collect_table_texts(self, tables, all_texts: list):
        """Collect all table texts for mega-batch translation."""
        table_data = []
        
        for table_idx, table in enumerate(tables):
            table_info = {'table_idx': table_idx, 'table': table, 'cells_data': []}
            
            for row_idx, row in enumerate(table.rows):
                for col_idx, cell in enumerate(row.cells):
                    for para_idx, paragraph in enumerate(cell.paragraphs):
                        if not paragraph.text.strip() or self._is_toc_or_index(paragraph):
                            continue
                        
                        cell_data = {
                            'row_idx': row_idx,
                            'col_idx': col_idx,
                            'para_idx': para_idx,
                            'paragraph': paragraph,
                            'runs_data': []
                        }
                        
                        for run_idx, run in enumerate(paragraph.runs):
                            txt = run.text or ""
                            if txt.strip() and not self._should_not_translate(txt):
                                clean, preserved = self._preserve_formatting_elements(txt)
                                cell_data['runs_data'].append({
                                    'run_idx': run_idx,
                                    'run': run,
                                    'clean_text': clean,
                                    'preserved': preserved,
                                    'original_text': txt,
                                    'batch_idx': len(all_texts)
                                })
                                all_texts.append(clean)
                        
                        if cell_data['runs_data']:
                            table_info['cells_data'].append(cell_data)
            
            if table_info['cells_data']:
                table_data.append(table_info)
        
        return table_data

    def _process_mega_batches(self, all_texts, document_map, target_lang: str, max_mega_batch: int = 1000):
        """Process the document in mega-batches to minimize API calls."""
        total_texts = len(all_texts)
        logger.info(f"Processing {total_texts} total texts")
        if total_texts != 0:
            # Calcola quanti mega-batch servono
            num_batches = (total_texts + max_mega_batch - 1) // max_mega_batch
            logger.info(f"Using {num_batches} mega-batches of max {max_mega_batch} texts each")
            
            for batch_num in range(num_batches):
                start_idx = batch_num * max_mega_batch
                end_idx = min(start_idx + max_mega_batch, total_texts)
                
                batch_texts = all_texts[start_idx:end_idx]
                logger.info(f"Translating mega-batch {batch_num + 1}/{num_batches} ({len(batch_texts)} texts)")
                
                # UNA sola chiamata API per questo mega-batch
                translations = self._batch_translate(batch_texts, target_lang)
                
                # Applica le traduzioni a tutti gli elementi del documento
                self._apply_mega_batch_translations(document_map, translations, start_idx, end_idx)

    def _apply_mega_batch_translations(self, document_map, translations, start_idx, end_idx):
        """Apply translations from a mega-batch to all document elements."""
        for element_type, element_data in document_map:
            if element_type == 'paragraphs':
                self._apply_paragraph_translations(element_data, translations, start_idx, end_idx)
            elif element_type == 'tables':
                self._apply_table_translations(element_data, translations, start_idx, end_idx)

    def _apply_paragraph_translations(self, paragraph_data, translations, start_idx, end_idx):
        """Apply translations to paragraph runs."""
        for para_data in paragraph_data:
            for run_data in para_data['runs_data']:
                batch_idx = run_data['batch_idx']
                if start_idx <= batch_idx < end_idx:
                    translation_idx = batch_idx - start_idx
                    if translation_idx < len(translations):
                        translated = translations[translation_idx]
                        restored = self._restore_formatting_elements(translated, run_data['preserved'])
                        
                        if translated.strip():
                            run_data['run'].text = restored
                        else:
                            run_data['run'].text = run_data['original_text']

    def _apply_table_translations(self, table_data, translations, start_idx=0, end_idx=None):
        """Apply translations to table cell runs."""
        if end_idx is None:
            end_idx = len(translations) + start_idx
        
        for table_info in table_data:
            for cell_data in table_info['cells_data']:
                for run_data in cell_data['runs_data']:
                    batch_idx = run_data['batch_idx']
                    if start_idx <= batch_idx < end_idx:
                        translation_idx = batch_idx - start_idx
                        if translation_idx < len(translations):
                            translated = translations[translation_idx]
                            restored = self._restore_formatting_elements(translated, run_data['preserved'])
                            
                            if translated.strip():
                                run_data['run'].text = restored
                            else:
                                run_data['run'].text = run_data['original_text']

    def translate_with_smart_chunking(self, paragraphs, tables, target_lang: str):
        """
        Smart chunking that adapts batch size based on content characteristics.
        Ultimate optimization that balances API limits with minimal calls.
        """
        logger.info("Using smart chunking translation strategy")
        
        # Analizza il contenuto per determinare la strategia ottimale
        total_chars = 0
        all_texts = []
        
        # Raccogli tutto il testo per analisi
        paragraph_data = self._collect_paragraph_texts(paragraphs, all_texts)
        table_data = self._collect_table_texts(tables, all_texts)
        
        total_chars = sum(len(text) for text in all_texts)
        avg_text_length = total_chars / len(all_texts) if all_texts else 0
        
        logger.info(f"Document analysis: {len(all_texts)} texts, {total_chars} chars, avg {avg_text_length:.1f} chars/text")
        
        # Determina la strategia ottimale
        if total_chars < 50000:  # Documento piccolo - un batch solo
            optimal_batch_size = len(all_texts)
            logger.info("Small document: using single mega-batch")
        elif avg_text_length > 500:  # Testi lunghi - batch più piccoli
            optimal_batch_size = min(200, len(all_texts))
            logger.info(f"Long texts detected: using batches of {optimal_batch_size}")
        else:  # Testi corti - batch molto grandi
            optimal_batch_size = min(2000, len(all_texts))
            logger.info(f"Short texts detected: using large batches of {optimal_batch_size}")
        
        # Processa con la strategia ottimale
        document_map = []
        if paragraph_data:
            document_map.append(('paragraphs', paragraph_data))
        if table_data:
            document_map.append(('tables', table_data))
        
        self._process_mega_batches(all_texts, document_map, target_lang, optimal_batch_size)
        
        # Gestisci separatamente i TOC che richiedono trattamento speciale
        toc_paragraphs = [p for p in paragraphs if self._is_toc_or_index(p)]
        if toc_paragraphs:
            logger.info(f"Processing {len(toc_paragraphs)} TOC entries separately")
            self._translate_toc(toc_paragraphs, target_lang)

    # Funzione principale da usare al posto delle tre originali
    def translate_document_ultra_optimized(self, paragraphs, tables, target_lang: str):
        """
        Single entry point for ultra-optimized document translation.
        Replaces translate_paragraphs_enhanced and translate_tables.
        """
        self.translate_with_smart_chunking(paragraphs, tables, target_lang)
     
    def _is_toc_or_index(self, paragraph: Paragraph) -> bool:
        try:
            for run in paragraph.runs:
                if run._element.xpath('.//w:fldChar[@w:fldCharType="begin"]'):
                    fld_code = run._element.xpath('.//w:instrText')
                    if fld_code and any(code.text and ("TOC" in code.text.upper() or "INDEX" in code.text.upper()) for code in fld_code):
                        return True
            if paragraph.style and paragraph.style.name and paragraph.style.name.lower() in ("toc", "table of contents", "index"):
                return True
        except Exception:
            # keep original behavior - if we can't inspect runs, assume not TOC
            return False
        return False

    def _translate_toc(self, paragraphs, target_lang):

        """
        Trabslation of indexes and TOC
        """

        runs_to_translate = []
        original_texts = []

        for p in paragraphs:
            for run in p.runs:
                # Salta i codici di campo
                if run._element.xpath('.//w:fldChar') or run._element.xpath('.//w:instrText'):
                    continue
                if run.text.strip():
                    runs_to_translate.append(run)
                    original_texts.append(run.text)

        if not original_texts:
            return

        translations = self._batch_translate(original_texts, target_lang)

        for run, translated in zip(runs_to_translate, translations):
            if translated.strip():
                run.text = translated   

    def _translate_headers_footers(self, document: Document, target_lang: str):
        for section in document.sections:
            for hf in (section.header, section.footer):
                if hf:
                    self.translate_with_smart_chunking(hf.paragraphs, hf.tables, target_lang)
                    # translate textboxes in headers/footers
                    try:
                        if hf._element is not None:
                            self._translate_textboxes_comprehensive(hf._element, target_lang)
                    except Exception as exc:
                        logger.debug("Error translating header/footer textboxes: %s", exc)

    # ---------------------- Textbox handling ---------------------- #
    def _translate_textbox_with_enhanced_adaptation(self, textbox_element, target_lang: str) -> int:
        """
        Translate textual w:t elements inside a textbox element. Adjusts size heuristically.
        """
        texts = []
        elements = []
        preserved = []
        original_combined = []

        try:
            for elem in textbox_element.iter():
                if elem.tag == qn("w:t") and elem.text and elem.text.strip():
                    t = elem.text
                    if self._should_not_translate(t):
                        clean, pres = t, {}
                    else:
                        clean, pres = self._preserve_formatting_elements(t)
                    texts.append(clean)
                    preserved.append(pres)
                    elements.append(elem)
                    original_combined.append(t)

            if not texts:
                return 0

            translations = self._batch_translate(texts, target_lang)
            translated_combined = []

            for elem, tr, pres in zip(elements, translations, preserved):
                restored = self._restore_formatting_elements(tr, pres)
                if isinstance(restored, str) and restored.strip():
                    elem.text = restored
                translated_combined.append(restored)

            # Adjust box dims based on ratio
            self._adjust_textbox_size(
                textbox_element, " ".join(original_combined).strip(), " ".join(translated_combined).strip()
            )

            return len(elements)
        except Exception as exc:
            logger.debug("Error translating textbox with enhanced adaptation: %s", exc)
            return 0

    def _translate_textboxes_comprehensive(self, element, target_lang: str) -> int:
        """
        Single XPath pass to find textbox-like containers and translate them.
        Uses a union XPath so we don't do multiple passes.
        """
        count = 0
        # union XPath covering common textbox containers
        xpath_union = (
            './/*[local-name()="txbxContent"] '
            '| .//*[contains(local-name(), "textbox")] '
            '| .//*[contains(local-name(), "txbx")] '
            '| .//*[contains(local-name(), "shape")] '
            '| .//*[contains(local-name(), "wsp")]'
        )
        try:
            textboxes = element.xpath(xpath_union)
            logger.debug("Found %s textbox-like elements", len(textboxes))
            for tb in textboxes:
                try:
                    c = self._translate_textbox_with_enhanced_adaptation(tb, target_lang)
                    count += c
                except Exception as inner:
                    logger.debug("Error translating one textbox: %s", inner)
                    continue
        except Exception as exc:
            logger.debug("Pattern search for textboxes failed: %s", exc)
        return count

    def _extract_and_translate_textbox_text_enhanced(self, document: Document, target_lang: str) -> int:
        """
        Group contiguous w:t elements by their textbox container and translate combined content
        for PDF converted documents (safer version: translate per-element in batch to avoid losing fragments).
        """
        try:
            doc_element = document._element
            textbox_map = {}
            # find all w:t and group by nearest textbox/shape ancestor
            for elem in doc_element.iter():
                if elem.tag == qn("w:t") and elem.text and elem.text.strip():
                    parent = elem.getparent()
                    textbox_container = None
                    while parent is not None:
                        parent_tag = parent.tag.split("}")[-1] if "}" in parent.tag else parent.tag
                        if any(k in parent_tag.lower() for k in ("txbx", "txbxcontent", "textbox", "shape", "drawing", "wps")):
                            textbox_container = parent
                            break
                        parent = parent.getparent()

                    if textbox_container is not None:
                        cid = id(textbox_container)
                        textbox_map.setdefault(cid, {"container": textbox_container, "elements": [], "texts": []})
                        textbox_map[cid]["elements"].append(elem)
                        textbox_map[cid]["texts"].append(elem.text.strip())

            if not textbox_map:
                logger.info("No textbox groups found")
                return 0

            total_translated = 0

            for group in textbox_map.values():
                elements = group["elements"]
                texts = group["texts"]
                if not texts:
                    continue

                # Translate per element (keeps mapping stable). _batch_translate already batches internally.
                translations = self._batch_translate(texts, target_lang)

                # Apply translations element-wise, but do NOT overwrite with empty translation.
                translated_count = 0
                for elem, tr in zip(elements, translations):
                    if isinstance(tr, str) and tr.strip():
                        elem.text = self._restore_formatting_elements(tr, {})  # restored formatting already handled in batch
                        translated_count += 1
                    else:
                        # keep original text if translation is empty / whitespace
                        continue

                total_translated += translated_count

                # heuristic size adjust (give it combined strings)
                translated_combined = " ".join([t for t in translations if isinstance(t, str) and t.strip()]) or ""
                self._adjust_textbox_size(group["container"], " ".join(texts).strip(), translated_combined.strip())

            logger.info("Successfully translated %s textbox text elements in %s textbox groups",
                        total_translated, len(textbox_map))
            return total_translated

        except Exception as exc:
            logger.error("Error in _extract_and_translate_textbox_text_enhanced: %s", exc)
            logger.error("Traceback: %s", traceback.format_exc())
            return 0

    def _adjust_textbox_size(self, textbox_element, original_text: str, translated_text: str):
        """
        Adjust textbox dimensions based on translated text
        
        Args:
            textbox_element: XML element of textbox
            original_text: Original text
            translated_text: Translated text
        """
        try:
            # Calculate length ratio between original and translated text
            if not original_text or len(original_text.strip()) == 0:
                return
                
            length_ratio = len(translated_text) / len(original_text)
            
            # If text is significantly longer, try to adapt textbox
            if length_ratio > 1.2:  # If text is 20% longer
                # Look for textbox dimension elements
                for elem in textbox_element.iter():
                    # Look for width attributes
                    if 'w' in elem.attrib:
                        try:
                            current_width = int(elem.attrib['w'])
                            # Increase width proportionally (max 50% increase)
                            new_width = int(current_width * min(length_ratio, 1.5))
                            elem.attrib['w'] = str(new_width)
                            logger.debug(f"Adjusted textbox width from {current_width} to {new_width}")
                        except (ValueError, KeyError):
                            continue
                    
                    # Look for height attributes if necessary
                    if length_ratio > 1.5 and 'h' in elem.attrib:
                        try:
                            current_height = int(elem.attrib['h'])
                            new_height = int(current_height * min(length_ratio * 0.8, 1.3))
                            elem.attrib['h'] = str(new_height)
                            logger.debug(f"Adjusted textbox height from {current_height} to {new_height}")
                        except (ValueError, KeyError):
                            continue
                            
        except Exception as e:
            logger.debug(f"Error adjusting textbox size: {e}")

    def _translate_document_textboxes_enhanced(self, document: Document, target_lang: str) -> int:
        """
        Try multiple strategies to translate textboxes with dimension adaptation.
        The function will apply same translation approach to headers/footers if successful.
        """
        logger.info("Starting enhanced textbox translation with dimension adaptation")
        strategies = [
            ("Comprehensive XPath", lambda: self._translate_textboxes_comprehensive(document._body._element, target_lang)),
            ("Enhanced text extraction", lambda: self._extract_and_translate_textbox_text_enhanced(document, target_lang)),
        ]
        for name, func in strategies:
            try:
                logger.info("Trying strategy: %s", name)
                count = func()
                if count > 0:
                    logger.info("Strategy '%s' successful: %s elements processed", name, count)
                    # Apply to headers/footers as well
                    for section in document.sections:
                        if section.header:
                            self._translate_textboxes_comprehensive(section.header._element, target_lang)
                        if section.footer:
                            self._translate_textboxes_comprehensive(section.footer._element, target_lang)
                    return count
                else:
                    logger.info("Strategy '%s' found no textboxes", name)
            except Exception as exc:
                logger.warning("Strategy '%s' failed: %s", name, exc)
                continue

        logger.warning("All textbox translation strategies failed")
        return 0

    def _count_textboxes_simple(self, document: Document) -> int:
        """Count probable textbox elements with a simple iteration (fast)."""
        total = 0
        try:
            for elem in document._body._element.iter():
                tag_name = elem.tag.split("}")[-1] if "}" in elem.tag else elem.tag
                if any(k in tag_name.lower() for k in ("txbxcontent", "textbox", "txbx")):
                    total += 1
            for section in document.sections:
                for hf in (section.header, section.footer):
                    if hf:
                        for elem in hf._element.iter():
                            tag_name = elem.tag.split("}")[-1] if "}" in elem.tag else elem.tag
                            if any(k in tag_name.lower() for k in ("txbxcontent", "textbox", "txbx")):
                                total += 1
        except Exception as exc:
            logger.debug("Error counting textboxes: %s", exc)
        return total

    # ---------------------- Combining & Distribution ---------------------- #
    def _combine_texts_intelligently(self, texts: List[str]) -> str:
        """
        Combine textual fragments conservatively:
        - merge hyphenated splits
        - join fragments that likely continue previous fragment
        """
        if not texts:
            return ""

        if len(texts) == 1:
            return self.text_merger.merge_hyphenated_words(texts[0].strip())

        combined_parts = [texts[0].strip()]
        for nxt in texts[1:]:
            nxt_s = nxt.strip()
            if not nxt_s:
                continue
            # Se l'ultimo pezzo termina con trattino → rimuovilo e unisci senza spazio
            if combined_parts[-1].endswith("-"):
                combined_parts[-1] = combined_parts[-1][:-1] + nxt_s
            else:
                # Mantieni frammenti separati come nel DOCX originale
                combined_parts.append(nxt_s)

        return " ".join(combined_parts)

    def _distribute_translation_across_paragraphs(self, paragraph_group: List[Paragraph], translated_text: str):
        """
        Split translated text by sentences and distribute across paragraph group.
        Conservative splitting using punctuation followed by whitespace.
        """
        if not paragraph_group:
            return

        sentences = re.split(r"(?<=[.!?])\s+", translated_text)
        if len(sentences) <= 1 or len(paragraph_group) <= 1:
            paragraph_group[0].text = translated_text
            for p in paragraph_group[1:]:
                p.text = ""
            return

        per = max(1, len(sentences) // len(paragraph_group))
        for i, p in enumerate(paragraph_group):
            start = i * per
            end = start + per
            if i == len(paragraph_group) - 1:
                end = len(sentences)
            p.text = " ".join(sentences[start:end]).strip()

    def _apply_translation_to_enhanced_group(self, paragraph_group: List[Paragraph], original_combined: str, translated_text: str):
        """
        Apply translated text to a paragraph group (PDF-converted or native).
        For PDF converted groups, we place text in first paragraph and clear others,
        unless the translated text is significantly larger in which case we distribute.
        """
        if not paragraph_group:
            return
        if len(paragraph_group) == 1:
            paragraph_group[0].text = translated_text
            return

        if self.is_pdf_converted:
            ratio = len(translated_text) / max(len(original_combined), 1)
            if ratio > 1.5:
                self._distribute_translation_across_paragraphs(paragraph_group, translated_text)
            else:
                paragraph_group[0].text = translated_text
                for p in paragraph_group[1:]:
                     if not p.text.strip():  # Cancella solo se era già vuoto
                        p.text = "" 
        else:
            paragraph_group[0].text = translated_text
            for p in paragraph_group[1:]:
                 if not p.text.strip():
                    p.text = ""

    # ---------------------- Public Workflow ---------------------- #
    def translate_document(self, target_language: str, is_pdf: bool) -> Document:
        """
        Translate an entire Word document including tables, headers, footers, TOC,
        indexes and textboxes with intelligent fragmentation handling and memory optimization.
        """
        try:
            initial_memory = self._get_memory_usage_mb()
            logger.info("Loading document: %s (Initial memory: %.1fMB)", self.docx_path, initial_memory)

            document = Document(self.docx_path)

            # Basic analysis and language caching
            self.is_pdf_converted = is_pdf
            logger.info("PDF converted document: %s", self.is_pdf_converted)

            paragraph_count = len(document.paragraphs)
            table_count = len(document.tables)
            textbox_count = self._count_textboxes_simple(document)

            load_memory = self._get_memory_usage_mb()
            logger.info(
                "Starting translation of %s paragraphs, %s tables, and %s textboxes (Memory after load: %.1fMB)",
                paragraph_count, table_count, textbox_count, load_memory
            )

            with self._memory_monitor_context("Word document translation") as monitor:
            
                logger.info("Translating tables and paragraphs")
                self.translate_with_smart_chunking(document.paragraphs, document.tables, target_language)
                monitor.update_peak()
                self._force_garbage_collection()

                logger.info("Translating textboxes with enhanced size adaptation")
                translated_textbox_count = self._translate_document_textboxes_enhanced(document, target_language)
                if translated_textbox_count > 0:
                    logger.info("Successfully translated %s textbox elements with dimension adaptation", translated_textbox_count)
                else:
                    logger.warning("No textboxes were translated")
                monitor.update_peak()
                self._force_garbage_collection()

                logger.info("Translating headers and footers")
                self._translate_headers_footers(document, target_language)
                monitor.update_peak()

                logger.info("Checking for RTF content...")
                rtf_count = self._detect_and_translate_rtf_advanced(document, target_language)
                monitor.update_peak()

                if rtf_count > 0:
                    logger.info(f"Successfully translated {rtf_count} RTF sections")

            logger.info("Translation completed successfully!")
            if self.is_pdf_converted:
                logger.info("Applied enhanced PDF-aware fragmentation handling for superior translation quality")
                logger.info("Used intelligent text merging to reconstruct fragmented sentences")


            return document

        except Exception as exc:
            logger.error("Error during translation: %s", exc)
            logger.error("Full traceback: %s", traceback.format_exc())
            return None

    def write_result_to_file(self, document: Document, lang: str):
        """
        Save the translated Document object into a new file named with language suffix.
        """
        logger.info("Writing results to Word file for language: %s", lang)
        original_base, original_ext = os.path.splitext(self.docx_path)
        translated_path = f"{original_base}_{lang}{original_ext}"
        directory = os.path.dirname(translated_path)
        if directory and not os.path.exists(directory):
            logger.info("Creating directory: %s", directory)
            os.makedirs(directory, exist_ok=True)

        try:
            document.save(translated_path)
            logger.info("Successfully saved translated Word file: %s", translated_path)
            
            count1 = self.translate_all_texts_ultra_optimized(translated_path, lang)
            
            logger.info(f"Tradotti {count1} testi normali tramite fallback XML")
            if self.is_pdf_converted:
                logger.info("Document was converted from PDF - applied enhanced fragmentation handling for improved translation")
                logger.info("Text fragments were intelligently merged and distributed for better readability")
            logger.info("Note: You may need to manually update TOC and Index fields in Word after opening the translated document")
        except Exception as exc:
            logger.error("Error saving translated Word file: %s", exc)
            raise

    def convert_to_word(self):
        """
        Convert PDF (same base name) to DOCX using pdf2docx with memory optimization.
        """
        initial_memory = self._get_memory_usage_mb()
        logger.info("Converting PDF to Word using pdf2docx (Initial memory: %.1fMB)", initial_memory)

        original_base, _ = os.path.splitext(self.docx_path)
        pdf_input = f"{original_base}.pdf"
        docx_output = f"{original_base}.docx"
        self.original_name = self.docx_path

        if not os.path.exists(pdf_input):
            logger.error("PDF file not found: %s", pdf_input)
            raise FileNotFoundError(f"PDF file not found: {pdf_input}")

        # Get PDF file size for optimization
        pdf_size_mb = self._get_file_size_mb(pdf_input)
        logger.info("PDF file size: %.1fMB", pdf_size_mb)

        os.makedirs(os.path.dirname(docx_output) or ".", exist_ok=True)
        cv = None

        try:
            with self._memory_monitor_context(f"PDF to Word conversion ({pdf_size_mb:.1f}MB)") as monitor:
                logger.info("Starting PDF to DOCX conversion: %s", pdf_input)
                cv = Converter(pdf_input)

                # For large PDFs, consider page-by-page conversion to reduce memory usage
                if pdf_size_mb > 50:  # Large PDF
                    logger.info("Large PDF detected, using memory-optimized conversion")

                    cv.convert(docx_output, start=0, end=None, layout_mode="exact", parse_lattice_table=False)
                else:
                    cv.convert(docx_output, start=0, end=None, layout_mode="exact", parse_lattice_table=False)

                cv.close()
                cv = None  # Clear reference immediately
                monitor.update_peak()

                # Force garbage collection after conversion
                self._force_garbage_collection()

            if os.path.exists(docx_output):
                conversion_memory = self._get_memory_usage_mb()
                docx_size_mb = self._get_file_size_mb(docx_output)
                logger.info("Successfully saved converted Word file: %s (%.1fMB, Memory: %.1fMB)",
                           docx_output, docx_size_mb, conversion_memory)

                # Clean up PDF file
                try:
                    os.remove(pdf_input)
                    logger.info("Successfully deleted PDF file: %s", pdf_input)
                except Exception as del_exc:
                    logger.warning("Could not delete PDF file %s: %s", pdf_input, del_exc)
            else:
                logger.error("DOCX file not found at expected location: %s", docx_output)
                raise Exception("Conversion failed: DOCX file not created")

            self.docx_path = docx_output
            self.is_pdf_converted = True

            # Update memory optimization settings based on converted file size
            self._initialize_memory_optimization(docx_output)

            final_memory = self._get_memory_usage_mb()
            logger.info("PDF conversion complete. Memory: %.1fMB -> %.1fMB", initial_memory, final_memory)

            return docx_output, self.is_pdf_converted

        except Exception as exc:
            logger.error("Error converting PDF to Word with pdf2docx: %s", exc)
            # Force cleanup on error
            self._force_garbage_collection()
            raise Exception(f"pdf2docx conversion failed: {exc}")
        finally:
            try:
                if cv:
                    cv.close()
            except Exception:
                pass
    # ---------------------- Preview Workflow ---------------------- #
    def get_preview_content(self, max_paragraphs: int = 25) -> Dict[str, str]:
        """
        Extract the first meaningful paragraphs from the Word document for preview translation.
        Returns a dictionary with paragraph numbers as keys and text content as values.
        Now selects the first portion of the document for a more coherent preview.
        """
        try:
            document = Document(self.docx_path)

            # Get the first meaningful paragraphs from the beginning of the document
            selected_paragraphs = []
            paragraphs_found = 0

            for i, paragraph in enumerate(document.paragraphs):
                text = paragraph.text.strip()

                # Skip empty paragraphs and non-translatable content
                if not text or self._should_not_translate(text) or len(text) < 10:
                    continue

                # Add this paragraph to our selection
                selected_paragraphs.append((i, text))
                paragraphs_found += 1

                # Stop when we have enough paragraphs
                if paragraphs_found >= max_paragraphs:
                    break

            # Convert to dictionary format expected by the translator
            preview_content = {}
            for para_index, text in selected_paragraphs:
                # Use 1-based numbering for the key but store 0-based index info
                preview_content[f"paragraph_{para_index + 1}"] = text

            logger.info(f"Extracted first {len(preview_content)} meaningful paragraphs for preview from {len(document.paragraphs)} total paragraphs")
            logger.info(f"Selected paragraph indices: {[idx for idx, _ in selected_paragraphs]}")
            return preview_content

        except Exception as exc:
            logger.error("Error extracting preview content: %s", exc)
            return {}

    def create_shortened_document(self, min_pages: int = 2) -> str:
        """
        Create a shortened version of the original document (minimum 2 pages).
        Uses page break detection first, then falls back to paragraph counting.
        Preserves images and embedded objects using document cloning approach.
        Returns the path to the temporary shortened document.
        """
        try:
            import tempfile
            import shutil
            import os
            from docx.shared import Inches

            # If the input file is a PDF, cut to first 2 pages and convert it to DOCX first
            if self.docx_path.lower().endswith('.pdf'):
                from src.backend.blueprints.translator_bot.pdf_utils import extract_pdf_pages
                from pdf2docx import Converter
                pdf_cut_path = os.path.splitext(self.docx_path)[0] + '_cut.pdf'
                try:
                    extract_pdf_pages(self.docx_path, pdf_cut_path, max_pages=2)
                except Exception as e:
                    import logging
                    logging.warning(f"PDF page extraction failed: {e}. Using original PDF.")
                    pdf_cut_path = self.docx_path
                pdf_docx_path = os.path.splitext(self.docx_path)[0] + '_converted.docx'
                cv = Converter(pdf_cut_path)
                cv.convert(pdf_docx_path, start=0, end=None)
                cv.close()
                self.docx_path = pdf_docx_path

            logger.info(f"Creating shortened document with image preservation (target: {min_pages} pages)")

            # Create a copy of the original document file to preserve all relationships
            with tempfile.NamedTemporaryFile(suffix='_temp_copy.docx', delete=False) as temp_copy:
                temp_copy_path = temp_copy.name
            
            shutil.copy2(self.docx_path, temp_copy_path)
            
            # Load the copied document
            shortened_doc = Document(temp_copy_path)

            # Copy document properties
            self._update_document_properties(shortened_doc)

            # Try page break approach first
            success = self._remove_content_after_pages(shortened_doc, min_pages)
            
            if not success:
                logger.info("No page breaks detected, falling back to paragraph counting method")
                # Reload document and try paragraph-based approach
                shortened_doc = Document(temp_copy_path)
                self._update_document_properties(shortened_doc)
                success = self._remove_content_after_paragraphs(shortened_doc, min_pages)

            if not success:
                logger.error("Both page break and paragraph methods failed")
                # Clean up temp file
                try:
                    os.unlink(temp_copy_path)
                except:
                    pass
                return None

            # Save the modified document to final location
            with tempfile.NamedTemporaryFile(suffix='_shortened.docx', delete=False) as temp_file:
                final_path = temp_file.name

            shortened_doc.save(final_path)
            
            # Verify and log image preservation
            images_preserved = self._preserve_images_in_shortened_document(shortened_doc)
            
            # Clean up temporary copy
            try:
                os.unlink(temp_copy_path)
            except:
                pass
            
            # Log statistics
            stats = self._get_document_stats(shortened_doc)
            logger.info(f"Created shortened document with preserved images: {final_path}")
            logger.info(f"Document stats: {stats}")
            logger.info(f"Images preserved: {images_preserved}")

            return final_path

        except Exception as exc:
            logger.error("Error creating shortened document: %s", exc)
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            return None

    def _update_document_properties(self, document):
        """Update document properties for the shortened version."""
        try:
            if document.core_properties:
                original_title = document.core_properties.title or 'Document'
                document.core_properties.title = f"Preview - {original_title}"
                document.core_properties.subject = f"Shortened Preview with Images"
        except Exception as e:
            logger.debug(f"Could not update document properties: {e}")

    def _remove_content_after_pages(self, document, min_pages: int) -> bool:
        """
        Remove content after the specified number of pages by deleting elements.
        This preserves all relationships and images in the kept content.
        """
        try:
            page_count = 0
            elements_to_remove = []
            found_page_breaks = False
            remove_after_index = None

            logger.info("Attempting to shorten document using page break detection")

            # Find where to cut the document
            for i, element in enumerate(document.element.body):
                # Check for page breaks in the element XML
                element_xml = element.xml if hasattr(element, 'xml') else str(element)
                
                if ("lastRenderedPageBreak" in element_xml or 
                    "w:br w:type=\"page\"" in element_xml or
                    "w:br w:type='page'" in element_xml):
                    page_count += 1
                    found_page_breaks = True
                    logger.debug(f"Found page break #{page_count} at element {i}")
                    
                    if page_count >= min_pages:
                        remove_after_index = i + 1  # Remove elements after this one
                        logger.info(f"Will remove content after element {remove_after_index}")
                        break

            if not found_page_breaks:
                logger.info("No page breaks detected in document")
                return False

            # Remove elements after the cut point
            if remove_after_index is not None:
                body_elements = list(document.element.body)
                elements_removed = 0
                
                for i in range(len(body_elements) - 1, remove_after_index - 1, -1):
                    try:
                        element = body_elements[i]
                        element.getparent().remove(element)
                        elements_removed += 1
                    except Exception as e:
                        logger.debug(f"Error removing element {i}: {e}")

                logger.info(f"Removed {elements_removed} elements after page {min_pages}")
                return True

            return found_page_breaks

        except Exception as e:
            logger.error(f"Error in page break removal method: {e}")
            return False

    def _remove_content_after_paragraphs(self, document, min_pages: int) -> bool:
        """
        Remove content after a certain number of paragraphs (fallback method).
        This preserves all relationships and images in the kept content.
        """
        try:
            logger.info("Using paragraph counting fallback method")
            
            # Estimate paragraphs to keep
            target_paragraphs = min_pages * 15
            
            paragraphs_counted = 0
            tables_counted = 0
            max_tables = 3
            remove_after_index = None

            # Find where to cut the document
            for i, element in enumerate(document.element.body):
                if element.tag.endswith('p'):  # Paragraph
                    # Check if it's a meaningful paragraph
                    para_index = self._get_paragraph_index(document, element)
                    if para_index is not None and para_index < len(document.paragraphs):
                        original_para = document.paragraphs[para_index]
                        
                        if original_para.text.strip() or self._element_contains_images(element):
                            paragraphs_counted += 1
                            
                            # Log if this paragraph contains images
                            if self._element_contains_images(element):
                                logger.debug(f"Found paragraph with images at index {i}")

                elif element.tag.endswith('tbl'):  # Table
                    tables_counted += 1
                    
                    # Check if table contains images
                    if self._element_contains_images(element):
                        logger.debug(f"Found table with images at index {i}")
                    
                    if tables_counted > max_tables:
                        remove_after_index = i
                        break

                # Check if we have enough paragraphs
                if paragraphs_counted >= target_paragraphs:
                    remove_after_index = i + 1
                    logger.info(f"Will remove content after {paragraphs_counted} paragraphs")
                    break

            # Remove elements after the cut point
            if remove_after_index is not None:
                body_elements = list(document.element.body)
                elements_removed = 0
                
                for i in range(len(body_elements) - 1, remove_after_index - 1, -1):
                    try:
                        element = body_elements[i]
                        element.getparent().remove(element)
                        elements_removed += 1
                    except Exception as e:
                        logger.debug(f"Error removing element {i}: {e}")

                logger.info(f"Removed {elements_removed} elements after {paragraphs_counted} paragraphs")
                return True

            logger.info(f"Kept all content: {paragraphs_counted} paragraphs, {tables_counted} tables")
            return paragraphs_counted > 0

        except Exception as e:
            logger.error(f"Error in paragraph counting method: {e}")
            return False

    def _copy_document_properties(self, source_doc, target_doc):
        """Copy document properties from source to target document."""
        try:
            if source_doc.core_properties:
                target_doc.core_properties.title = f"Preview - {source_doc.core_properties.title or 'Document'}"
                target_doc.core_properties.subject = f"Shortened Preview"
                if source_doc.core_properties.author:
                    target_doc.core_properties.author = source_doc.core_properties.author
                if source_doc.core_properties.created:
                    target_doc.core_properties.created = source_doc.core_properties.created
            
            # Copy section properties (margins, page size, etc.)
            if source_doc.sections and target_doc.sections:
                source_section = source_doc.sections[0]
                target_section = target_doc.sections[0]
                
                try:
                    target_section.page_height = source_section.page_height
                    target_section.page_width = source_section.page_width
                    target_section.left_margin = source_section.left_margin
                    target_section.right_margin = source_section.right_margin
                    target_section.top_margin = source_section.top_margin
                    target_section.bottom_margin = source_section.bottom_margin
                    target_section.orientation = source_section.orientation
                except Exception as e:
                    logger.debug(f"Could not copy section properties: {e}")
                    
        except Exception as e:
            logger.warning(f"Could not copy document properties: {e}")

    def _element_contains_images(self, element) -> bool:
        """Check if an element contains images."""
        try:
            element_xml = element.xml if hasattr(element, 'xml') else str(element)
            # Check for various image-related tags in Word documents
            image_indicators = [
                'w:drawing',
                'w:object',
                'w:pict',
                'pic:pic',
                'a:graphic',
                'a:blip',
                'v:imagedata',
                'v:shape'
            ]
            
            return any(indicator in element_xml for indicator in image_indicators)
            
        except Exception as e:
            logger.debug(f"Error checking for images in element: {e}")
            return False

    def _preserve_images_in_shortened_document(self, document):
        """Ensure images are properly preserved in the shortened document."""
        try:
            # Count and log images found
            image_count = 0
            drawing_count = 0
            
            for element in document.element.body:
                if self._element_contains_images(element):
                    image_count += 1
                    
                    # Check for drawings specifically
                    element_xml = element.xml if hasattr(element, 'xml') else str(element)
                    if 'w:drawing' in element_xml:
                        drawing_count += 1
            
            logger.info(f"Preserved {image_count} image-containing elements ({drawing_count} drawings)")
            return image_count
            
        except Exception as e:
            logger.warning(f"Error preserving images: {e}")
            return 0

    def _copy_images_and_relationships(self, source_doc, target_doc):
        """Copy image relationships from source to target document."""
        try:
            # This is a complex operation in python-docx
            # For now, we'll rely on element copying to preserve relationships
            # In the future, we could implement explicit relationship copying
            
            logger.debug("Image relationships preserved through element copying")
            return True
            
        except Exception as e:
            logger.warning(f"Error copying image relationships: {e}")
            return False

    def _copy_content_by_page_breaks(self, source_doc, target_doc, min_pages: int) -> bool:
        """
        Copy content using page break detection with proper image and relationship handling.
        Returns True if successful, False if no page breaks found.
        """
        try:
            page_count = 0
            elements_copied = 0
            found_page_breaks = False

            logger.info("Attempting to copy content using page break detection with image preservation")

            # Copy images and relationships first
            self._copy_document_relationships(source_doc, target_doc)

            # Clear the default paragraph in the new document
            if target_doc.paragraphs:
                target_doc.paragraphs[0].clear()

            # Track elements to copy until we reach the target pages
            elements_to_copy = []
            
            # First pass: identify elements to copy based on page breaks
            for element in source_doc.element.body:
                elements_to_copy.append(element)
                
                # Check for page breaks in the element XML
                element_xml = element.xml if hasattr(element, 'xml') else str(element)
                
                if ("lastRenderedPageBreak" in element_xml or 
                    "w:br w:type=\"page\"" in element_xml or
                    "w:br w:type='page'" in element_xml):
                    page_count += 1
                    found_page_breaks = True
                    logger.debug(f"Found page break #{page_count} at element {len(elements_to_copy)}")
                    
                    if page_count >= min_pages:
                        logger.info(f"Reached target {min_pages} pages via page breaks")
                        break

            if not found_page_breaks:
                logger.info("No page breaks detected in document")
                return False

            # Second pass: copy elements with proper relationship handling
            for element in elements_to_copy:
                try:
                    # Use a more sophisticated copying approach that preserves relationships
                    self._copy_element_with_relationships(element, source_doc, target_doc)
                    elements_copied += 1

                except Exception as e:
                    logger.debug(f"Error copying element: {e}")
                    continue

            if page_count < min_pages:
                logger.info(f"Only found {page_count} page breaks, less than target {min_pages}")
                
            logger.info(f"Successfully copied {elements_copied} elements with {page_count} page breaks and preserved images")
            return True

        except Exception as e:
            logger.error(f"Error in page break method: {e}")
            return False

    def _get_paragraph_index(self, document, element):
        """Get the index of a paragraph element in the document."""
        try:
            paragraph_elements = [e for e in document.element.body if e.tag.endswith('p')]
            if element in paragraph_elements:
                return paragraph_elements.index(element)
            return None
        except Exception:
            return None

    def _get_document_stats(self, document):
        """Get basic statistics about the document including image count."""
        try:
            paragraphs = [p for p in document.paragraphs if p.text.strip()]
            tables = document.tables
            
            # Count images by checking elements
            image_elements = 0
            for element in document.element.body:
                if self._element_contains_images(element):
                    image_elements += 1
            
            total_chars = sum(len(p.text) for p in paragraphs)
            
            return {
                'paragraphs': len(paragraphs),
                'tables': len(tables),
                'image_elements': image_elements,
                'total_characters': total_chars,
                'avg_paragraph_length': total_chars / len(paragraphs) if paragraphs else 0,
                'estimated_pages': max(1, total_chars / 2000)  # Rough estimate
            }
        except Exception as e:
            logger.warning(f"Error getting document stats: {e}")
            return {'error': str(e)}

    def create_preview_document(self, target_language: str, max_paragraphs: int = 5) -> str:
        """
        Create a preview Word document with the same layout as original but with translated text.
        Returns the path to the temporary preview document.
        """
        try:
            logger.info(f"Creating preview document using shortened document approach for {target_language}")

            # Step 1: Create a shortened version of the original document (2+ pages)
            shortened_doc_path = self.create_shortened_document(min_pages=2)

            if not shortened_doc_path:
                logger.error("Failed to create shortened document")
                return None

            logger.info(f"Created shortened document: {shortened_doc_path}")

            # Step 2: Create a new WordHandler for the shortened document
            shortened_handler = WordHandler(shortened_doc_path, self.file_context, self.source_language)

            # Step 3: Use the standard translation process on the shortened document
            logger.info("Translating shortened document using standard translation process")
            translated_document = shortened_handler.translate_document(target_language, False)

            if not translated_document:
                logger.error("Failed to translate shortened document")
                # Clean up shortened document
                try:
                    os.unlink(shortened_doc_path)
                except:
                    pass
                return None

            logger.info(f"Successfully translated shortened document: {translated_document}")

            # Step 4: Save the translated document to a temporary file
            import tempfile
            with tempfile.NamedTemporaryFile(suffix='_preview.docx', delete=False) as temp_file:
                preview_doc_path = temp_file.name
            
            translated_document.save(preview_doc_path)
            logger.info(f"Saved preview document to: {preview_doc_path}")

            # Step 5: Clean up the temporary shortened document
            try:
                os.unlink(shortened_doc_path)
                logger.info("Cleaned up temporary shortened document")
            except Exception as e:
                logger.warning(f"Could not clean up shortened document: {e}")

            # Step 6: Return the translated document path
            return preview_doc_path

        except Exception as exc:
            logger.error("Error creating preview document: %s", exc)
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            return None

    def get_translation_stats(self) -> Dict[str, object]:
        """Return statistics about the (current) document path."""
        try:
            document = Document(self.docx_path)
            non_empty = [p for p in document.paragraphs if p.text.strip()]
            stats = {
                "total_paragraphs": len(document.paragraphs),
                "non_empty_paragraphs": len(non_empty),
                "average_paragraph_length": (sum(len(p.text) for p in non_empty) / len(non_empty)) if non_empty else 0,
                "short_paragraphs": len([p for p in non_empty if len(p.text) < 50]),
                "total_tables": len(document.tables),
                "textbox_count": self._count_textboxes_simple(document),
                "is_pdf_converted": self.is_pdf_converted,
                "detected_language": getattr(self, "document_language", "unknown"),
                "file_size_bytes": os.path.getsize(self.docx_path) if os.path.exists(self.docx_path) else 0,
                "fragmentation_handling": "enhanced" if self.is_pdf_converted else "standard",
            }
            return stats
        except Exception as exc:
            logger.error("Error getting translation stats: %s", exc)
            return {"error": str(exc)}
        

