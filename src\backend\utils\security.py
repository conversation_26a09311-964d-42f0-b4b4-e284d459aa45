from src.backend.contracts.chat_data import BotType
from src.backend.db_managers import user_get_allowed_bots
from utils.exceptions import SecurityException

def can_user_access_bot(user_db, requested_bot: BotType):
    """
    Check if the user has permission to access the specified bot.
    """
    if not user_db or not requested_bot:
        return False

    allowed_bots = user_get_allowed_bots(user_db)
    if not requested_bot in allowed_bots:
        raise SecurityException("Unable to access the requested bot")