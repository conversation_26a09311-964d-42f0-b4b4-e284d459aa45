from flask import Blueprint, render_template, request, jsonify, current_app, session, send_file
from src.backend.blueprints.auth.decorators import login_epr
from src.backend.blueprints.translator_bot.translation_service import TranslationService
from src.backend.blueprints.translator_bot.excel_handler import <PERSON><PERSON><PERSON>and<PERSON>
from src.backend.blueprints.translator_bot.prompt_generator import PromptGenerator
from src.backend.models import User
from src import db
import os
import pandas as pd
import tempfile
from werkzeug.utils import secure_filename
from src.backend.contracts.chat_data import BotType
from src.backend.models import User
from src.backend.utils.security import can_user_access_bot
from utils.exceptions import SecurityException
from utils.core import get_logger

logger = get_logger(__file__)

translator_bot_routes = Blueprint('translator_bot_routes', __name__, template_folder='templates')


def get_translator_upload_dir(user_id: str) -> str:
    """Get the upload directory path for a specific user"""
    temp_base_dir = os.path.join(tempfile.gettempdir(), 'translator-bot-uploads')
    os.makedirs(temp_base_dir, exist_ok=True)
    upload_dir = os.path.join(temp_base_dir, f'user_{user_id}')
    os.makedirs(upload_dir, exist_ok=True)
    return upload_dir


def generate_translated_filename(original_filename: str, lang: str) -> str:
    """Generate a filename for the translated file."""
    base, ext = os.path.splitext(original_filename)
    return f"{base}_{lang}{ext}"


@translator_bot_routes.route('/')
@login_epr
def index():
    """Main translation tool page"""
    
    user = session.get('user')
    if not user:
        return jsonify({'error': 'User not authenticated'}), 401
    
    user_db = User.query.filter(User.email.ilike(user['email'])).first()
    try:
        can_user_access_bot(user_db, BotType.TRANSLATOR)
        current_app.logger.info("Translation tool accessed")
        return render_template('translator_bot/translation_tool.html')
    except SecurityException as e:
        current_app.logger.error(f"Security exception: {e}")
        return jsonify({'error': str(e)}), 403


@translator_bot_routes.route('/upload', methods=['POST'])
@login_epr
def upload_file():
    """Handle file upload for translation"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        # Validate file type
        allowed_extensions = {'.xlsx', '.pptx', '.docx', '.pdf'}
        file_extension = '.' + file.filename.rsplit('.', 1)[1].lower()

        if file_extension not in allowed_extensions:
            return jsonify({'error': 'Invalid file type'}), 400

        # Get user ID from session
        user = session.get('user')
        if not user:
            return jsonify({'error': 'User not authenticated'}), 401
        user_id = user['id']

        # Clean up any existing files for this user before uploading new one
        try:
            translation_service = TranslationService(user_id)
            translation_service.cleanup()
            
            current_app.logger.info(f"Cleaned up existing files for user {user_id} during new upload")
        except Exception as e:
            current_app.logger.warning(f"Cleanup warning for user {user_id}: {e}")

        # Get upload directory for this user
        upload_dir = get_translator_upload_dir(user_id)

        # Save file with a random name
        safe_filename = secure_filename(file.filename)
        file_path = os.path.join(upload_dir, safe_filename)
        file.seek(0)  # Reset file pointer after reading
        file.save(file_path)
        # Store original filename mapping in session for later download
        session['uploaded_file_info'] = {
            'original_filename': file.filename,
            'secure_filename': safe_filename,
            'file_extension': file_extension
        }
        
        return jsonify({
            'success': True,
            'filename': file.filename,
            'type': file_extension,
        })
    
    except Exception as e:
        current_app.logger.error(f"File upload error: {e}")
        import traceback
        current_app.logger.error(f"Full traceback: {traceback.format_exc()}")
        return jsonify({'error': f'Upload failed: {str(e)}'}), 500


@translator_bot_routes.route('/api/translate', methods=['POST'])
@login_epr
def translate_document():
    """Handle document translation request"""
    import zipfile
    data = request.get_json() or {}
    user = session.get('user')
    if not user:
        current_app.logger.error("User not authenticated")
        return jsonify({'error': 'User not authenticated'}), 401
    user_id = user['id']

    target_languages = data.get('target_languages')
    if not target_languages or not isinstance(target_languages, list):
        current_app.logger.error("Target languages are required")
        return jsonify({'error': 'Target languages are required'}), 400

    source_language = data.get('source_language', 'auto')
    file_type = data.get('file_type', '')
    selected_columns = data.get('selected_columns', [])
    file_context = data.get('file_context', '').strip()
    session_id = data.get('session_id')

    uploaded_file_info = session.get('uploaded_file_info', {})
    secure_filename_val = uploaded_file_info.get('secure_filename', data.get('original_filename', ''))
    original_filename = uploaded_file_info.get('original_filename', secure_filename_val)
    upload_dir = get_translator_upload_dir(user_id)
    full_file_path = os.path.join(upload_dir, secure_filename_val)

    current_app.logger.info(f"Translation request: user={user_id}, file={original_filename}, type={file_type}, source={source_language}, langs={target_languages}, columns={selected_columns}")

    session['translation_info'] = {
        'original_filename': original_filename,
        'target_languages': target_languages
    }

    if file_type != '.xlsx' and file_type != '.pptx' and file_type != '.docx' and file_type != '.pdf':
        current_app.logger.info(f"Unsupported file type {file_type} for translation")
        return jsonify({'success': False, 'error': f'File type {file_type} is not supported yet'}), 400

    if current_app.config["AI_READY"] == False: 
        return jsonify({
            'success': False,
            'error': "Hang tight… AI services are still sipping their morning coffee ☕️, please try again shortly!",
            'session_id': session_id
        })

    try:
        # Initialize translation service 
        translation_service = TranslationService(user_id, secure_filename_val)
        
        # Set up file path for translation service
        translation_service.file_path = full_file_path
        
        # Store the session_id for progress tracking
        session['translation_session_id'] = session_id
        
        # Use the translation service's translate_file method
        translation_result = translation_service.translate_file(
            target_languages=target_languages,
            source_language=source_language,
            file_context=file_context,
            selected_columns=selected_columns,
            session_id=session_id
        )


        
        if not translation_result.get('success'):
            return jsonify({
                'success': False,
                'error': translation_result.get('error', 'Translation failed'),
                'session_id': session_id
            }), 500
        
        if file_type == '.pdf':
            # Change the name of the pdf to its docx counterpart
            original_base, original_ext = os.path.splitext(secure_filename_val)
            secure_filename_val = f"{original_base}.docx"

            original_base, original_ext = os.path.splitext(original_filename)
            original_filename = f"{original_base}.docx"


        # Create zip file if multiple languages were processed
        files_created = []
        successful_languages = []
        result = {}
        
        # Check for translated files in upload directory
        for lang in target_languages:
            # Use secure filename (the one actually used to save the file) instead of original filename
            translated_file_path = os.path.join(upload_dir, generate_translated_filename(secure_filename_val, lang))
            if os.path.exists(translated_file_path):
                files_created.append(translated_file_path)
                successful_languages.append(lang)
            else:
                current_app.logger.warning(f"Translated file not found for language {lang}: {translated_file_path}")

        # Create zip file if multiple files exist
        if len(files_created) > 1:
            zip_filename = "translated_files.zip"
            zip_filepath = os.path.join(upload_dir, zip_filename)
            try:
                import zipfile
                with zipfile.ZipFile(zip_filepath, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    for lang in successful_languages:
                        # Use secure filename for zip creation too
                        translated_file_path = os.path.join(upload_dir, generate_translated_filename(secure_filename_val, lang))
                        if os.path.exists(translated_file_path):
                            # Use original filename for the archive name (user-friendly name)
                            archive_name = generate_translated_filename(original_filename, lang)
                            zipf.write(translated_file_path, arcname=archive_name)
                            current_app.logger.info(f"Added {archive_name} to zip")
                result['zip_file'] = zip_filename
            except Exception as e:
                current_app.logger.error(f"Error creating zip file: {e}")
                result['zip_file'] = None

        return jsonify({
            'success': True,
            'message': 'Translation completed',
            'status': 'completed',
            'data': result,
            'zip_file': result.get('zip_file'),
            'languages': successful_languages,  # Only return successfully translated languages
            'columns_translated': selected_columns,
            'session_id': session_id
        })
    except Exception as e:
        current_app.logger.error(f"Translation error for user {user_id}: {e}")
        return jsonify({'error': f'Translation failed: {str(e)}'}), 500
    


@translator_bot_routes.route('/api/download/<translation_id>')
@login_epr
def download_translated_file(translation_id):
    """Download translated file"""
    try:
        current_app.logger.info(f"Download request for translation_id: {translation_id}")

        user = session.get('user')
        if not user:
            current_app.logger.error("Download request from unauthenticated user")
            return jsonify({'error': 'User not authenticated'}), 401
        user_id = user['id']
        current_app.logger.info(f"Download request from user {user_id}")

        # Get translation info from session
        translation_info = session.get('translation_info', {})
        uploaded_file_info = session.get('uploaded_file_info', {})
        original_filename = uploaded_file_info.get('original_filename', translation_info.get('original_filename', 'translated_file.xlsx'))
        target_languages = translation_info.get('target_languages', [])

        original_base, original_ext = os.path.splitext(original_filename)

        if original_ext == '.pdf':
            # Change the name of the pdf to its docx counterpart
            original_base, original_ext = os.path.splitext(original_filename)
            original_filename = f"{original_base}.docx"


        current_app.logger.info(f"Translation info - Original: {original_filename}, Languages: {target_languages}")

        # Log all request parameters for debugging
        current_app.logger.info(f"Request args: {dict(request.args)}")

        # Check if zip_file is requested
        zip_file = request.args.get('zip_file')
        if zip_file:
            upload_dir = get_translator_upload_dir(user_id)
            zip_path = os.path.join(upload_dir, zip_file)
            if os.path.exists(zip_path):
                from flask import send_file
                return send_file(
                    zip_path,
                    as_attachment=True,
                    download_name=zip_file,
                    mimetype='application/zip'
                )
            else:
                return jsonify({'error': 'Zip file not found'}), 404

        else:
            # If a specific language is requested, download that file
            requested_language = request.args.get('lang')

            # If no language specified but only one target language, use that one
            if not requested_language and len(target_languages) == 1:
                requested_language = target_languages[0]
                current_app.logger.info(f"No language parameter provided, using single target language: {requested_language}")

            if requested_language and requested_language in target_languages:
                translated_filename = generate_translated_filename(original_filename, requested_language)
                upload_dir = get_translator_upload_dir(user_id)

                # Use secure filename (the one actually used to save the file) for looking up the file
                secure_filename_val = uploaded_file_info.get('secure_filename', original_filename)
                if secure_filename_val:
                    # The translation service saves files using the secure filename with _lang suffix
                    original_base, original_ext = os.path.splitext(secure_filename_val)
                    if original_ext == '.pdf':
                        # PDF files are converted to DOCX during translation
                        lang_file_name = f"{original_base}_{requested_language}.docx"
                    else:
                        lang_file_name = f"{original_base}_{requested_language}{original_ext}"
                else:
                    # Fallback to original filename if secure filename not available
                    lang_file_name = generate_translated_filename(original_filename, requested_language)

                file_path = os.path.join(upload_dir, lang_file_name)
                current_app.logger.info(f"Looking for translated file at: {file_path}")
                current_app.logger.info(f"Secure filename used: {secure_filename_val}")
                current_app.logger.info(f"Generated lang_file_name: {lang_file_name}")

                if os.path.exists(file_path):
                    from flask import send_file
                    file_extension = translated_filename.lower().split('.')[-1] if '.' in translated_filename else 'xlsx'
                    mime_types = {
                        'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                        'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                        'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                    }
                    mimetype = mime_types.get(file_extension, 'application/octet-stream')
                    
                    # Use original filename for download (user-friendly name with original spaces/characters)
                    download_filename = generate_translated_filename(original_filename, requested_language)
                    
                    response = send_file(
                        file_path,
                        as_attachment=True,
                        download_name=download_filename,
                        mimetype=mimetype
                    )
                    return response
                else:
                    # Log additional diagnostic information
                    current_app.logger.error(f"File not found at path: {file_path}")
                    current_app.logger.error(f"Original filename: {original_filename}")
                    current_app.logger.error(f"Secure filename: {uploaded_file_info.get('secure_filename', 'Not set')}")
                    current_app.logger.error(f"Upload directory: {upload_dir}")
                    
                    # List files in upload directory for debugging
                    try:
                        if os.path.exists(upload_dir):
                            files_in_dir = os.listdir(upload_dir)
                            current_app.logger.error(f"Files in upload directory: {files_in_dir}")
                        else:
                            current_app.logger.error(f"Upload directory does not exist: {upload_dir}")
                    except Exception as list_error:
                        current_app.logger.error(f"Error listing upload directory: {list_error}")
                    
                    return jsonify({'error': f'Translated file for {requested_language} not found'}), 404
            else:
                # No valid language specified or language not in target languages
                current_app.logger.error(f"Invalid or missing language parameter. Requested: {requested_language}, Available: {target_languages}")
                return jsonify({'error': 'Invalid or missing language parameter'}), 400
        
    except Exception as e:
        current_app.logger.error(f"Download error: {e}")
        return jsonify({'error': 'Download failed'}), 500
    

@translator_bot_routes.route('/api/preview', methods=['POST'])
@login_epr
def preview_translation():
    """Generate a preview of translation for a specific column"""
    try:
        user = session.get('user')
        if not user:
            return jsonify({'error': 'User not authenticated'}), 401
        user_id = user['id']

        data = request.get_json() or {}
        column_names = data.get('column_names', [])  # Accept multiple columns
        column_name = data.get('column_name')  # Keep backward compatibility
        target_languages = data.get('target_languages', [])  # Accept multiple languages
        target_language = data.get('target_language')  # Keep backward compatibility
        file_context = data.get('file_context', '')

        # Handle both single column (backward compatibility) and multiple columns
        if column_name and not column_names:
            column_names = [column_name]

        # Handle both single language (backward compatibility) and multiple languages
        if target_language and not target_languages:
            target_languages = [target_language]

        if not column_names or not target_languages:
            return jsonify({'success': False, 'error': 'Column name(s) and target language(s) are required'}), 400

        if current_app.config["AI_READY"] == False:
            return jsonify({
                'success': False,
                'error': "Hang tight… AI services are still sipping their morning coffee ☕️, please try again shortly!"
            })

        # Initialize translation service
        translation_service = TranslationService(user_id)

        # Validate file exists
        if not os.path.exists(translation_service.file_path):
            return jsonify({'success': False, 'error': 'File not found. Please upload a file first.'}), 404

        # Get file handler based on file type
        handler_type = translation_service.get_file_handler()

        if handler_type == 'excel':
            # Use ExcelHandler for preview
            excel_handler = ExcelHandler(translation_service.file_path)

            # Get total number of rows in the Excel file
            excel_info = excel_handler.get_excel_info()
            total_rows = excel_info.get('row_count', 0) if excel_info else 0
            # Calculate preview batch size: at least 5, or 10% of total rows
            preview_batch_size = max(5, int(total_rows * 0.1)) if total_rows else 5
            # Get preview batches for all selected columns (random rows for preview)
            preview_batches = excel_handler.get_batches(column_names, max_rows=preview_batch_size, random_sample=True)

            if not preview_batches:
                return jsonify({'success': False, 'error': 'No data found in the selected columns'}), 400

            # Process all batches and organize by column
            all_preview_data = {}

            for batch in preview_batches:
                column_name = batch.get('column')
                original_content = batch.get('content', {})
                current_app.logger.info(f"Processing column '{column_name}' with content: {original_content}")

                # Create preview data structure for this column
                column_preview_data = []
                for row_key, original_text in original_content.items():
                    # Initialize translations dict for multiple languages
                    translations = {}
                    for lang in target_languages:
                        translations[lang] = f"[Translating to {lang}...]"

                    column_preview_data.append({
                        'row': row_key,
                        'original': original_text,
                        'translations': translations,
                        'translated': f"[Translating to {target_languages[0]}...]"  # Backward compatibility
                    })

                all_preview_data[column_name] = {
                    'preview_data': column_preview_data,
                    'batch': batch
                }

            # Process translations for each column and each language
            for column_name, column_data in all_preview_data.items():
                preview_data = column_data['preview_data']
                preview_batch = column_data['batch']

                # If we have data, try to get actual translation for each language
                if preview_data:
                    for target_language in target_languages:
                        try:
                            # Check if AI is ready
                            if current_app.config.get("AI_READY", False):
                                # Get translation prompt and translate the preview batch
                                prompt = translation_service.translator.get_default_prompt(target_language)
                                current_app.logger.info(f"Preview batch data for column '{column_name}' to {target_language}: {preview_batch}")
                                current_app.logger.info(f"Using prompt: {prompt[:200]}...")

                                preview_result = translation_service.translator.submit_to_gpt(preview_batch, prompt, file_context=file_context, file_extension="xlsx")
                                current_app.logger.info(f"Preview translation result for column '{column_name}' to {target_language}: {preview_result}")

                                # Parse the preview result to extract translated content
                                import json
                                if preview_result and preview_result.strip():
                                    try:
                                        result_data = json.loads(preview_result)

                                        # Handle different possible response formats
                                        if 'content' in result_data:
                                            translated_content = result_data['content']
                                        elif isinstance(result_data, dict):
                                            # If the result is directly the content dictionary
                                            translated_content = result_data
                                        else:
                                            current_app.logger.warning(f"Unexpected result format: {type(result_data)}")
                                            translated_content = {}

                                        current_app.logger.info(f"Translated content for column '{column_name}' to {target_language}: {translated_content}")

                                        # Update preview data with actual translations for this language
                                        for item in preview_data:
                                            row_key = item['row']
                                            if row_key in translated_content:
                                                item['translations'][target_language] = translated_content[row_key]
                                                # Update backward compatibility field for first language
                                                if target_language == target_languages[0]:
                                                    item['translated'] = translated_content[row_key]
                                            else:
                                                current_app.logger.warning(f"No translation found for row {row_key} in column '{column_name}' for {target_language}")

                                        current_app.logger.info(f"Updated preview data for column '{column_name}' to {target_language}")

                                    except json.JSONDecodeError as e:
                                        current_app.logger.error(f"Failed to parse translation result for column '{column_name}' to {target_language}: {e}")
                                        current_app.logger.error(f"Raw result was: {preview_result}")
                                        # Keep the placeholder translations if parsing fails
                                else:
                                    current_app.logger.error(f"Empty or null translation result for column '{column_name}' to {target_language}")
                            else:
                                current_app.logger.warning("AI service not ready, keeping placeholder translations")

                        except Exception as translation_error:
                            current_app.logger.error(f"Translation failed for column '{column_name}': {translation_error}")
                            import traceback
                            current_app.logger.error(f"Translation error traceback: {traceback.format_exc()}")

            # Flatten all preview data from all columns into a single list
            combined_preview_data = []
            for column_name, column_data in all_preview_data.items():
                for item in column_data['preview_data']:
                    # Add column information to each item
                    item_with_column = item.copy()
                    item_with_column['column'] = column_name
                    combined_preview_data.append(item_with_column)

            return jsonify({
                'success': True,
                'column_names': column_names,
                'target_languages': target_languages,
                'target_language': target_languages[0] if target_languages else None,  # Backward compatibility
                'preview_data': combined_preview_data,
                'columns_data': all_preview_data,  # Include detailed column data
                'total_rows_in_file': excel_handler.get_excel_info().get('row_count', 0)
            })

        elif handler_type == 'word' or handler_type == '.pdf':
            # For Word documents, return a special response indicating document preview is available
            return jsonify({
                'success': True,
                'use_document_preview': True,
                'message': 'Word document preview available',
                'target_languages': target_languages,
                'file_type': 'word'
            })

        elif handler_type == 'ppt':
            # For PowerPoint documents, return a special response indicating document preview is available
            return jsonify({
                'success': True,
                'use_document_preview': True,
                'message': 'PowerPoint document preview available',
                'target_languages': target_languages,
                'file_type': 'ppt'
            })

        else:
            # For other file types, return a message that preview is not available yet
            return jsonify({
                'success': False,
                'error': f'Preview is not yet available for {handler_type} files. You can proceed with full translation.'
            }), 400

    except Exception as e:
        current_app.logger.error(f"Preview error: {e}")
        return jsonify({'success': False, 'error': f'Preview failed: {str(e)}'}), 500


@translator_bot_routes.route('/api/columns', methods=['GET'])
@login_epr
def get_excel_columns():
    """Get available columns and row count from uploaded Excel file"""
    try:
        user = session.get('user')
        if not user:
            return jsonify({'error': 'User not authenticated'}), 401
        user_id = user['id']

        # Initialize TranslationService
        translation_service = TranslationService(user_id)

        # Use ExcelHandler directly with the file path from translation_service
        excel_handler = ExcelHandler(translation_service.file_path)

        # Get Excel file info (columns and row count)
        excel_info = excel_handler.get_excel_info()

        if excel_info and 'error' not in excel_info:
            return jsonify({
                'success': True,
                'columns': excel_info['col_names'],
                'row_count': excel_info['row_count']
            })
        else:
            return jsonify({'success': False, 'error': excel_info.get('error', 'No columns found or file not uploaded')}), 404

    except Exception as e:
        current_app.logger.error(f"Get columns error: {e}")
        return jsonify({'error': 'Failed to get columns'}), 500


@translator_bot_routes.route('/api/version', methods=['GET'])
@login_epr
def get_version():
    """Get version information for translator bot"""
    try:
        from src.backend.utils.sys_utils import get_env_version
        return get_env_version()
    except Exception as e:
        current_app.logger.error(f"Version error: {e}")
        return jsonify({'error': 'Failed to get version'}), 500


@translator_bot_routes.route('/api/cleanup', methods=['POST'])
@login_epr
def cleanup_session():
    """Clean up translation session data only (files are preserved unless explicitly requested)"""
    try:
        user = session.get('user')
        if not user:
            return jsonify({'error': 'User not authenticated'}), 401
        
        user_id = user['id']
        data = request.get_json() or {}
        session_id = data.get('session_id')
        cleanup_files = data.get('cleanup_files', False)
        force_cleanup = data.get('force_cleanup', False)  # Only for internal use (new uploads)
        
        current_app.logger.info(f"Cleanup request: user={user_id}, session={session_id}, cleanup_files={cleanup_files}, force_cleanup={force_cleanup}")
        
        # Only clean up uploaded files if explicitly forced (e.g., from new upload process)
        if cleanup_files and force_cleanup:
            try:
                translation_service = TranslationService(user_id)
                translation_service.cleanup()
                current_app.logger.info(f"Cleaned up files for user {user_id} (forced cleanup)")
            except Exception as e:
                current_app.logger.warning(f"Failed to clean up files for user {user_id}: {e}")
        elif cleanup_files:
            current_app.logger.info(f"File cleanup requested but not forced - preserving files for user {user_id}")
        
        # Clean up preview documents
        preview_docs = session.get('preview_documents', [])
        for doc_path in preview_docs:
            try:
                if os.path.exists(doc_path):
                    os.unlink(doc_path)
                    current_app.logger.info(f"Cleaned up preview document: {doc_path}")
            except Exception as e:
                current_app.logger.warning(f"Failed to clean up preview document {doc_path}: {e}")

        # Clear session data (this is safe to do always)
        session.pop('uploaded_file_info', None)
        session.pop('translation_info', None)
        session.pop('translation_session_id', None)
        session.pop('preview_documents', None)
        
        return jsonify({'success': True})
        
    except Exception as e:
        current_app.logger.error(f"Cleanup error: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500


@translator_bot_routes.route('/api/preview-word-document', methods=['POST'])
@login_epr
def preview_word_document():
    """Create and serve a preview Word document with translated sample content"""
    try:
        user = session.get('user')
        if not user:
            return jsonify({'error': 'User not authenticated'}), 401
        user_id = user['id']

        # Get request data
        data = request.get_json() or {}
        target_language = data.get('target_language')
        file_context = data.get('file_context', '').strip()

        if not target_language:
            return jsonify({'error': 'Target language is required'}), 400

        # Get the uploaded file info from session
        uploaded_file_info = session.get('uploaded_file_info', {})
        secure_filename_val = uploaded_file_info.get('secure_filename')
        file_extension = uploaded_file_info.get('file_extension')

        if not secure_filename_val or file_extension not in ['.docx', '.pdf']:
            return jsonify({'error': 'No Word document found'}), 404

        # Get the original file path
        upload_dir = get_translator_upload_dir(user_id)
        file_path = os.path.join(upload_dir, secure_filename_val)

        if not os.path.exists(file_path):
            return jsonify({'error': 'Original document not found'}), 404

        # Check if AI is ready
        if not current_app.config.get("AI_READY", False):
            return jsonify({
                'error': "Hang tight… AI services are still sipping their morning coffee ☕️, please try again shortly!"
            }), 503

        # Create preview document using WordHandler
        from src.backend.blueprints.translator_bot.word_handler import WordHandler

        # If the file is a PDF, cut to first 2 pages and convert to DOCX first
        if file_extension == '.pdf':
            from src.backend.blueprints.translator_bot.pdf_utils import extract_pdf_pages
            from pdf2docx import Converter
            import tempfile
            # Cut PDF to first 2 pages (or all if less)
            pdf_cut_path = os.path.splitext(file_path)[0] + '_cut.pdf'
            try:
                extract_pdf_pages(file_path, pdf_cut_path, max_pages=2)
            except Exception as e:
                logger.warning(f"PDF page extraction failed: {e}. Using original PDF.")
                pdf_cut_path = file_path
            # Convert cut PDF to DOCX
            pdf_docx_path = os.path.splitext(file_path)[0] + '_converted.docx'
            cv = Converter(pdf_cut_path)
            cv.convert(pdf_docx_path, start=0, end=None)
            cv.close()
            word_handler = WordHandler(pdf_docx_path, file_context)
            logger.info(f"PDF cut to 2 pages (or less) and converted to DOCX for preview: {pdf_docx_path}")
        else:
            word_handler = WordHandler(file_path, file_context)

        # Add debugging
        logger.info(f"Creating preview document for language: {target_language}")
        logger.info(f"File path: {file_path}")
        logger.info(f"File context: {file_context[:200] if file_context else 'None'}...")

        preview_doc_path = word_handler.create_preview_document(target_language, max_paragraphs=25)

        logger.info(f"Preview document created at: {preview_doc_path}")

        if not preview_doc_path or not os.path.exists(preview_doc_path):
            return jsonify({'error': 'Failed to create preview document'}), 500

        # Store the preview document path in session for cleanup
        session_preview_docs = session.get('preview_documents', [])
        session_preview_docs.append(preview_doc_path)
        session['preview_documents'] = session_preview_docs

        # Serve the preview Word document with cache-busting headers
        from flask import make_response
        import time

        response = make_response(send_file(
            preview_doc_path,
            mimetype='application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            as_attachment=False,
            download_name=f'preview_{target_language}_{int(time.time())}.docx'
        ))

        # Add cache-busting headers
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'

        logger.info(f"Serving translated preview document: {preview_doc_path}")
        return response

    except Exception as e:
        current_app.logger.error(f"Word document preview error: {e}")
        import traceback
        current_app.logger.error(f"Full traceback: {traceback.format_exc()}")
        return jsonify({'error': 'Failed to create Word document preview'}), 500


@translator_bot_routes.route('/api/preview-translated-word', methods=['GET'])
@login_epr
def preview_translated_word_document():
    """Serve translated Word document for preview"""
    try:
        user = session.get('user')
        if not user:
            return jsonify({'error': 'User not authenticated'}), 401
        user_id = user['id']

        # Get the language parameter
        language = request.args.get('lang')
        if not language:
            return jsonify({'error': 'Language parameter required'}), 400

        # Get the uploaded file info from session
        uploaded_file_info = session.get('uploaded_file_info', {})
        secure_filename_val = uploaded_file_info.get('secure_filename')
        file_extension = uploaded_file_info.get('file_extension')

        if not secure_filename_val or file_extension not in ['.docx', '.pdf']:
            return jsonify({'error': 'No Word document found'}), 404

        # Get the translated file path
        upload_dir = get_translator_upload_dir(user_id)

        # Generate the translated filename
        original_base, original_ext = os.path.splitext(secure_filename_val)
        if original_ext == '.pdf':
            # PDF files are converted to DOCX during translation
            translated_filename = f"{original_base}_{language}.docx"
        else:
            translated_filename = f"{original_base}_{language}{original_ext}"

        translated_file_path = os.path.join(upload_dir, translated_filename)

        if not os.path.exists(translated_file_path):
            return jsonify({'error': f'Translated document for {language} not found. Please complete translation first.'}), 404

        # Serve the translated Word document file
        return send_file(
            translated_file_path,
            mimetype='application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            as_attachment=False
        )

    except Exception as e:
        current_app.logger.error(f"Translated Word preview error: {e}")
        return jsonify({'error': 'Failed to load translated Word document'}), 500


@translator_bot_routes.route('/api/changelog/preview', methods=['GET'])
@login_epr
def get_changelog_preview():
    """Get changelog preview for translator bot"""
    try:
        from src.backend.utils.sys_utils import stream_last_n_releases
        import os

        # Look for changelog file
        changelog_path = os.path.join(os.path.dirname(__file__), '..', '..', '..', '..', 'CHANGELOG.md')
        if os.path.exists(changelog_path):
            preview = stream_last_n_releases(changelog_path, 2)
            return preview, 200, {'Content-Type': 'text/plain; charset=utf-8'}
        else:
            return 'Changelog not found', 404
    except Exception as e:
        current_app.logger.error(f"Changelog error: {e}")
        return 'Error loading changelog', 500


@translator_bot_routes.route('/api/preview-powerpoint-document', methods=['POST'])
@login_epr
def preview_powerpoint_document():
    """Create and serve a preview PowerPoint document with translated sample content"""
    try:
        user = session.get('user')
        if not user:
            return jsonify({'error': 'User not authenticated'}), 401
        user_id = user['id']

        # Get request data
        data = request.get_json() or {}
        target_language = data.get('target_language')
        file_context = data.get('file_context', '').strip()

        if not target_language:
            return jsonify({'error': 'Target language is required'}), 400

        # Get the uploaded file info from session
        uploaded_file_info = session.get('uploaded_file_info', {})
        secure_filename_val = uploaded_file_info.get('secure_filename')
        file_extension = uploaded_file_info.get('file_extension')

        if not secure_filename_val or file_extension != '.pptx':
            return jsonify({'error': 'No PowerPoint document found'}), 404

        # Get the original file path
        upload_dir = get_translator_upload_dir(user_id)
        file_path = os.path.join(upload_dir, secure_filename_val)

        if not os.path.exists(file_path):
            return jsonify({'error': 'Original document not found'}), 404

        # Check if AI is ready
        if not current_app.config.get("AI_READY", False):
            return jsonify({
                'error': "Hang tight… AI services are still sipping their morning coffee ☕️, please try again shortly!"
            }), 503

        # Create preview document using PowerPointHandler
        from src.backend.blueprints.translator_bot.pptx_handler import PPTXHandler

        ppt_handler = PPTXHandler(file_path, file_context)

        logger.info(f"Creating PowerPoint preview document for language: {target_language}")
        preview_ppt_path = ppt_handler.create_preview_presentation(target_language, max_slides=3)

        if not preview_ppt_path or not os.path.exists(preview_ppt_path):
            return jsonify({'error': 'Failed to create preview PowerPoint document'}), 500

        # Store preview path in session for cleanup
        session_preview_docs = session.get('preview_documents', [])
        session_preview_docs.append(preview_ppt_path)
        session['preview_documents'] = session_preview_docs

        # Serve the preview PowerPoint document with cache-busting headers
        from flask import make_response
        import time
        
        # image_dir = os.path.join(upload_dir, f"preview_images")
        # max_slides = 2
        # image_paths = ppt_handler.pptx_to_images(preview_ppt_path, upload_dir, max_slides)
        html_content = ppt_handler.convert_pptx_to_html(preview_ppt_path)

        response = make_response(send_file(
            preview_ppt_path,
            mimetype='application/vnd.openxmlformats-officedocument.presentationml.presentation',
            as_attachment=False,
            download_name=f'preview_{target_language}_{int(time.time())}.pptx'
        ))

        # Add cache-busting headers
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'

        logger.info(f"Serving translated PowerPoint preview document: {preview_ppt_path}")
        # image_urls = [f"/uploads/{user_id}/preview/{os.path.basename(p)}" for p in image_paths]

        return  jsonify({'preview_ppt_path': html_content})

    except Exception as e:
        current_app.logger.error(f"PowerPoint preview error: {e}")
        import traceback
        current_app.logger.error(f"Full traceback: {traceback.format_exc()}")
        return jsonify({'error': 'Failed to create PowerPoint preview'}), 500


@translator_bot_routes.route('/api/preview-translated-powerpoint', methods=['GET'])
@login_epr
def preview_translated_powerpoint_document():
    """Serve translated PowerPoint document for preview"""
    try:
        user = session.get('user')
        if not user:
            return jsonify({'error': 'User not authenticated'}), 401
        user_id = user['id']

        # Get the language parameter
        language = request.args.get('lang')
        if not language:
            return jsonify({'error': 'Language parameter required'}), 400

        # Get uploaded file info from session
        uploaded_file_info = session.get('uploaded_file_info', {})
        secure_filename_val = uploaded_file_info.get('secure_filename')
        file_extension = uploaded_file_info.get('file_extension')

        if not secure_filename_val or file_extension != '.pptx':
            return jsonify({'error': 'No PowerPoint document found'}), 404

        # Get the translated file path
        upload_dir = get_translator_upload_dir(user_id)
        original_base, _ = os.path.splitext(secure_filename_val)

        translated_filename = f"{original_base}_{language}.pptx"
        translated_file_path = os.path.join(upload_dir, translated_filename)

        if not os.path.exists(translated_file_path):
            return jsonify({'error': f'Translated PowerPoint for {language} not found. Please complete translation first.'}), 404

        # Serve the translated PowerPoint file
        return send_file(
            translated_file_path,
            mimetype='application/vnd.openxmlformats-officedocument.presentationml.presentation',
            as_attachment=False
        )

    except Exception as e:
        current_app.logger.error(f"Translated PowerPoint preview error: {e}")
        return jsonify({'error': 'Failed to load translated PowerPoint document'}), 500


@translator_bot_routes.route('/api/generate-prompt', methods=['POST'])
@login_epr
def generate_prompt():
    """Generate a contextual prompt based on uploaded file analysis"""
    try:
        user = session.get('user')
        if not user:
            return jsonify({'error': 'User not authenticated'}), 401
        user_id = user['id']

        # Get request data
        data = request.get_json() or {}
        target_languages = data.get('target_languages', [])

        # Get the uploaded file info from session
        uploaded_file_info = session.get('uploaded_file_info', {})
        secure_filename_val = uploaded_file_info.get('secure_filename')
        original_filename = uploaded_file_info.get('original_filename')

        if not secure_filename_val:
            return jsonify({'error': 'No file uploaded'}), 400

        # Get the file path
        upload_dir = get_translator_upload_dir(user_id)
        file_path = os.path.join(upload_dir, secure_filename_val)

        if not os.path.exists(file_path):
            return jsonify({'error': 'Uploaded file not found'}), 404

        # Generate prompt using the PromptGenerator
        prompt_generator = PromptGenerator()
        generated_prompt = prompt_generator.generate_prompt(
            file_path=file_path,
            target_languages=target_languages,
            original_filename=original_filename
        )

        # Get file analysis for debugging
        file_analysis = prompt_generator._analyze_file(file_path, original_filename)
        is_ees = prompt_generator._is_ees_report(file_analysis)

        # Log analysis results for debugging
        current_app.logger.info(f"File analysis for {original_filename}:")
        current_app.logger.info(f"  - File type: {file_analysis.get('file_type')}")
        current_app.logger.info(f"  - Content sample: {file_analysis.get('content_sample', '')[:100]}...")
        current_app.logger.info(f"  - Columns: {file_analysis.get('columns', [])[:5]}")
        current_app.logger.info(f"  - Is EES report: {is_ees}")

        return jsonify({
            'success': True,
            'prompt': generated_prompt,
            'is_ees_report': is_ees,
            'debug_info': {
                'file_type': file_analysis.get('file_type'),
                'content_sample_length': len(file_analysis.get('content_sample', '')),
                'has_columns': bool(file_analysis.get('columns')),
                'column_count': len(file_analysis.get('columns', []))
            }
        })

    except Exception as e:
        current_app.logger.error(f"Prompt generation error: {e}")
        return jsonify({'error': 'Failed to generate prompt'}), 500
