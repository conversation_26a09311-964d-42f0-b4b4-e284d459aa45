import requests, uuid
from config.config import EproExcelLaConfig
from utils.core import Singleton
from utils.core import get_logger

logger = get_logger(__file__)

class TextTranslator(metaclass=Singleton):

    def __init__(self) -> None:
        config = EproExcelLaConfig()

        self.endpoint = config.translator_llm_endpoint
        self.key = config.translator_llm_key

    def __clean_translation_output(self, translation, to_lang):
        """
            Once translated, the data is returned in a complex and
            structured way to use the translations obtained,
            for this reason it is necessary to use this function which
            returns a simple array containing the translations.
        """
        n_langs = len(to_lang)
        clean_translation = [[] for _ in range(n_langs)]

        if n_langs == 1:
            for item in translation:
                clean_translation[0].append(item['translations'][0]['text'])
        else:
            for item in translation:
                for i in range(n_langs):
                    clean_translation[i].append(item['translations'][i]['text'])

        return clean_translation

    def translate(self, from_lang, to_lang, data_to_translate):
        # location, also known as region.
        # required if you're using a multi-service or regional (not global) resource. It can be found in the Azure portal on the Keys and Endpoint page.

        location = "westeurope"

        path = '/translate'
        constructed_url = self.endpoint + path

        params = {
            'api-version': '3.0',
            'from' : from_lang,
            'to': to_lang
        }
        headers = {
            'Ocp-Apim-Subscription-Key': self.key,
            # location required if you're using a multi-service or regional (not global) resource.
            'Ocp-Apim-Subscription-Region': location,
            'Content-type': 'application/json',
            'X-ClientTraceId': str(uuid.uuid4())
        }

        chunk_dim = 300
        chunks = [data_to_translate[x:x+chunk_dim] for x in range(0, len(data_to_translate), chunk_dim)]

        response = []
        try:
            for chunk in chunks:
                request = requests.post(constructed_url, params=params, headers=headers, json=chunk)
                tmp_response = request.json()
                print(tmp_response)
                response = response + tmp_response
            #json_data = json.load(response[0])
        except Exception as e:
            logger.error(e)
            response = response + [{'translations' : [{'text':"Translation service error"}]}]
            to_lang = ["en"]

        response = self.__clean_translation_output(response, to_lang)
        #print(json.dumps(response, sort_keys=True, ensure_ascii=False, indent=4, separators=(',', ': ')))
        return response