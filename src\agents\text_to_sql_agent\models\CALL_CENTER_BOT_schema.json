{"DWH_PUBLIC": {"description": "", "tables": {"DWH_PUBLIC.BOM": {"schema": "CREATE TABLE DWH_PUBLIC.BOM (\n\tCOMPANY_CODE NVARCHAR2(5),\n\tCOMPANY_DESCRIPTION NVARCHAR2(50),\n\tPLANT_CODE NVARCHAR2(12),\n\tPLANT_DESCRIPTION NVARCHAR2(50),\n\tCHILD_ITEM_CODE NVARCHAR2(25),\n\tPARENT_ITEM_CODE NVARCHAR2(25),\n\tQUANTITY NUMBER(25, 0),\n\tITEM_UNIT_OF_MEASURE NVARCHAR2(2),\n\tITEM_UNIT_OF_MEASURE_DESCRIPTION NVARCHAR2(500),\n\tSTART_PRODUCTION_DATE DATE,\n\tSTOP_PRODUCTION_DATE DATE,\n\t<PERSON><PERSON><PERSON><PERSON> NVARCHAR2(2),\n\tPOSITION NVARCHAR2(8),\n\tSYSTEM_SOURCE NVARCHAR2(10),\n\tSPARE_PART VARCHAR2(30), \n\tPRIMAR<PERSON> (COMPANY_CODE, PLANT_CODE, CHILD_ITEM_CODE, PARENT_ITEM_CODE, MODULE, POSITION, START_PRODUCTION_DATE, STOP_PRODUCTION_DATE)\n);", "aliases": [], "description": "", "example_id": "bom_", "columns": {"COMPANY_CODE": {"aliases": [], "description": ""}, "COMPANY_DESCRIPTION": {"aliases": [], "description": ""}, "PLANT_CODE": {"aliases": [], "description": ""}, "PLANT_DESCRIPTION": {"aliases": [], "description": ""}, "CHILD_ITEM_CODE": {"aliases": [], "description": ""}, "PARENT_ITEM_CODE": {"aliases": [], "description": ""}, "QUANTITY": {"aliases": [], "description": ""}, "ITEM_UNIT_OF_MEASURE": {"aliases": [], "description": ""}, "ITEM_UNIT_OF_MEASURE_DESCRIPTION": {"aliases": [], "description": ""}, "START_PRODUCTION_DATE": {"aliases": [], "description": ""}, "STOP_PRODUCTION_DATE": {"aliases": [], "description": ""}, "MODULE": {"aliases": [], "description": ""}, "POSITION": {"aliases": [], "description": ""}, "SYSTEM_SOURCE": {"aliases": [], "description": ""}, "SPARE_PART": {"aliases": [], "description": ""}}}, "DWH_PUBLIC.COMMERCIAL_PRODUCTS": {"schema": "CREATE TABLE DWH_PUBLIC.COMMERCIAL_PRODUCTS (\n\tINTERNAL_CODE NVARCHAR2(50), -- Always use in OR with DWH_PUBLIC.COMMERCIAL_PRODUCTS.PRODUCT_NUMBER\n\tSHORT_DESCRIPTION NVARCHAR2(1000),\n\tLONG_DESCRIPTION CLOB,\n\tSTART_PRODUCTION_DATE TIMESTAMP (6),\n\tSTOP_PRODUCTION_DATE TIMESTAMP (6),\n\tSTART_DISTRIBUTION_DATE TIMESTAMP (6),\n\tSTOP_DISTRIBUTION_DATE TIMESTAMP (6),\n\tPRODUCTION_STATUS NUMBER(38, 0),\n\tPRODUCTION_STATUS_DESCRIPTION NVARCHAR2(50),\n\tDISTRIBUTION_STATUS NCHAR(1),\n\tDISTRIBUTION_STATUS_DESCRIPTION NVARCHAR2(40),\n\tPLANT_CODE NVARCHAR2(20),\n\tPLANT_DESCRIPTION NVARCHAR2(100),\n\tFACTORY_CODE NVARCHAR2(10),\n\tFACTORY_DESCRIPTION NVARCHAR2(60),\n\tCUSTOM_CLASS NVARCHAR2(40),\n\t<PERSON><PERSON><PERSON>_OF_PRODUCT NVARCHAR2(60),\n\tCOUNTRY_OF_ORIGIN_ISO_CODE NVARCHAR2(8),\n\tCOUNTRY_OF_ORIGIN_DECLARATION_EXPIRY_DATE TIMESTAMP (6),\n\tPREFERENTIAL_ORIGIN NVARCHAR2(2), -- Either 'P' for \"preferential\" or 'C' for \"common\"\n\tPREFERENTIAL_ORIGIN_PERCENTAGE_EU FLOAT(53),\n\tCOO_ISO2 CHAR(2 BYTE),\n\tCOUNTRY_OF_ORIGIN NVARCHAR2(100),\n\tPREFERENTIAL_ORIGIN_PERCENTAGE_EXTRA_EU FLOAT(53),\n\tPRODUCT_NUMBER NVARCHAR2(60), -- Always use in OR with DWH_PUBLIC.COMMERCIAL_PRODUCTS.INTERNAL_CODE\n\tFACTORY_MODEL NVARCHAR2(60),\n\tEXTERNAL_MODEL_NAME NVARCHAR2(60),\n\tLANGUAGE_GROUP NVARCHAR2(10),\n\tIRONER_FRONT_COLOR NVARCHAR2(200),\n\tWASHER_FRONT_COLOR NVARCHAR2(200),\n\tDRYER_FRONT_COLOR NVARCHAR2(100),\n\tIRONER_SIDE_COLOR NVARCHAR2(200),\n\tWASHER_SIDE_COLOR NVARCHAR2(200),\n\tDRYER_SIDE_COLOR NVARCHAR2(100),\n\tELECTRICAL_CONNECTION_VOLTAGE NVARCHAR2(200),\n\tELECTRICAL_CONNECTION_FREQUENCY NVARCHAR2(200),\n\tELECTRICAL_CONNECTION_PHASE NVARCHAR2(200),\n\tHEATING_TYPE NVARCHAR2(64),\n\tWASHER_COINMETER NVARCHAR2(40),\n\tDRYER_COINMETER NVARCHAR2(40),\n\tDRYER_CONTROL_SYSTEM NVARCHAR2(100),\n\tIRONER_CONTROL_SYSTEM NVARCHAR2(200),\n\tWASHER_CONTROL_SYSTEM NVARCHAR2(200),\n\tDRYER_SOFTWARE_CODE NVARCHAR2(100),\n\tWASHER_SOFTWARE_CODE NVARCHAR2(200),\n\tWATER_CONNECTION_TEMPERATURE NVARCHAR2(20),\n\tIRONER_NUMBER_OF_IO_BOARDS FLOAT(126),\n\tWASHER_NUMBER_OF_IO_BOARDS FLOAT(126),\n\tFACTORY_PRODUCT_NUMBER NVARCHAR2(60),\n\tCOMPANY_OWNER NVARCHAR2(40),\n\tBELSCODE NUMBER(3,0),\n\tSFATHER VARCHAR(25),\n\tPRIMARY KEY ( INTERNAL_CODE )\n)", "aliases": [], "description": "", "example_id": "product_", "columns": {"INTERNAL_CODE": {"aliases": [], "description": ""}, "SHORT_DESCRIPTION": {"aliases": [], "description": ""}, "LONG_DESCRIPTION": {"aliases": [], "description": ""}, "START_PRODUCTION_DATE": {"aliases": [], "description": ""}, "STOP_PRODUCTION_DATE": {"aliases": [], "description": ""}, "START_DISTRIBUTION_DATE": {"aliases": [], "description": ""}, "STOP_DISTRIBUTION_DATE": {"aliases": [], "description": ""}, "PRODUCTION_STATUS": {"aliases": [], "description": "Use these values: 0 means 'Draft' status, 1 means 'Preliminary' status, 2 means 'in production' status, 3 means 'out of production' status"}, "PRODUCTION_STATUS_DESCRIPTION": {"aliases": [], "description": ""}, "DISTRIBUTION_STATUS": {"aliases": [], "description": "Use these values: 0 means 'in distribution' status , 5 means 'replaced by' status, 7 means 'in distribution after order' status "}, "DISTRIBUTION_STATUS_DESCRIPTION": {"aliases": [], "description": ""}, "PLANT_CODE": {"aliases": [], "description": ""}, "PLANT_DESCRIPTION": {"aliases": [], "description": ""}, "FACTORY_CODE": {"aliases": [], "description": ""}, "FACTORY_DESCRIPTION": {"aliases": [], "description": ""}, "CUSTOM_CLASS": {"aliases": [], "description": ""}, "KIND_OF_PRODUCT": {"aliases": [], "description": ""}, "COUNTRY_OF_ORIGIN_ISO_CODE": {"aliases": [], "description": ""}, "COUNTRY_OF_ORIGIN_DECLARATION_EXPIRY_DATE": {"aliases": [], "description": ""}, "PREFERENTIAL_ORIGIN": {"aliases": [], "description": ""}, "PREFERENTIAL_ORIGIN_PERCENTAGE_EU": {"aliases": [], "description": ""}, "COO_ISO2": {"aliases": [], "description": ""}, "COUNTRY_OF_ORIGIN": {"aliases": [], "description": ""}, "PREFERENTIAL_ORIGIN_PERCENTAGE_EXTRA_EU": {"aliases": [], "description": ""}, "PRODUCT_NUMBER": {"aliases": [], "description": ""}, "FACTORY_MODEL": {"aliases": [], "description": ""}, "EXTERNAL_MODEL_NAME": {"aliases": [], "description": ""}, "LANGUAGE_GROUP": {"aliases": [], "description": ""}, "IRONER_FRONT_COLOR": {"aliases": [], "description": ""}, "WASHER_FRONT_COLOR": {"aliases": [], "description": ""}, "DRYER_FRONT_COLOR": {"aliases": [], "description": ""}, "IRONER_SIDE_COLOR": {"aliases": [], "description": ""}, "WASHER_SIDE_COLOR": {"aliases": [], "description": ""}, "DRYER_SIDE_COLOR": {"aliases": [], "description": ""}, "ELECTRICAL_CONNECTION_VOLTAGE": {"aliases": [], "description": ""}, "ELECTRICAL_CONNECTION_FREQUENCY": {"aliases": [], "description": ""}, "ELECTRICAL_CONNECTION_PHASE": {"aliases": [], "description": ""}, "HEATING_TYPE": {"aliases": [], "description": ""}, "WASHER_COINMETER": {"aliases": [], "description": ""}, "DRYER_COINMETER": {"aliases": [], "description": ""}, "DRYER_CONTROL_SYSTEM": {"aliases": [], "description": ""}, "IRONER_CONTROL_SYSTEM": {"aliases": [], "description": ""}, "WASHER_CONTROL_SYSTEM": {"aliases": [], "description": ""}, "DRYER_SOFTWARE_CODE": {"aliases": [], "description": ""}, "WASHER_SOFTWARE_CODE": {"aliases": [], "description": ""}, "WATER_CONNECTION_TEMPERATURE": {"aliases": [], "description": ""}, "IRONER_NUMBER_OF_IO_BOARDS": {"aliases": [], "description": ""}, "WASHER_NUMBER_OF_IO_BOARDS": {"aliases": [], "description": ""}, "FACTORY_PRODUCT_NUMBER": {"aliases": [], "description": ""}, "COMPANY_OWNER": {"aliases": [], "description": ""}, "BELSCODE": {"aliases": [], "description": ""}, "SFATHER": {"aliases": [], "description": ""}, "PRODUCT_CAPACITY": {"aliases": [], "description": ""}, "CURRENT_CONSUMPTION": {"aliases": [], "description": ""}, "PRODUCT_DEPTH": {"aliases": [], "description": ""}, "MAXIMUM_CURRENT_CONSUMPTION": {"aliases": [], "description": ""}, "MINIMUM_CURRENT_CONSUMPTION": {"aliases": [], "description": ""}, "GAS_MAX_CONS": {"aliases": [], "description": ""}, "GAS_POWER": {"aliases": [], "description": ""}, "PRODUCT_HEIGHT": {"aliases": [], "description": ""}, "WC_MIN_PRESSURE": {"aliases": [], "description": ""}, "PRODUCT_WEIGHT": {"aliases": [], "description": ""}, "PRODUCT_WIDTH": {"aliases": [], "description": ""}, "CERTIFICATION_GROUP": {"aliases": [], "description": ""}, "EC_PLUG_TYPE": {"aliases": [], "description": ""}, "EC_PREDISPOSED_FOR": {"aliases": [], "description": ""}, "WATER_PROOF_INDEX": {"aliases": [], "description": ""}, "GAS_DELIVERY": {"aliases": [], "description": ""}, "GAS_DELIVERY_PRESSURE": {"aliases": [], "description": ""}, "GAS_INLET_INCH": {"aliases": [], "description": ""}, "SC_GENERATION": {"aliases": [], "description": ""}, "SC_INLET": {"aliases": [], "description": ""}, "WC_INLET_COLD": {"aliases": [], "description": ""}, "WC_INLET_HOT": {"aliases": [], "description": ""}, "WD_OUTLET": {"aliases": [], "description": ""}, "WTYPE": {"aliases": [], "description": ""}, "CONSTRAINT": {"aliases": [], "description": ""}, "USING": {"aliases": [], "description": ""}}}, "DWH_PUBLIC.ITEM_MASTER": {"schema": "CREATE TABLE DWH_PUBLIC.ITEM_MASTER (\n\tCOMPANY_CODE VARCHAR2(12 BYTE),\n\tCOMPANY_DESCRIPTION VARCHAR2(100 BYTE),\n\tPLANT_CODE VARCHAR2(12 BYTE),\n\tPLANT_DESCRIPTION VARCHAR2(50 BYTE),\n\tITEM_CODE VARCHAR2(25 BYTE),\n\tITEM_INTERNAL_CODE VARCHAR2(25 BYTE),\n\tSHORT_ITEM_CODE VARCHAR2(12 BYTE),\n\tMAIN_ITEM_DESCRIPTION VARCHAR2(160 BYTE),\n\tITEM_STOCKING_TYPE VARCHAR2(2 BYTE), -- Either \"M\" for \"manufacture\" or \"B\" for \"buy\"\n\tECO_NUMBER VARCHAR2(16 BYTE),\n\tITEM_PREFERRED_SUPPLIER VARCHAR2(20 BYTE),\n\tITEM_UNIT_OF_MEASURE_CODE VARCHAR2(4 BYTE),\n\tITEM_UNIT_OF_MEASURE_DESCRIPTION VARCHAR2(100 BYTE),\n\tSTART_PRODUCTION_DATE DATE,\n\tSTOP_PRODUCTION_DATE DATE,\n\tCOUNTRY_OF_ORIGIN VARCHAR2(6 BYTE), -- Accepts international vehicle registration codes only\n\tEXPIRING_DATE DATE, -- Country of origin expiration date\n\tPERCENTAGE_NOT_UE NUMBER(5, 0),\n\tFOOD_CONTACT VARCHAR2(2 BYTE),\n\tCURRENCY VARCHAR2(6 BYTE),\n\tSTANDARD_STK3_COST NUMBER(15, 5), -- Cost of production/manufacturing\n\tPURCHASING_COST NUMBER(15, 5),\n\tEXTERNAL_WORK_COST NUMBER(15, 5),\n\tITEM_CODE_VALID VARCHAR2(1 BYTE),\n\tSYSTEM_SOURCE VARCHAR2(10 BYTE),\n\tITEM_SPARE_PARTS_FLAG VARCHAR2(1 BYTE),\n\tDUAL_USE VARCHAR2(2 BYTE),\n\tITEM_TYPE VARCHAR2(2 BYTE),\n\tITEM_WAREHOUSE VARCHAR2(1 BYTE),\n\tTECH_DRAWING NVARCHAR2(40),\n\tPRIMARY KEY (COMPANY_CODE, PLANT_CODE, ITEM_CODE, SYSTEM_SOURCE)\n);", "aliases": [], "description": "", "example_id": "items_", "columns": {"COMPANY_CODE": {"aliases": [], "description": ""}, "COMPANY_DESCRIPTION": {"aliases": [], "description": ""}, "PLANT_CODE": {"aliases": [], "description": ""}, "PLANT_DESCRIPTION": {"aliases": [], "description": ""}, "ITEM_CODE": {"aliases": [], "description": ""}, "ITEM_INTERNAL_CODE": {"aliases": [], "description": ""}, "SHORT_ITEM_CODE": {"aliases": [], "description": ""}, "MAIN_ITEM_DESCRIPTION": {"aliases": [], "description": ""}, "ITEM_STOCKING_TYPE": {"aliases": [], "description": ""}, "ECO_NUMBER": {"aliases": [], "description": ""}, "ITEM_PREFERRED_SUPPLIER": {"aliases": [], "description": ""}, "ITEM_UNIT_OF_MEASURE_CODE": {"aliases": [], "description": ""}, "ITEM_UNIT_OF_MEASURE_DESCRIPTION": {"aliases": [], "description": ""}, "START_PRODUCTION_DATE": {"aliases": [], "description": ""}, "STOP_PRODUCTION_DATE": {"aliases": [], "description": ""}, "COUNTRY_OF_ORIGIN": {"aliases": [], "description": ""}, "EXPIRING_DATE": {"aliases": [], "description": ""}, "PERCENTAGE_NOT_UE": {"aliases": [], "description": ""}, "FOOD_CONTACT": {"aliases": [], "description": ""}, "CURRENCY": {"aliases": [], "description": ""}, "STANDARD_STK3_COST": {"aliases": [], "description": ""}, "PURCHASING_COST": {"aliases": [], "description": ""}, "EXTERNAL_WORK_COST": {"aliases": [], "description": ""}, "ITEM_CODE_VALID": {"aliases": [], "description": ""}, "SYSTEM_SOURCE": {"aliases": [], "description": ""}, "ITEM_SPARE_PARTS_FLAG": {"aliases": [], "description": ""}, "DUAL_USE": {"aliases": [], "description": ""}, "ITEM_TYPE": {"aliases": [], "description": ""}, "ITEM_WAREHOUSE": {"aliases": [], "description": ""}, "TECH_DRAWING": {"aliases": [], "description": ""}}}, "DWH_PUBLIC.SUPPLIER": {"schema": "CREATE TABLE DWH_PUBLIC.SUPPLIER (\n\tSUPPLIER VARCHAR2(10 BYTE),\n\tSUPPLIER_DESCRIPTION VARCHAR2(240 BYTE),\n\tCOMPANY_CODE VARCHAR2(3 BYTE),\n\tCOMPANY_DESCRIPTION VARCHAR2(100 BYTE),\n\tOLD_SUPPLIER VARCHAR2(6 BYTE) DEFAULT ' ',\n\tCURRENCY VARCHAR2(3 BYTE) DEFAULT ' ',\n\tCURRENCY_DESCRIPTION VARCHAR2(100 BYTE),\n\tPURCHASING_STATUS CHAR(1 BYTE) DEFAULT ' ',\n\tADMIN_STATUS CHAR(1 BYTE) DEFAULT ' ',\n\tPAYMENT_CODE VARCHAR2(10 BYTE) DEFAULT ' ',\n\tPAYMENT_DESCRIPTION VARCHAR2(360 BYTE) DEFAULT ' ',\n\tSUPPLIER_CATEGORY VARCHAR2(3 BYTE) DEFAULT ' ',\n\tSUPPLIER_CATEGORY_DESCRIPTION VARCHAR2(400 BYTE) DEFAULT ' ',\n\tLANGUAGE VARCHAR2(2 BYTE) DEFAULT ' ',\n\tLANGUAGE_DESCRIPTION VARCHAR2(100 BYTE) DEFAULT ' ',\n\tEDI_STATUS CHAR(1 BYTE) DEFAULT ' ',\n\tTOP_IDCO VARCHAR2(2 BYTE) DEFAULT ' ',\n\tTOP_IDCO_DESCRIPTION VARCHAR2(400 BYTE) DEFAULT ' ',\n\tSUPPLY_TYPE CHAR(1 BYTE) DEFAULT ' ',\n\tSUPPLIER_STATUS CHAR(1 BYTE) DEFAULT '',\n\tPRIMARY KEY (SUPPLIER, COMPANY_CODE)\n);", "aliases": [], "description": "", "example_id": "suppliers_", "columns": {"SUPPLIER": {"aliases": [], "description": ""}, "SUPPLIER_DESCRIPTION": {"aliases": [], "description": ""}, "COMPANY_CODE": {"aliases": [], "description": ""}, "COMPANY_DESCRIPTION": {"aliases": [], "description": ""}, "OLD_SUPPLIER": {"aliases": [], "description": ""}, "CURRENCY": {"aliases": [], "description": ""}, "CURRENCY_DESCRIPTION": {"aliases": [], "description": ""}, "PURCHASING_STATUS": {"aliases": [], "description": ""}, "ADMIN_STATUS": {"aliases": [], "description": ""}, "PAYMENT_CODE": {"aliases": [], "description": ""}, "PAYMENT_DESCRIPTION": {"aliases": [], "description": ""}, "SUPPLIER_CATEGORY": {"aliases": [], "description": ""}, "SUPPLIER_CATEGORY_DESCRIPTION": {"aliases": [], "description": ""}, "LANGUAGE": {"aliases": [], "description": ""}, "LANGUAGE_DESCRIPTION": {"aliases": [], "description": ""}, "EDI_STATUS": {"aliases": [], "description": ""}, "TOP_IDCO": {"aliases": [], "description": ""}, "TOP_IDCO_DESCRIPTION": {"aliases": [], "description": ""}, "SUPPLY_TYPE": {"aliases": [], "description": ""}, "SUPPLIER_STATUS": {"aliases": [], "description": ""}}}, "DWH_PUBLIC.INSTALLED_BASE": {"schema": "CREATE TABLE DWH_PUBLIC.INSTALLED_BASE (\n\tID_PNC NUMBER(38, 0) NOT NULL ENABLE,\n\tINTERNAL_CODE  NVARCHAR2(80), -- Always use in OR with DWH_PUBLIC.INSTALLED_BASE.PRODUCT_NUMBER\n\tPRODUCT_NUMBER NVARCHAR2(80), -- Always use in OR with DWH_PUBLIC.INSTALLED_BASE.INTERNAL_CODE\n\tSERVICE_PRODUCT_DESCRIPTION NVARCHAR2(960),\n\tSERIAL_NUMBER NVARCHAR2(80),\n\tQC_NUMBER NVARCHAR2(80),\n\tSERVICE_MODEL NVARCHAR2(480),\n\tDATA_SOURCE_INSTALLED_BASE NVARCHAR2(12),\n\tDATA_SOURCE_INSTALLED_BASE_DESCRIPTION NVARCHAR2(400),\n\tSALES_ORDER_NUMBER NVARCHAR2(52),\n\tSALES_ORDER_NUMBER_ROW NUMBER(38, 0),\n\tWARRANTY_CONDITION NVARCHAR2(12),\n\tORDER_BRAND NVARCHAR2(8),\n\t<PERSON>HY<PERSON>CAL_POSITION NVARCHAR2(200),\n\tPHYSICAL_AREA NVARCHAR2(40),\n\tDELIVERY_NOTE_COMPANY NVARCHAR2(12),\n\tDELIVERY_NOTE_DATE DATE,\n\tDELIVERY_NOTE_NUMBER NVARCHAR2(40),\n\tCOMPANY_CODE NVARCHAR2(40),\n\tINVOICE_COMPANY NVARCHAR2(12),\n\tINVOICE_DATE DATE,\n\tINVOICE_NUMBER NVARCHAR2(40),\n\tDISTRIBUTION_STATUS NVARCHAR2(4),\n\tINSTALLATION_DATE DATE,\n\tEND_WARRANTY_COSTS_DATE DATE,\n\tEND_WARRANTY_SPARES_DATE DATE,\n\tINTERNAL_COMMENT NVARCHAR2(1440),\n\tSKILL NVARCHAR2(8),\n\tPRODUCT_LINE NVARCHAR2(12),\n\tCURRENT_CONTRACT_NUMBER NVARCHAR2(80),\n\tSITE_DATA_SOURCE NVARCHAR2(12),\n\tSITE_ID NVARCHAR2(400),\n\tSITE_CODE NVARCHAR2(40),\n\tSITE_NAME NVARCHAR2(1440),\n\tSITE_ADDRESS NVARCHAR2(1440),\n\tSITE_CITY NVARCHAR2(1440), -- Always use UPPER() \n\tSITE_ZIP NVARCHAR2(160),\n\tSITE_PLATE NVARCHAR2(16),\n\tCUSTOMER_DATA_SOURCE NVARCHAR2(12),\n\tCUSTOMER_CODE NVARCHAR2(40),\n\tCUSTOMER_NAME NVARCHAR2(1440),\n\tCUSTOMER_ADDRESS NVARCHAR2(1440),\n\tCUSTOMER_CITY NVARCHAR2(1440),\n\tCUSTOMER_ZIP NVARCHAR2(160),\n\tCUSTOMER_PLATE NVARCHAR2(16),\n\tIDPNC_SITEDI NVARCHAR2(800) NOT NULL ENABLE,\n\tID_CUSTOMER NUMBER,\n\tROW_NUM NUMBER,\n\tSITE_PLAT_DESCRIPTION NVARCHAR2(800),\n\tCUSTOMER_PLATE_DESCRIPTION NVARCHAR2(800),\n\tWARRANTY_CONDITION_DESCRIPTION NVARCHAR2(1000),\n\tBRAND_DESCRIPTION NVARCHAR2(1000),\n\tACTIVE_MACHINE NVARCHAR2(1000),\n\tSKILL_DESCRIPTION NVARCHAR2(1000),\n\tELX_CST NVARCHAR2(1000),\n\tELX_CST_DESCRIPTION NVARCHAR2(1000),\n\tDIRECT_SERVICE NVARCHAR2(1000),\n\tSITE_COUNTRY_ISO2 NVARCHAR2(1000), -- Always use UPPER() \n\tSITE_COUNTRY_ISO2_DESCRIPTION NVARCHAR2(1000)\n\tPRODUCT_LINE_DESCRIPTION NVARCHAR2(1000),\n\tCONSTRAINT PK_IDPNC_SITEDI PRIMARY KEY (IDPNC_SITEDI) USING INDEX ENABLE\n);", "aliases": [], "description": "", "example_id": "installed-base_", "columns": {"ID_PNC": {"aliases": [], "description": ""}, "INTERNAL_CODE": {"aliases": [], "description": ""}, "PRODUCT_NUMBER": {"aliases": [], "description": ""}, "SERVICE_PRODUCT_DESCRIPTION": {"aliases": [], "description": ""}, "SERIAL_NUMBER": {"aliases": [], "description": ""}, "QC_NUMBER": {"aliases": [], "description": ""}, "SERVICE_MODEL": {"aliases": [], "description": ""}, "DATA_SOURCE_INSTALLED_BASE": {"aliases": [], "description": ""}, "DATA_SOURCE_INSTALLED_BASE_DESCRIPTION": {"aliases": [], "description": ""}, "SALES_ORDER_NUMBER": {"aliases": [], "description": ""}, "SALES_ORDER_NUMBER_ROW": {"aliases": [], "description": ""}, "WARRANTY_CONDITION": {"aliases": [], "description": ""}, "ORDER_BRAND": {"aliases": [], "description": ""}, "PHYSICAL_POSITION": {"aliases": [], "description": ""}, "PHYSICAL_AREA": {"aliases": [], "description": ""}, "DELIVERY_NOTE_COMPANY": {"aliases": [], "description": ""}, "DELIVERY_NOTE_DATE": {"aliases": [], "description": ""}, "DELIVERY_NOTE_NUMBER": {"aliases": [], "description": ""}, "COMPANY_CODE": {"aliases": [], "description": ""}, "INVOICE_COMPANY": {"aliases": [], "description": ""}, "INVOICE_DATE": {"aliases": [], "description": ""}, "INVOICE_NUMBER": {"aliases": [], "description": ""}, "DISTRIBUTION_STATUS": {"aliases": [], "description": ""}, "INSTALLATION_DATE": {"aliases": [], "description": ""}, "END_WARRANTY_COSTS_DATE": {"aliases": [], "description": ""}, "END_WARRANTY_SPARES_DATE": {"aliases": [], "description": ""}, "INTERNAL_COMMENT": {"aliases": [], "description": ""}, "SKILL": {"aliases": [], "description": ""}, "PRODUCT_LINE": {"aliases": [], "description": ""}, "CURRENT_CONTRACT_NUMBER": {"aliases": [], "description": ""}, "SITE_DATA_SOURCE": {"aliases": [], "description": ""}, "SITE_ID": {"aliases": [], "description": ""}, "SITE_CODE": {"aliases": [], "description": ""}, "SITE_NAME": {"aliases": [], "description": ""}, "SITE_ADDRESS": {"aliases": [], "description": ""}, "SITE_CITY": {"aliases": [], "description": ""}, "SITE_ZIP": {"aliases": [], "description": ""}, "SITE_PLATE": {"aliases": [], "description": ""}, "CUSTOMER_DATA_SOURCE": {"aliases": [], "description": ""}, "CUSTOMER_CODE": {"aliases": [], "description": ""}, "CUSTOMER_NAME": {"aliases": [], "description": ""}, "CUSTOMER_ADDRESS": {"aliases": [], "description": ""}, "CUSTOMER_CITY": {"aliases": [], "description": ""}, "CUSTOMER_ZIP": {"aliases": [], "description": ""}, "CUSTOMER_PLATE": {"aliases": [], "description": ""}, "IDPNC_SITEDI": {"aliases": [], "description": ""}, "ID_CUSTOMER": {"aliases": [], "description": ""}, "ROW_NUM": {"aliases": [], "description": ""}, "SITE_PLAT_DESCRIPTION": {"aliases": [], "description": ""}, "CUSTOMER_PLATE_DESCRIPTION": {"aliases": [], "description": ""}, "WARRANTY_CONDITION_DESCRIPTION": {"aliases": [], "description": ""}, "BRAND_DESCRIPTION": {"aliases": [], "description": ""}, "ACTIVE_MACHINE": {"aliases": [], "description": ""}, "SKILL_DESCRIPTION": {"aliases": [], "description": ""}, "ELX_CST": {"aliases": [], "description": ""}, "ELX_CST_DESCRIPTION": {"aliases": [], "description": ""}, "DIRECT_SERVICE": {"aliases": [], "description": ""}, "SITE_COUNTRY_ISO2": {"aliases": [], "description": ""}, "SITE_COUNTRY_ISO2_DESCRIPTION": {"aliases": [], "description": ""}, "CONSTRAINT": {"aliases": [], "description": ""}}}, "DWH_PUBLIC.SERIAL_NUMBERS": {"schema": "CREATE TABLE DWH_PUBLIC.SERIAL_NUMBERS (\n\tCOMPANY VARCHAR2(5 BYTE),\n\tCOMPANY_DESCRIPTION VARCHAR2(50 BYTE),\n\tPLANT VARCHAR2(12 BYTE),\n\tPLANT_DESCRIPTION VARCHAR2(50 BYTE),\n\tITEM_CODE VARCHAR2(25 BYTE),\n\tITEM_INTERNAL_CODE VARCHAR2(10 BYTE),\n\tSHORT_ITEM_CODE VARCHAR2(10 BYTE),\n\tMAIN_ITEM_DESCRIPTION VARCHAR2(160 BYTE),\n\tITEM_SERIAL_NUMBER VARCHAR2(30 BYTE),\n\tITEM_PRODUCED_DATE DATE,\n\tITEM_PRODUCED_WORKORDER_NUMBER VARCHAR2(8 BYTE),\n\tITEM_PRODUCED_CUTOMER_ORDER_NUMBER VARCHAR2(13 BYTE),\n\tITEM_PRODUCED_CUTOMER_ORDER_LINE VARCHAR2(10 BYTE),\n\tITEM_PRODUCED_QUANTITY NUMBER(10, 0),\n\tSYSTEM_SOURCE VARCHAR2(10 BYTE),\n\tPRIMARY <PERSON> (COMPANY, PLANT, <PERSON>EM_CODE, ITEM_SERIAL_NUMBER, SYSTEM_SOURCE)\n);", "aliases": [], "description": "", "example_id": "machine-sn-pnc-model_", "columns": {"COMPANY": {"aliases": [], "description": ""}, "COMPANY_DESCRIPTION": {"aliases": [], "description": ""}, "PLANT": {"aliases": [], "description": ""}, "PLANT_DESCRIPTION": {"aliases": [], "description": ""}, "ITEM_CODE": {"aliases": [], "description": ""}, "ITEM_INTERNAL_CODE": {"aliases": [], "description": ""}, "SHORT_ITEM_CODE": {"aliases": [], "description": ""}, "MAIN_ITEM_DESCRIPTION": {"aliases": [], "description": ""}, "ITEM_SERIAL_NUMBER": {"aliases": [], "description": ""}, "ITEM_PRODUCED_DATE": {"aliases": [], "description": ""}, "ITEM_PRODUCED_WORKORDER_NUMBER": {"aliases": [], "description": ""}, "ITEM_PRODUCED_CUTOMER_ORDER_NUMBER": {"aliases": [], "description": ""}, "ITEM_PRODUCED_CUTOMER_ORDER_LINE": {"aliases": [], "description": ""}, "ITEM_PRODUCED_QUANTITY": {"aliases": [], "description": ""}, "SYSTEM_SOURCE": {"aliases": [], "description": ""}}}, "DWH_PUBLIC.SALES_ORDER_VIEW": {"schema": "CREATE TABLE DWH_PUBLIC.SALES_ORDER_VIEW(\nORDER_COMPANY NVARCHAR2(10) NOT NULL ENABLE,\nORDER_YEAR NUMBER NOT NULL ENABLE,\nORDER_NUMBER NUMBER NOT NULL ENABLE,\nORDER_SOLDTO NVARCHAR2(20),\nORDER_BILLTO NVARCHAR2(20),\nORDER_SHIPTO NVARCHAR2(20),\nORDER_END_USER NVARCHAR2(20),\nORDER_KEY_ACCOUNT NVARCHAR2(20),\nORDER_CHANNEL NVARCHAR2(400),\nORDER_COMMERCIALBRAND NVARCHAR2(400),\nORDER_PRODUCTLINE NVARCHAR2(400),\nORDER_ORDERTYPE NVARCHAR2(10),\nORDER_DATE DATE,\nORDER_PRICELISTCOMPANY NVARCHAR2(20),\nORDER_PRICELISTCODE NVARCHAR2(20),\nOR<PERSON>R_CURRENCY NVARCHAR2(10),\nORDER_EXCHANGERATE NUMBER(12,6),\nORDER_BRAND NVARCHAR2(10),\nORDER_PAYMENTCODE NVARCHAR2(10),\nORDER_PAYMENTCODE_DESCRIPTION NVARCHAR2(360),\nORDER_INTEREST NVARCHAR2(10),\nORDER_BANKFEE NVARCHAR2(10),\nORDER_INSTALLATION NVARCHAR2(10),\nORDER_WARRANTY NVARCHAR2(10),\nORDER_INVOICINGPERIOD NVARCHAR2(400),\nORDER_DISCOUNTAMOUNT NUMBER(13,2),\nORDER_DISCOUNTINVOICED NUMBER(13,2),\nORDER_DISCOUNT_PERCROW NUMBER(5,2),\nORDER_DISCOUNT_PERCENTAGE NUMBER(5,2),\nORDER_SOURCE NVARCHAR2(20),\nORDER_CREDIT_STATUS NVARCHAR2(400),\nORDER_ORDERSTATUS NVARCHAR2(400),\nORDER_DELVRYSTATUS NVARCHAR2(10),\nORDER_INVOICESTATUS NVARCHAR2(10),\nORDER_ROW NUMBER(38,0) NOT NULL ENABLE,\nORDER_ROW_SPLIT_ATP NUMBER(38,0) NOT NULL ENABLE,\nORDER_ROW_SPLIT_DELIVERY NUMBER(38,0) NOT NULL ENABLE,\nORDER_LOT_NUMBER NUMBER(38,0),\nINTERNAL_CODE NVARCHAR2(20),\nORDER_ROW_QUANTITY NUMBER(38,0),\nWH_REQUEST NVARCHAR2(20),\nREQUESTED_DATE_ORIGIN DATE,\nREQUESTED_DATE_ATP DATE,\nDATE_ACCEPTED DATE,\nUNIT_MESURE NVARCHAR2(20),\nGROSS_PRICE NUMBER(17,3),\nCAMPAIGN_PRICE NUMBER(17,3),\nNET_PRICE NUMBER(17,3),\nORDER_CHECK_CREDIT_ENABLED NVARCHAR2(20),\nORDER_SHIP NVARCHAR2(10),\nORDER_DELVRYTERM NVARCHAR2(10),\nORDER_DELVRYTERM_DESCRIPTION NVARCHAR2(400),\nORDER_DELVRYTYPE NVARCHAR2(10),\nORDER_DELVRYTYPE_DESCRIPTION NVARCHAR2(400),\nWARRANTY NVARCHAR2(5),\nPROM_DATE_ATP DATE,\nINVOICE_NUMBER NVARCHAR2(10),\nDELIVERY_NOTE_NUMBER NVARCHAR2(10),\nPRODUCT_NUMBER NVARCHAR2(20)\n)", "aliases": [], "description": "", "example_id": "", "columns": {"ORDER_COMPANY": {"aliases": [], "description": ""}, "ORDER_YEAR": {"aliases": [], "description": ""}, "ORDER_NUMBER": {"aliases": [], "description": ""}, "ORDER_SOLDTO": {"aliases": [], "description": ""}, "ORDER_BILLTO": {"aliases": [], "description": ""}, "ORDER_SHIPTO": {"aliases": [], "description": ""}, "ORDER_END_USER": {"aliases": [], "description": ""}, "ORDER_KEY_ACCOUNT": {"aliases": [], "description": ""}, "ORDER_CHANNEL": {"aliases": [], "description": ""}, "ORDER_COMMERCIALBRAND": {"aliases": [], "description": ""}, "ORDER_PRODUCTLINE": {"aliases": [], "description": ""}, "ORDER_ORDERTYPE": {"aliases": [], "description": ""}, "ORDER_DATE": {"aliases": [], "description": ""}, "ORDER_PRICELISTCOMPANY": {"aliases": [], "description": ""}, "ORDER_PRICELISTCODE": {"aliases": [], "description": ""}, "ORDER_CURRENCY": {"aliases": [], "description": ""}, "ORDER_EXCHANGERATE": {"aliases": [], "description": ""}, "ORDER_BRAND": {"aliases": [], "description": ""}, "ORDER_PAYMENTCODE": {"aliases": [], "description": ""}, "ORDER_PAYMENTCODE_DESCRIPTION": {"aliases": [], "description": ""}, "ORDER_INTEREST": {"aliases": [], "description": ""}, "ORDER_BANKFEE": {"aliases": [], "description": ""}, "ORDER_INSTALLATION": {"aliases": [], "description": ""}, "ORDER_WARRANTY": {"aliases": [], "description": ""}, "ORDER_INVOICINGPERIOD": {"aliases": [], "description": ""}, "ORDER_DISCOUNTAMOUNT": {"aliases": [], "description": ""}, "ORDER_DISCOUNTINVOICED": {"aliases": [], "description": ""}, "ORDER_DISCOUNT_PERCROW": {"aliases": [], "description": ""}, "ORDER_DISCOUNT_PERCENTAGE": {"aliases": [], "description": ""}, "ORDER_SOURCE": {"aliases": [], "description": ""}, "ORDER_CREDIT_STATUS": {"aliases": [], "description": ""}, "ORDER_ORDERSTATUS": {"aliases": [], "description": ""}, "ORDER_DELVRYSTATUS": {"aliases": [], "description": ""}, "ORDER_INVOICESTATUS": {"aliases": [], "description": ""}, "ORDER_ROW": {"aliases": [], "description": ""}, "ORDER_ROW_SPLIT_ATP": {"aliases": [], "description": ""}, "ORDER_ROW_SPLIT_DELIVERY": {"aliases": [], "description": ""}, "ORDER_LOT_NUMBER": {"aliases": [], "description": ""}, "INTERNAL_CODE": {"aliases": [], "description": ""}, "ORDER_ROW_QUANTITY": {"aliases": [], "description": ""}, "WH_REQUEST": {"aliases": [], "description": ""}, "REQUESTED_DATE_ORIGIN": {"aliases": [], "description": ""}, "REQUESTED_DATE_ATP": {"aliases": [], "description": ""}, "DATE_ACCEPTED": {"aliases": [], "description": ""}, "UNIT_MESURE": {"aliases": [], "description": ""}, "GROSS_PRICE": {"aliases": [], "description": ""}, "CAMPAIGN_PRICE": {"aliases": [], "description": ""}, "NET_PRICE": {"aliases": [], "description": ""}, "ORDER_CHECK_CREDIT_ENABLED": {"aliases": [], "description": ""}, "ORDER_SHIP": {"aliases": [], "description": ""}, "ORDER_DELVRYTERM": {"aliases": [], "description": ""}, "ORDER_DELVRYTERM_DESCRIPTION": {"aliases": [], "description": ""}, "ORDER_DELVRYTYPE": {"aliases": [], "description": ""}, "ORDER_DELVRYTYPE_DESCRIPTION": {"aliases": [], "description": ""}, "WARRANTY": {"aliases": [], "description": ""}, "PROM_DATE_ATP": {"aliases": [], "description": ""}, "INVOICE_NUMBER": {"aliases": [], "description": ""}, "DELIVERY_NOTE_NUMBER": {"aliases": [], "description": ""}, "PRODUCT_NUMBER": {"aliases": [], "description": ""}}}, "DWH_PUBLIC.RELATED_SPARE_PARTS": {"schema": "CREATE TABLE DWH_PUBLIC.RELATED_SPARE_PARTS (\n\tINTERNAL_CODE VARCHAR2(25), -- Always use in OR with DWH_PUBLIC.INSTALLED_BASE.PRODUCT_NUMBER\n\tPRODUCT_NUMBER VARCHAR2(30), -- Always use in OR with DWH_PUBLIC.INSTALLED_BASE.INTERNAL_CODE\n\tSPARE_PART_CODE VARCHAR2(50),\n\tFACTORY_MODEL VARCHAR2(50),\n\tFACTORY_CODE VARCHAR2(50),\n\tPRODUCT_NUMBER VARCHAR2(50),\n\tSPARE_PART_CODE VARCHAR2(50),\n\tSPARE_PART_PNC VARCHAR2(50),\n\tSPARE_PART_DESCRIPTION VARCHAR2(50),\n\tQUANTITY VARCHAR2(50)\n);", "aliases": [], "description": "", "example_id": "spare-parts_", "columns": {"INTERNAL_CODE": {"aliases": [], "description": ""}, "PRODUCT_NUMBER": {"aliases": [], "description": ""}, "SPARE_PART_CODE": {"aliases": [], "description": ""}, "FACTORY_MODEL": {"aliases": [], "description": "factory model, always use UPPER() when this field is asked"}, "FACTORY_CODE": {"aliases": [], "description": ""}, "SPARE_PART_PNC": {"aliases": [], "description": ""}, "SPARE_PART_DESCRIPTION": {"aliases": [], "description": ""}, "QUANTITY": {"aliases": [], "description": ""}}}, "DWH_PUBLIC.COMMERCIAL_PRODUCTS_HIERARCHY": {"schema": "CREATE TABLE DWH_PUBLIC.COMMERCIAL_PRODUCTS_HIERARCHY (           \n    INTCODE NVARCHAR2(50) NOT NULL ENABLE,\n    PNC NVARCHAR2(50),\n    L1 NVARCHAR2(50),\n    L1_DESC NVARCHAR2(50),\n    L2 NVARCHAR2(50),\n    L2_DESC NVARCHAR2(50),\n    L3 NVARCHAR2(50),\n    L3_DESC NVARCHAR2(50),\n    AG<PERSON>EG<PERSON>IONCODE NVARCHAR2(50),\n    AGGREGATIONCODE_DESC NVARCHAR2(300),\n    CLASSSUBCLASS NVARCHAR2(50),\n    CLASSSUBCLASS_DESC NVARCHAR2(300),\n    PRODUCTTYPE NVARCHAR2(50),\n    PRODUCTLINE NVARCHAR2(50),\n    FACTCODE NVARCHAR2(30),\n    CONSTRAINT PK_C_P_H PRIMARY KEY (INTCODE)\n    USING INDEX  ENABLE\n);", "aliases": [], "description": "", "example_id": "product_", "columns": {"INTCODE": {"aliases": [], "description": ""}, "PNC": {"aliases": [], "description": ""}, "L1": {"aliases": [], "description": ""}, "L1_DESC": {"aliases": [], "description": ""}, "L2": {"aliases": [], "description": ""}, "L2_DESC": {"aliases": [], "description": ""}, "L3": {"aliases": [], "description": ""}, "L3_DESC": {"aliases": [], "description": ""}, "AGGREGATIONCODE": {"aliases": [], "description": ""}, "AGGREGATIONCODE_DESC": {"aliases": [], "description": ""}, "CLASSSUBCLASS": {"aliases": [], "description": ""}, "CLASSSUBCLASS_DESC": {"aliases": [], "description": ""}, "PRODUCTTYPE": {"aliases": [], "description": ""}, "PRODUCTLINE": {"aliases": [], "description": ""}, "FACTCODE": {"aliases": [], "description": ""}, "CONSTRAINT": {"aliases": [], "description": ""}}}, "DWH_PUBLIC.COMMERCIAL_PRODUCTS_DOCUMENTS": {"schema": "CREATE TABLE DWH_PUBLIC.COMMERCIAL_PRODUCTS_DOCUMENTS (\n\tINTERNAL_CODE VARCHAR2(25),\t\t\t\n\tPRODUCT_NUMBER VARCHAR2(30),\t\t\t\n\tFACTORY_MODEL VARCHAR2(60),\t\t\t\n\tCOMMERCIAL_MODEL VARCHAR2(30),\t\t\t\n\tDOCUMENT_TYPE_DESCR VARCHAR2(50),\t\t\n\tDOCUMENT_TYPE VARCHAR2(7),\t\n\tDOCUMENT_NUMBER VARCHAR2(150),\t\t\t\n\tDOCUMENT_EDITION VARCHAR2(15),\t\t\t\n\tDOCUMENT_DATE DATE(7),\t\t\t\n\tID_FILE\tNUMBER(22),\t\n\tFILE_DESCR VARCHAR2(200),\t\t\t\n\tFILE_PATH VARCHAR2(255),\t\t\n\tFILE_SIZE NUMBER(22),\t\n\tLANGUAGE NUMBER(22),\t\n\tLANGUAGE_DESCR VARCHAR2(30),\t\t\n\tDOCUMENT_BRAND VARCHAR2(50),\t\t\t\n\tDOCUMENT_STATUS\tNUMBER(22),\t\t\n\tDOCUMENT_TITLE VARCHAR2(255),\t\t\t\n\tIS_DOCUMENT_PUBLIC NUMBER(22),\n\tDOCUMENT_DESCR VARCHAR2(2000),\n\tALIAS VARCHAR2(30),\n\tCERTIFICATE_TYPE VARCHAR2(30),\n\tFACTORY VARCHAR2(5),\n\tPLATFORM VARCHAR2(50),\n\tOWNER VARCHAR2(30),\n\tSUPPLIERCODE VARCHAR2(50),\n\tSUPPLIER VARCHAR2(100),\n\tCLASS VARCHAR2(50),\n\tFAMILY VARCHAR2(50),\n\tTYPE VARCHAR2(50),\n\tELSCODE VARCHAR2(20),\n\tPRODUCT_LINE VARCHAR2(100)\n);", "aliases": [], "description": "The table COMMERCIAL_PRODUCTS_DOCUMENTS has the information about all the documents related to commercial products. very important is the column document type, here a glossary of acronyms of document types:  'Service Manual': 'SM','SMA' \n 'Installation Manual' : 'IN' \n 'Handbooks' : 'HB' \n 'Spare Part Catalog' : 'SPC' \n Technical Bulletin : 'TB', 'TBELS' \n Technical Information : 'TI' \n Installation Instruction : 'II' \n Product Data Sheet : 'PDS' \n Spare Part Product Data Sheet : 'SPPDS' \n Spare Part Photo : 'SPPH' \n Spare Part Original Component Sticker : 'SPOCS' \n Operating Manual : 'OM' \n Operating Instruction : 'OI' \n Commissioning & performance maintenance : 'CPM','CPMA' \n User maintenance : 'UM','UMG' \n Product Wiring Diagram : 'PWD'\n Electrical Wiring Diagram : 'EWD' \n Conformity Certificates : 'COC' \n Conformity Declaration : 'DC','DOC' \n Programming manual : 'PM' \n Technical videos : 'TEVI', 'TVID' \n Spare Part General Purpose : 'SPGP' \n Spare Part Instruction : 'SPI' \n Spare Part Technical videos : 'SPVID' \n Spare Parts Instructions for Kit : 'SPIK' \n Instructions: 'IS'\n Guidelines : 'GUI','GUL' \n Wall instruction : 'WI'", "example_id": "commercial-products-documents_", "columns": {"INTERNAL_CODE": {"aliases": [], "description": ""}, "PRODUCT_NUMBER": {"aliases": [], "description": ""}, "FACTORY_MODEL": {"aliases": [], "description": ""}, "COMMERCIAL_MODEL": {"aliases": [], "description": ""}, "DOCUMENT_TYPE_DESCR": {"aliases": [], "description": ""}, "DOCUMENT_TYPE": {"aliases": [], "description": "Can assume the following values:  'Service Manual': 'SM','SMA' \n 'Installation Manual' : 'IN' \n 'Handbooks' : 'HB' \n 'Spare Part Catalog' : 'SPC' \n Technical Bulletin : 'TB', 'TBELS' \n Technical Information : 'TI' \n Installation Instruction : 'II' \n Product Data Sheet : 'PDS' \n Spare Part Product Data Sheet : 'SPPDS' \n Spare Part Photo : 'SPPH' \n Spare Part Original Component Sticker : 'SPOCS' \n Operating Manual : 'OM' \n Operating Instruction : 'OI' \n Commissioning & performance maintenance : 'CPM','CPMA' \n User maintenance : 'UM','UMG' \n Product Wiring Diagram : 'PWD'\n Electrical Wiring Diagram : 'EWD' \n Conformity Certificates : 'COC' \n Conformity Declaration : 'DC','DOC' \n Programming manual : 'PM' \n Technical videos : 'TEVI', 'TVID' \n Spare Part General Purpose : 'SPGP' \n Spare Part Instruction : 'SPI' \n Spare Part Technical videos : 'SPVID' \n Spare Parts Instructions for Kit : 'SPIK' \n Instructions: 'IS'\n Guidelines : 'GUI','GUL' \n Wall instruction : 'WI' \n Programming File: 'PRF'"}, "DOCUMENT_NUMBER": {"aliases": [], "description": ""}, "DOCUMENT_EDITION": {"aliases": [], "description": ""}, "DOCUMENT_DATE": {"aliases": [], "description": ""}, "ID_FILE": {"aliases": [], "description": ""}, "FILE_DESCR": {"aliases": [], "description": ""}, "FILE_PATH": {"aliases": [], "description": ""}, "FILE_SIZE": {"aliases": [], "description": ""}, "LANGUAGE": {"aliases": [], "description": ""}, "LANGUAGE_DESCR": {"aliases": [], "description": ""}, "DOCUMENT_BRAND": {"aliases": [], "description": ""}, "DOCUMENT_STATUS": {"aliases": [], "description": "Assumes the following values: 0,1,2,3,4.\n 0: DRAFT; 1: In production and in distribution;\n 2: Out of production and out of distribution;\n 3: In production and out of distribution;\n 4: Out of production and in distribution "}, "DOCUMENT_TITLE": {"aliases": [], "description": ""}, "IS_DOCUMENT_PUBLIC": {"aliases": [], "description": ""}, "DOCUMENT_DESCR": {"aliases": [], "description": ""}, "ALIAS": {"aliases": [], "description": ""}, "CERTIFICATE_TYPE": {"aliases": [], "description": ""}, "FACTORY": {"aliases": [], "description": ""}, "PLATFORM": {"aliases": [], "description": ""}, "OWNER": {"aliases": [], "description": ""}, "SUPPLIERCODE": {"aliases": [], "description": ""}, "SUPPLIER": {"aliases": [], "description": ""}, "CLASS": {"aliases": [], "description": "refers to the PRODUCT CLASS"}, "FAMILY": {"aliases": [], "description": "refers to the PRODUCT FAMILY"}, "TYPE": {"aliases": [], "description": "refers to the PRODUCT TYPE"}, "ELSCODE": {"aliases": [], "description": ""}, "PRODUCT_LINE": {"aliases": [], "description": ""}}}, "DWH_PUBLIC.BOM_PRMS": {"schema": "CREATE TABLE DWH_PUBLIC.BOM_PRMS (\n\tCOMPANY_CODE NVARCHAR2(5),\n\tCOMPANY_DESCRIPTION NVARCHAR2(50),\n\tPLANT_CODE NVARCHAR2(12),\n\tPLANT_DESCRIPTION NVARCHAR2(50),\n\tCHILD_ITEM_CODE NVARCHAR2(25),\n\tPARENT_ITEM_CODE NVARCHAR2(25),\n\tQUANTITY NUMBER(25, 0),\n\tITEM_UNIT_OF_MEASURE NVARCHAR2(2),\n\tITEM_UNIT_OF_MEASURE_DESCRIPTION NVARCHAR2(500),\n\tSTART_PRODUCTION_DATE DATE,\n\tSTOP_PRODUCTION_DATE DATE,\n\t<PERSON><PERSON><PERSON><PERSON> NVARCHAR2(2),\n\tPOSITION NVARCHAR2(8),\n\tSYSTEM_SOURCE NVARCHAR2(10),\n\tPARENT_ITEM_DESCR NVARCHAR2(500),\n\tCHILD_ITEM_DESCR NVARCHAR2(500),\n\t<PERSON><PERSON><PERSON><PERSON> (COMPANY_CODE, P<PERSON>NT_CODE, CH<PERSON>D_ITEM_CODE, PARENT_ITEM_CODE, MODULE, POSITION, START_PRODUCTION_D<PERSON>E, STOP_PRODUCTION_DATE)\n);", "aliases": [], "description": "", "example_id": "bom_", "columns": {"COMPANY_CODE": {"aliases": [], "description": ""}, "COMPANY_DESCRIPTION": {"aliases": [], "description": ""}, "PLANT_CODE": {"aliases": [], "description": ""}, "PLANT_DESCRIPTION": {"aliases": [], "description": ""}, "CHILD_ITEM_CODE": {"aliases": [], "description": ""}, "PARENT_ITEM_CODE": {"aliases": [], "description": ""}, "QUANTITY": {"aliases": [], "description": ""}, "ITEM_UNIT_OF_MEASURE": {"aliases": [], "description": ""}, "ITEM_UNIT_OF_MEASURE_DESCRIPTION": {"aliases": [], "description": ""}, "START_PRODUCTION_DATE": {"aliases": [], "description": ""}, "STOP_PRODUCTION_DATE": {"aliases": [], "description": ""}, "MODULE": {"aliases": [], "description": ""}, "POSITION": {"aliases": [], "description": ""}, "SYSTEM_SOURCE": {"aliases": [], "description": ""}, "PARENT_ITEM_DESCR": {"aliases": [], "description": ""}, "CHILD_ITEM_DESCR": {"aliases": [], "description": ""}}}, "DWH_PUBLIC.get_hierarchy_data": {"schema": "CREATE TABLE DWH_PUBLIC.get_hierarchy_data (\n    ID NUMBER,\n    FATHER VARCHAR2(1000),\n    CHILD VARCHAR2(1000),\n    SPARE VARCHAR2(1000),\n    COMPANY_CODE VARCHAR2(1000),\n    PLANT_CODE VARCHAR2(1000),\n    SEP2 VARCHAR2(1),\n    LEVEL_1 VARCHAR2(1000),\n    LEVEL_2 VARCHAR2(1000),\n    LEVEL_3 VARCHAR2(1000),\n    LEVEL_4 VARCHAR2(1000),\n    LEVEL_5 VARCHAR2(1000),\n    LEVEL_6 VARCHAR2(1000),\n    LEVEL_7 VARCHAR2(1000),\n    LEVEL_8 VARCHAR2(1000),\n    LEVEL_9 VARCHAR2(1000),\n    LEVEL_10 VARCHAR2(1000),\n    LEVEL_11 VARCHAR2(1000),\n    LEVEL_12 VARCHAR2(1000),\n    LEVEL_13 VARCHAR2(1000)\n);", "aliases": [], "description": "", "example_id": "spare-parts_", "columns": {"ID": {"aliases": [], "description": ""}, "FATHER": {"aliases": [], "description": ""}, "CHILD": {"aliases": [], "description": ""}, "SPARE": {"aliases": [], "description": ""}, "COMPANY_CODE": {"aliases": [], "description": ""}, "PLANT_CODE": {"aliases": [], "description": ""}, "SEP2": {"aliases": [], "description": ""}, "LEVEL_1": {"aliases": [], "description": ""}, "LEVEL_2": {"aliases": [], "description": ""}, "LEVEL_3": {"aliases": [], "description": ""}, "LEVEL_4": {"aliases": [], "description": ""}, "LEVEL_5": {"aliases": [], "description": ""}, "LEVEL_6": {"aliases": [], "description": ""}, "LEVEL_7": {"aliases": [], "description": ""}, "LEVEL_8": {"aliases": [], "description": ""}, "LEVEL_9": {"aliases": [], "description": ""}, "LEVEL_10": {"aliases": [], "description": ""}, "LEVEL_11": {"aliases": [], "description": ""}, "LEVEL_12": {"aliases": [], "description": ""}, "LEVEL_13": {"aliases": [], "description": ""}}}, "DWH_PUBLIC.SERVICE_JOBS_COMPLETE_LIST": {"schema": "CREATE TABLE DWH_PUBLIC.SERVICE_JOBS_COMPLETE_LIST (\n\tACTIVITY_ID VARCHAR2(10), \n\tSERVICE_PARTNER_AREA VARCHAR2(10), \n\tAREA_MANAGER_CODE VARCHAR2(16), \n\tAREA_MANAGER_NAME VARCHAR2(100), \n\tACTIVITY_TYPE CHAR(1), \n\tACTIVITY_STATUS CHAR(1), \n\tACTIVITY_YEAR NUMBER, \n\tACTIVITY_NUMBER NUMBER, \n\tCALL_DATE VARCHAR2(10), \n\tENGINEER CHAR(3), \n\tACTIVITY_NOTE VARCHAR2(1000), \n\tCUSTOMER_PO_NUMBER CHAR(50), \n\tCUSTOMER_REFERENCE_PERSON VARCHAR2(120), \n\tCUSTOMER_COMPLAIN VARCHAR2(1000), \n\tENGINEER_ACTIVITY_DESCRIPTION VARCHAR2(1000), \n\tACTIVITY_INSERTION_USER CHAR(16), \n\tSTART_ACTIVITY_DATE VARCHAR2(10), \n\tEND_ACTIVITY_DATE VARCHAR2(10), \n\tCONTRACT_NUM CHAR(10), \n\tCONTRACT_WO_NUMBER CHAR(10), \n\tCONTRACT_WO_DATE DATE, \n\tCONTRACT_SWO_NUMBER CHAR(10), \n\tCONTRACT_SWO_DATE DATE, \n\tSERVICE_PARTNER_CODE CHAR(3), \n\tSERVICE_PARTNER_APPROVAL_DATE DATE, \n\tREQUEST_INVOICE_DATE DATE, \n\tWARRANTY_CODE_LABOUR CHAR(2), \n\tWARRANTY_CODE_SPARES CHAR(2), \n\tMACHINE_ERROR_CODE CHAR(10), \n\tMACHINE_RUNNING_HOUR NUMBER, \n\tACTIVITY_INSERTION_DATE DATE, \n\tACTIVITY_PRIORITY CHAR(1), \n\tCUSTOMER_REQUEST_DATE DATE, \n\tMACHINE_YEAR_AGE NUMBER, \n\tENGINEER_AREA VARCHAR2(10), \n\tMACHINE_PRODUCT_LINE VARCHAR2(2), \n\tBRAND VARCHAR2(3), \n\tCOMMISSIONING_TYPE CHAR(1), \n\tMAINTENANCE_CONTRACT_YEAR NUMBER, \n\tMAINTENANCE_CONTRACT_NUMBER NUMBER, \n\tMAINTENANCE_CONTRACT_SUB_NUMBER NUMBER, \n\tMAINTENANCE_CONTRACT_TYPE VARCHAR2(3), \n\tCUSTOMER_CONTACT_NAME VARCHAR2(360), \n\tCUSTOMER_CONTACT_MOBILE VARCHAR2(80), \n\tCUSTOMER_CONTACT_PHONE VARCHAR2(80), \n\tCUSTOMER_CONTACT_EMAIL VARCHAR2(360), \n\tSERVICE_PARTNER_NAME VARCHAR2(400), \n\tSITE_CODE CHAR(10), \n\tSITE_COST_CENTER VARCHAR2(20), \n\tSERVICE_SITE_MOBILE VARCHAR2(80), \n\tSERVICE_SITE_EMAIL VARCHAR2(360), \n\tSERVICE_SITE_PHONE VARCHAR2(80), \n\tSERVICE_SITE_NOTE VARCHAR2(360), \n\tSERVICE_SITE_INTERNAL_NOTE VARCHAR2(360), \n\tSERVICE_SITE_KEY CHAR(40), \n\tCUSTOMER_CODE CHAR(10), \n\tCUSTOMER_CODE_EXT CHAR(20), \n\tCUSTOMER_MOBILE VARCHAR2(80), \n\tCUSTOMER_PHONE VARCHAR2(80), \n\tINVOICE_CUSTOMER_CODE CHAR(10), \n\tINVOICE_CUSTOMER_EXTERNAL_CODE CHAR(20), \n\tINVOICE_CUSTOMER_MOBILE VARCHAR2(80), \n\tINVOICE_CUSTOMER_PHONE VARCHAR2(80), \n\tMACHINE_ID NUMBER, \n\tMACHINE_PRODUCT_CODE VARCHAR2(15), \n\tMACHINE_SERIAL_NUMBER VARCHAR2(20), \n\tMACHINE_QC_NUMBER VARCHAR2(20), \n\tMACHINE_MODEL VARCHAR2(120), \n\tCUSTOMER_ORDER_NUMBER VARCHAR2(13), \n\tMACHINE_END_WARRANTY_SPARES_DATE DATE, \n\tMACHINE_END_WARRANTY_ACTIVITIES DATE, \n\tMACHINE_INSTALLATION_DATE DATE, \n\tMACHINE_PHYSICAL_POSITION VARCHAR2(50), \n\tMACHINE_PHYSICAL_AREA VARCHAR2(10), \n\tMACHINE_NOTE VARCHAR2(360), \n\tQES_ACTION_CODE VARCHAR2(3), \n\tENGINEER_NAME VARCHAR2(360), \n\tMACHINE_FACTORY_CLASSIFICATION_CODE VARCHAR2(43), \n\tMACHINE_FACTORY_CLASSIFICATION_DESCRIPTION VARCHAR2(403), \n\tMACHINE_PM_CLASSIFICATION_CODE VARCHAR2(32), \n\tMACHINE_PM_CLASSIFICATION_DESCRIPTION VARCHAR2(302), \n\tMACHINE_CLASS_AND_SUBCLASS_CLASSIFICATION_CODE VARCHAR2(21), \n\tMACHINE_CLASS_AND_SUBCLASS_CLASSIFICATION_DESCRIPTION VARCHAR2(201), \n\tMACHINE_PNC VARCHAR2(20), \n\tMACHINE_FACTORY_MODEL VARCHAR2(120), \n\tTOTAL_COST_SPARES NUMBER, \n\tTOTAL_COST_LABOUR NUMBER, \n\tTOTAL_REVENUE_SPARES NUMBER, \n\tTOTAL_REVENUE_LABOUR NUMBER, \n\tCONTACT_TITLE VARCHAR2(100), \n\tAPPROVED_COUNTER NUMBER, \n\tNOTAPPROVED_COUNTER VARCHAR2(1), \n\tLAST_APPROVER_NAME VARCHAR2(16), \n\tSITE_NAME VARCHAR2(481), \n\tSITE_ADDRESS VARCHAR2(722), \n\tSITE_CITY VARCHAR2(400), \n\tSITE_ZIP VARCHAR2(40), \n\tSITE_DISTRICT VARCHAR2(16), \n\tSITE_COUNTRY VARCHAR2(4), \n\tCUSTOMER_NAME VARCHAR2(481), \n\tCUSTOMER_ADDRESS VARCHAR2(722), \n\tCUSTOMER_CITY VARCHAR2(400), \n\tCUSTOMER_DISTRICT VARCHAR2(16), \n\tCUSTOMER_COUNTRY VARCHAR2(4), \n\tINVOICE_CUSTOMER_NAME VARCHAR2(481), \n\tINVOICE_CUSTOMER_ADDRESS VARCHAR2(722), \n\tINVOICE_CUSTOMER_CITY VARCHAR2(400), \n\tINVOICE_CUSTOMER_DISTRICT VARCHAR2(16), \n\tINVOICE_CUSTOMER_COUNTRY VARCHAR2(4), \n\tCONTACT_REFERENCE_NAME VARCHAR2(360), \n\tCONTACT_REFERENCE_MOBILE VARCHAR2(80), \n\tCONTACT_REFERENCE_EMAIL VARCHAR2(360), \n\tCONTACT_REFERENCE_ADDRESS VARCHAR2(360), \n\tCONTACT_REFERENCE_CITY VARCHAR2(360), \n\tCONTACT_REFERENCE_ZIP_CODE VARCHAR2(40), \n\tSPARE_CODE VARCHAR2(15), \n\tSPARE_QTY NUMBER, \n\tSPARE_COST_GROSS_UNIT NUMBER, \n\tSPARE_COST_GROSS_TOTAL NUMBER, \n\tSPARE_COST_NET_UNIT NUMBER, \n\tSPARE_COST_NET_TOTAL NUMBER, \n\tSPARE_REVENUE_GROSS_UNIT NUMBER, \n\tSPARE_REVENUE_GROSS_TOTAL NUMBER, \n\tSPARE_REVENUE_NET_UNIT NUMBER, \n\tSPARE_REVENUE_NET_TOTAL NUMBER, \n\tSPARE_STK3_UNIT NUMBER, \n\tSPARE_STK3_TOTAL NUMBER, \n\tSPARE_WARRANTY_CODE VARCHAR2(2), \n\tSPARE_WARRANTY_DESCRIPTION VARCHAR2(100), \n\tSPARE_WAREHOUSE VARCHAR2(3), \n\tSPARE_SENT_TO_OTC_DATE DATE, \n\tACTIVITY_COUNTRY CHAR(10), \n\tACTIVITY_TYPE_DESCRIPTION VARCHAR2(100), \n\tACTIVITY_STATUS_DESCRIPTION VARCHAR2(100), \n\tCURRENT_APPROVER VARCHAR2(16), \n\tPTP_DATE DATE\n);", "aliases": [], "description": "", "example_id": "service-jobs_", "columns": {"ACTIVITY_ID": {"aliases": [], "description": ""}, "SERVICE_PARTNER_AREA": {"aliases": [], "description": ""}, "AREA_MANAGER_CODE": {"aliases": [], "description": ""}, "AREA_MANAGER_NAME": {"aliases": [], "description": ""}, "ACTIVITY_TYPE": {"aliases": [], "description": ""}, "ACTIVITY_STATUS": {"aliases": [], "description": ""}, "ACTIVITY_YEAR": {"aliases": [], "description": ""}, "ACTIVITY_NUMBER": {"aliases": [], "description": ""}, "CALL_DATE": {"aliases": [], "description": ""}, "ENGINEER": {"aliases": [], "description": ""}, "ACTIVITY_NOTE": {"aliases": [], "description": ""}, "CUSTOMER_PO_NUMBER": {"aliases": [], "description": ""}, "CUSTOMER_REFERENCE_PERSON": {"aliases": [], "description": ""}, "CUSTOMER_COMPLAIN": {"aliases": [], "description": ""}, "ENGINEER_ACTIVITY_DESCRIPTION": {"aliases": [], "description": ""}, "ACTIVITY_INSERTION_USER": {"aliases": [], "description": ""}, "START_ACTIVITY_DATE": {"aliases": [], "description": ""}, "END_ACTIVITY_DATE": {"aliases": [], "description": ""}, "CONTRACT_NUM": {"aliases": [], "description": ""}, "CONTRACT_WO_NUMBER": {"aliases": [], "description": ""}, "CONTRACT_WO_DATE": {"aliases": [], "description": ""}, "CONTRACT_SWO_NUMBER": {"aliases": [], "description": ""}, "CONTRACT_SWO_DATE": {"aliases": [], "description": ""}, "SERVICE_PARTNER_CODE": {"aliases": [], "description": ""}, "SERVICE_PARTNER_APPROVAL_DATE": {"aliases": [], "description": ""}, "REQUEST_INVOICE_DATE": {"aliases": [], "description": ""}, "WARRANTY_CODE_LABOUR": {"aliases": [], "description": ""}, "WARRANTY_CODE_SPARES": {"aliases": [], "description": ""}, "MACHINE_ERROR_CODE": {"aliases": [], "description": ""}, "MACHINE_RUNNING_HOUR": {"aliases": [], "description": ""}, "ACTIVITY_INSERTION_DATE": {"aliases": [], "description": ""}, "ACTIVITY_PRIORITY": {"aliases": [], "description": ""}, "CUSTOMER_REQUEST_DATE": {"aliases": [], "description": ""}, "MACHINE_YEAR_AGE": {"aliases": [], "description": ""}, "ENGINEER_AREA": {"aliases": [], "description": ""}, "MACHINE_PRODUCT_LINE": {"aliases": [], "description": ""}, "BRAND": {"aliases": [], "description": ""}, "COMMISSIONING_TYPE": {"aliases": [], "description": ""}, "MAINTENANCE_CONTRACT_YEAR": {"aliases": [], "description": ""}, "MAINTENANCE_CONTRACT_NUMBER": {"aliases": [], "description": ""}, "MAINTENANCE_CONTRACT_SUB_NUMBER": {"aliases": [], "description": ""}, "MAINTENANCE_CONTRACT_TYPE": {"aliases": [], "description": ""}, "CUSTOMER_CONTACT_NAME": {"aliases": [], "description": ""}, "CUSTOMER_CONTACT_MOBILE": {"aliases": [], "description": ""}, "CUSTOMER_CONTACT_PHONE": {"aliases": [], "description": ""}, "CUSTOMER_CONTACT_EMAIL": {"aliases": [], "description": ""}, "SERVICE_PARTNER_NAME": {"aliases": [], "description": ""}, "SITE_CODE": {"aliases": [], "description": ""}, "SITE_COST_CENTER": {"aliases": [], "description": ""}, "SERVICE_SITE_MOBILE": {"aliases": [], "description": ""}, "SERVICE_SITE_EMAIL": {"aliases": [], "description": ""}, "SERVICE_SITE_PHONE": {"aliases": [], "description": ""}, "SERVICE_SITE_NOTE": {"aliases": [], "description": ""}, "SERVICE_SITE_INTERNAL_NOTE": {"aliases": [], "description": ""}, "SERVICE_SITE_KEY": {"aliases": [], "description": ""}, "CUSTOMER_CODE": {"aliases": [], "description": ""}, "CUSTOMER_CODE_EXT": {"aliases": [], "description": ""}, "CUSTOMER_MOBILE": {"aliases": [], "description": ""}, "CUSTOMER_PHONE": {"aliases": [], "description": ""}, "INVOICE_CUSTOMER_CODE": {"aliases": [], "description": ""}, "INVOICE_CUSTOMER_EXTERNAL_CODE": {"aliases": [], "description": ""}, "INVOICE_CUSTOMER_MOBILE": {"aliases": [], "description": ""}, "INVOICE_CUSTOMER_PHONE": {"aliases": [], "description": ""}, "MACHINE_ID": {"aliases": [], "description": ""}, "MACHINE_PRODUCT_CODE": {"aliases": [], "description": ""}, "MACHINE_SERIAL_NUMBER": {"aliases": [], "description": ""}, "MACHINE_QC_NUMBER": {"aliases": [], "description": ""}, "MACHINE_MODEL": {"aliases": [], "description": ""}, "CUSTOMER_ORDER_NUMBER": {"aliases": [], "description": ""}, "MACHINE_END_WARRANTY_SPARES_DATE": {"aliases": [], "description": ""}, "MACHINE_END_WARRANTY_ACTIVITIES": {"aliases": [], "description": ""}, "MACHINE_INSTALLATION_DATE": {"aliases": [], "description": ""}, "MACHINE_PHYSICAL_POSITION": {"aliases": [], "description": ""}, "MACHINE_PHYSICAL_AREA": {"aliases": [], "description": ""}, "MACHINE_NOTE": {"aliases": [], "description": ""}, "QES_ACTION_CODE": {"aliases": [], "description": ""}, "ENGINEER_NAME": {"aliases": [], "description": ""}, "MACHINE_FACTORY_CLASSIFICATION_CODE": {"aliases": [], "description": ""}, "MACHINE_FACTORY_CLASSIFICATION_DESCRIPTION": {"aliases": [], "description": ""}, "MACHINE_PM_CLASSIFICATION_CODE": {"aliases": [], "description": ""}, "MACHINE_PM_CLASSIFICATION_DESCRIPTION": {"aliases": [], "description": ""}, "MACHINE_CLASS_AND_SUBCLASS_CLASSIFICATION_CODE": {"aliases": [], "description": ""}, "MACHINE_CLASS_AND_SUBCLASS_CLASSIFICATION_DESCRIPTION": {"aliases": [], "description": ""}, "MACHINE_PNC": {"aliases": [], "description": ""}, "MACHINE_FACTORY_MODEL": {"aliases": [], "description": ""}, "TOTAL_COST_SPARES": {"aliases": [], "description": ""}, "TOTAL_COST_LABOUR": {"aliases": [], "description": ""}, "TOTAL_REVENUE_SPARES": {"aliases": [], "description": ""}, "TOTAL_REVENUE_LABOUR": {"aliases": [], "description": ""}, "CONTACT_TITLE": {"aliases": [], "description": ""}, "APPROVED_COUNTER": {"aliases": [], "description": ""}, "NOTAPPROVED_COUNTER": {"aliases": [], "description": ""}, "LAST_APPROVER_NAME": {"aliases": [], "description": ""}, "SITE_NAME": {"aliases": [], "description": ""}, "SITE_ADDRESS": {"aliases": [], "description": ""}, "SITE_CITY": {"aliases": [], "description": ""}, "SITE_ZIP": {"aliases": [], "description": ""}, "SITE_DISTRICT": {"aliases": [], "description": ""}, "SITE_COUNTRY": {"aliases": [], "description": ""}, "CUSTOMER_NAME": {"aliases": [], "description": ""}, "CUSTOMER_ADDRESS": {"aliases": [], "description": ""}, "CUSTOMER_CITY": {"aliases": [], "description": ""}, "CUSTOMER_DISTRICT": {"aliases": [], "description": ""}, "CUSTOMER_COUNTRY": {"aliases": [], "description": ""}, "INVOICE_CUSTOMER_NAME": {"aliases": [], "description": ""}, "INVOICE_CUSTOMER_ADDRESS": {"aliases": [], "description": ""}, "INVOICE_CUSTOMER_CITY": {"aliases": [], "description": ""}, "INVOICE_CUSTOMER_DISTRICT": {"aliases": [], "description": ""}, "INVOICE_CUSTOMER_COUNTRY": {"aliases": [], "description": ""}, "CONTACT_REFERENCE_NAME": {"aliases": [], "description": ""}, "CONTACT_REFERENCE_MOBILE": {"aliases": [], "description": ""}, "CONTACT_REFERENCE_EMAIL": {"aliases": [], "description": ""}, "CONTACT_REFERENCE_ADDRESS": {"aliases": [], "description": ""}, "CONTACT_REFERENCE_CITY": {"aliases": [], "description": ""}, "CONTACT_REFERENCE_ZIP_CODE": {"aliases": [], "description": ""}, "SPARE_CODE": {"aliases": [], "description": ""}, "SPARE_QTY": {"aliases": [], "description": ""}, "SPARE_COST_GROSS_UNIT": {"aliases": [], "description": ""}, "SPARE_COST_GROSS_TOTAL": {"aliases": [], "description": ""}, "SPARE_COST_NET_UNIT": {"aliases": [], "description": ""}, "SPARE_COST_NET_TOTAL": {"aliases": [], "description": ""}, "SPARE_REVENUE_GROSS_UNIT": {"aliases": [], "description": ""}, "SPARE_REVENUE_GROSS_TOTAL": {"aliases": [], "description": ""}, "SPARE_REVENUE_NET_UNIT": {"aliases": [], "description": ""}, "SPARE_REVENUE_NET_TOTAL": {"aliases": [], "description": ""}, "SPARE_STK3_UNIT": {"aliases": [], "description": ""}, "SPARE_STK3_TOTAL": {"aliases": [], "description": ""}, "SPARE_WARRANTY_CODE": {"aliases": [], "description": ""}, "SPARE_WARRANTY_DESCRIPTION": {"aliases": [], "description": ""}, "SPARE_WAREHOUSE": {"aliases": [], "description": ""}, "SPARE_SENT_TO_OTC_DATE": {"aliases": [], "description": ""}, "ACTIVITY_COUNTRY": {"aliases": [], "description": "This column contains country data in ISO2 format, the requested country must alwyas be converted in ISO2 for this column. For example: UK must be converted in its ISO2 that is GB"}, "ACTIVITY_TYPE_DESCRIPTION": {"aliases": [], "description": ""}, "ACTIVITY_STATUS_DESCRIPTION": {"aliases": [], "description": ""}, "CURRENT_APPROVER": {"aliases": [], "description": ""}, "PTP_DATE": {"aliases": [], "description": ""}}}, "DWH_PUBLIC.PRIDE_PDB_MAINDATA_IMPROVED": {"schema": "CREATE TABLE DWH_PUBLIC.PRIDE_PDB_MAINDATA_IMPROVED (\n  \"SINTCODE\" NVARCHAR2 (25) NOT NULL ENABLE,\n  \"CCOMSTATUS\" NVARCHAR2 (1),\n  \"CCOMSTATUS_DESCRIPTION\" NVARCHAR2 (400),\n  \"ISTATUS\" NUMBER,\n  \"ISTATUS_DESCRIPTION\" NVARCHAR2 (400),\n  \"IBRAND\" NUMBER,\n  \"IBRAND_DESCRIPTION\" NVARCHAR2 (400),\n  \"SPNC\" NVARCHAR2 (30),\n  \"SFACTMODEL\" NVARCHAR2 (30),\n  \"SFACTCODE\" NVARCHAR2 (30),\n  \"IPLATFORM\" NUMBER,\n  \"IPLATFORM_DESCRIPTION\" NVARCHAR2 (400),\n  \"SFACTORY\" NVARCHAR2 (5),\n  \"SFACTORY_DESCRIPTION\" NVARCHAR2 (400),\n  \"BSPECLOCKED\" NUMBER (1, 0),\n  \"SBENEFITLEVEL\" NVARCHAR2 (50),\n  \"SAGGCODE\" NVARCHAR2 (10),\n  \"CCLASS\" NVARCHAR2 (1),\n  \"CCLASS_DESCRIPTION\" NVARCHAR2 (400),\n  \"SFAMILY\" NVARCHAR2 (2),\n  \"SFAMILY_DESCRIPTION\" NVARCHAR2 (400),\n  \"CTYPE\" NVARCHAR2 (1),\n  \"CTYPE_DESCRIPTION\" NVARCHAR2 (400),\n  \"CCONDITION\" NVARCHAR2 (1),\n  \"SSUPPLIER\" NVARCHAR2 (20),\n  \"SSUPPLIER_DESCRIPTION\" NVARCHAR2 (400),\n  \"SINTCODESUPPLIER\" NVARCHAR2 (25),\n  \"SDESIGNFAM\" NVARCHAR2 (100),\n  \"SAPPROVALS\" NVARCHAR2 (1000),\n  \"SKEYACCOUNT\" NVARCHAR2 (100),\n  \"SENERGY\" NVARCHAR2 (100),\n  \"SDESCRIPTION\" NVARCHAR2 (120),\n  \"SDESCRIPTION_ELS\" NVARCHAR2 (4000),\n  \"SSUPDESCRIPTION\" NVARCHAR2 (100),\n  \"SCLASSIFICATION\" NVARCHAR2 (4),\n  \"SCOMMENT\" NVARCHAR2 (2000),\n  \"SCOMMENT1\" NVARCHAR2 (30),\n  \"SOBJTOUSETECH\" NVARCHAR2 (100),\n  \"SOBJTOUSEDSGN\" NVARCHAR2 (100),\n  \"SINSBY\" NVARCHAR2 (10),\n  \"DINSDATE\" DATE,\n  \"SUPDBY\" NVARCHAR2 (10),\n  \"DUPDDATE\" DATE,\n  \"SDQUALITY\" NVARCHAR2 (2000),\n  \"SITSCOMMENT\" NVARCHAR2 (1000),\n  \"IPREVSTATUS\" NUMBER,\n  \"BISNEWDOC\" NUMBER (1, 0),\n  \"BGRANTTOCOM\" NUMBER (1, 0),\n  \"BINCOM\" NUMBER (1, 0),\n  \"STOCOMBY\" NVARCHAR2 (10),\n  \"STOCOMDATE\" DATE,\n  \"IWEBSTATUS\" NUMBER,\n  \"DUPDFROMCOM\" DATE,\n  \"BUPDFROMNOTES\" NUMBER (1, 0),\n  \"DUPDFROMNOTES\" DATE,\n  \"DUPDFROMGULP\" DATE,\n  \"SCEAPPROVALNUMBER\" NVARCHAR2 (50),\n  \"BMACHINEDIRECTIVE\" NUMBER (1, 0),\n  \"SAPPLIANCEINFO\" NVARCHAR2 (60),\n  \"SORDINARYMAINTENANCE\" NVARCHAR2 (100),\n  \"SPREVENTIVEMAINTENANCE\" NVARCHAR2 (100),\n  \"SORDINARYMAINTENANCEDESC\" NVARCHAR2 (100),\n  \"SPREVENTIVEMAINTENANCEDESC\" NVARCHAR2 (100),\n  \"SCONFORMTO\" NVARCHAR2 (1000),\n  \"SCERTIFIEDTO\" NVARCHAR2 (1000),\n  \"BELSCODE\" NUMBER (1, 0),\n  \"SCONFORMTOSANITATION\" NVARCHAR2 (1000),\n  \"SMANUFACTURINGCOUNTRY\" NVARCHAR2 (20),\n  \"BCOMPETITORCODE\" NUMBER (1, 0),\n  \"SCOUNTRY\" NVARCHAR2 (50),\n  \"SOLDFACTCODE\" NVARCHAR2 (30),\n  \"BFUMIGATION\" NUMBER (1, 0),\n  \"SSTOPFUMIGATIONDATE\" NVARCHAR2 (6),\n  \"BCEDECLARATION\" NUMBER (1, 0),\n  \"BSCODE\" NUMBER (1, 0),\n  \"BCOMPOSITIONCODE\" NUMBER (1, 0),\n  \"SEXTERNALMODEL\" NVARCHAR2 (30),\n  \"SPRODCLASS\" NVARCHAR2 (10),\n  \"SLANGUAGECODE\" NVARCHAR2 (10),\n  \"SAPPROVALTYPE\" NVARCHAR2 (32),\n  \"STYPEOFHEATING\" NVARCHAR2 (32),\n  \"BINSAP\" NUMBER (1, 0),\n  \"GUIDPPD2\" NVARCHAR2 (255),\n  \"BSERIALNUMBERMANDATORY\" NUMBER (1, 0),\n  \"SWMAPPROVALNUMBER\" NVARCHAR2 (50),\n  \"SPMMODELDESCRIPTION\" NVARCHAR2 (1000),\n  \"SSTATISTICALMODEL\" NVARCHAR2 (30),\n  \"BUPDPACKAGE\" NUMBER (1, 0),\n  \"SKINDOFPRODUCT\" NVARCHAR2 (1),\n  \"SEANCODE\" NVARCHAR2 (25),\n  \"SSERVMODEL\" NVARCHAR2 (30),\n  \"SMODEL\" NVARCHAR2 (30),\n  \"IREQUESTCODE\" NUMBER,\n  \"BMARINE\" NUMBER (1, 0),\n  \"BLOCAL\" NUMBER (1, 0),\n  \"SSPECIALFLOW\" NVARCHAR2 (1),\n  \"SEUPCLASS\" NVARCHAR2 (20),\n  \"FEUPENERGYCONSUMPTION\" BINARY_DOUBLE,\n  \"SEUPENERGYCLASS\" NVARCHAR2 (5),\n  \"SEUPEEI\" NVARCHAR2 (10),\n  \"BFROMCONFIGURATOR\" NUMBER (1, 0),\n  \"BISKIT\" NUMBER (1, 0),\n  \"BNOMAXLINE\" NUMBER (1, 0),\n  \"BEXCLUDEFROMQESEXPORT\" NUMBER (1, 0),\n  \"FEPRELEQUIVALENTMODEL\" NUMBER (20, 0),\n  \"FEPRELREGISTRATIONCODE\" NUMBER (20, 0),\n  \"DEPRELREGISTRATIONDATE\" DATE,\n  \"SEPRELPRODUCTGROUP\" NVARCHAR2 (64),\n  \"SEPRELIDENTIFIER\" NVARCHAR2 (255),\n  \"BCREATEDBYCONFIG\" NUMBER (1, 0),\n  \"SENERGYGROUP\" NVARCHAR2 (15),\n  \"BKMODEL\" NUMBER (1, 0),\n  \"BENERGYLABEL\" NUMBER (1, 0),\n  \"SPNCMODEL\" NVARCHAR2 (20),\n  \"IROWORDER\" NUMBER,\n  \"SEPRELSTATUS\" NVARCHAR2 (30),\n  \"SKW\" NVARCHAR2 (15),\n  \"SCEIDNUMBERPRESSURE\" NVARCHAR2 (15),\n  \"SCEIDNUMBERGAS\" NVARCHAR2 (15),\n  \"SUKCAIDNUMBERPRESSURE\" NVARCHAR2 (15),\n  \"SUKCAIDNUMBERGAS\" NVARCHAR2 (15),\n  \"SCHANNEL\" NVARCHAR2 (2),\n  \"STYPEFAMILY\" NVARCHAR2 (10),\n  \"SAPPROVALAGGREGATIONSYMBOLS\" NVARCHAR2 (50),\n  \"SETLCONTROLNUMBER\" NVARCHAR2 (50),\n  CONSTRAINT \"PK_TB_PDB_MAINDATA\" PRIMARY KEY (\"SINTCODE\") USING INDEX ENABLE\n);", "aliases": [], "description": "", "example_id": "", "columns": {"SINTCODE": {"aliases": [], "description": ""}, "CCOMSTATUS": {"aliases": [], "description": ""}, "CCOMSTATUS_DESCRIPTION": {"aliases": [], "description": ""}, "ISTATUS": {"aliases": [], "description": ""}, "ISTATUS_DESCRIPTION": {"aliases": [], "description": ""}, "IBRAND": {"aliases": [], "description": ""}, "IBRAND_DESCRIPTION": {"aliases": [], "description": ""}, "SPNC": {"aliases": [], "description": ""}, "SFACTMODEL": {"aliases": [], "description": ""}, "SFACTCODE": {"aliases": [], "description": ""}, "IPLATFORM": {"aliases": [], "description": ""}, "IPLATFORM_DESCRIPTION": {"aliases": [], "description": ""}, "SFACTORY": {"aliases": [], "description": ""}, "SFACTORY_DESCRIPTION": {"aliases": [], "description": ""}, "BSPECLOCKED": {"aliases": [], "description": ""}, "SBENEFITLEVEL": {"aliases": [], "description": ""}, "SAGGCODE": {"aliases": [], "description": ""}, "CCLASS": {"aliases": [], "description": ""}, "CCLASS_DESCRIPTION": {"aliases": [], "description": ""}, "SFAMILY": {"aliases": [], "description": ""}, "SFAMILY_DESCRIPTION": {"aliases": [], "description": ""}, "CTYPE": {"aliases": [], "description": ""}, "CTYPE_DESCRIPTION": {"aliases": [], "description": ""}, "CCONDITION": {"aliases": [], "description": ""}, "SSUPPLIER": {"aliases": [], "description": ""}, "SSUPPLIER_DESCRIPTION": {"aliases": [], "description": ""}, "SINTCODESUPPLIER": {"aliases": [], "description": ""}, "SDESIGNFAM": {"aliases": [], "description": ""}, "SAPPROVALS": {"aliases": [], "description": ""}, "SKEYACCOUNT": {"aliases": [], "description": ""}, "SENERGY": {"aliases": [], "description": ""}, "SDESCRIPTION": {"aliases": [], "description": ""}, "SDESCRIPTION_ELS": {"aliases": [], "description": ""}, "SSUPDESCRIPTION": {"aliases": [], "description": ""}, "SCLASSIFICATION": {"aliases": [], "description": ""}, "SCOMMENT": {"aliases": [], "description": ""}, "SCOMMENT1": {"aliases": [], "description": ""}, "SOBJTOUSETECH": {"aliases": [], "description": ""}, "SOBJTOUSEDSGN": {"aliases": [], "description": ""}, "SINSBY": {"aliases": [], "description": ""}, "DINSDATE": {"aliases": [], "description": ""}, "SUPDBY": {"aliases": [], "description": ""}, "DUPDDATE": {"aliases": [], "description": ""}, "SDQUALITY": {"aliases": [], "description": ""}, "SITSCOMMENT": {"aliases": [], "description": ""}, "IPREVSTATUS": {"aliases": [], "description": ""}, "BISNEWDOC": {"aliases": [], "description": ""}, "BGRANTTOCOM": {"aliases": [], "description": ""}, "BINCOM": {"aliases": [], "description": ""}, "STOCOMBY": {"aliases": [], "description": ""}, "STOCOMDATE": {"aliases": [], "description": ""}, "IWEBSTATUS": {"aliases": [], "description": ""}, "DUPDFROMCOM": {"aliases": [], "description": ""}, "BUPDFROMNOTES": {"aliases": [], "description": ""}, "DUPDFROMNOTES": {"aliases": [], "description": ""}, "DUPDFROMGULP": {"aliases": [], "description": ""}, "SCEAPPROVALNUMBER": {"aliases": [], "description": ""}, "BMACHINEDIRECTIVE": {"aliases": [], "description": ""}, "SAPPLIANCEINFO": {"aliases": [], "description": ""}, "SORDINARYMAINTENANCE": {"aliases": [], "description": ""}, "SPREVENTIVEMAINTENANCE": {"aliases": [], "description": ""}, "SORDINARYMAINTENANCEDESC": {"aliases": [], "description": ""}, "SPREVENTIVEMAINTENANCEDESC": {"aliases": [], "description": ""}, "SCONFORMTO": {"aliases": [], "description": ""}, "SCERTIFIEDTO": {"aliases": [], "description": ""}, "BELSCODE": {"aliases": [], "description": ""}, "SCONFORMTOSANITATION": {"aliases": [], "description": ""}, "SMANUFACTURINGCOUNTRY": {"aliases": [], "description": ""}, "BCOMPETITORCODE": {"aliases": [], "description": ""}, "SCOUNTRY": {"aliases": [], "description": ""}, "SOLDFACTCODE": {"aliases": [], "description": ""}, "BFUMIGATION": {"aliases": [], "description": ""}, "SSTOPFUMIGATIONDATE": {"aliases": [], "description": ""}, "BCEDECLARATION": {"aliases": [], "description": ""}, "BSCODE": {"aliases": [], "description": ""}, "BCOMPOSITIONCODE": {"aliases": [], "description": ""}, "SEXTERNALMODEL": {"aliases": [], "description": ""}, "SPRODCLASS": {"aliases": [], "description": ""}, "SLANGUAGECODE": {"aliases": [], "description": ""}, "SAPPROVALTYPE": {"aliases": [], "description": ""}, "STYPEOFHEATING": {"aliases": [], "description": ""}, "BINSAP": {"aliases": [], "description": ""}, "GUIDPPD2": {"aliases": [], "description": ""}, "BSERIALNUMBERMANDATORY": {"aliases": [], "description": ""}, "SWMAPPROVALNUMBER": {"aliases": [], "description": ""}, "SPMMODELDESCRIPTION": {"aliases": [], "description": ""}, "SSTATISTICALMODEL": {"aliases": [], "description": ""}, "BUPDPACKAGE": {"aliases": [], "description": ""}, "SKINDOFPRODUCT": {"aliases": [], "description": ""}, "SEANCODE": {"aliases": [], "description": ""}, "SSERVMODEL": {"aliases": [], "description": ""}, "SMODEL": {"aliases": [], "description": ""}, "IREQUESTCODE": {"aliases": [], "description": ""}, "BMARINE": {"aliases": [], "description": ""}, "BLOCAL": {"aliases": [], "description": ""}, "SSPECIALFLOW": {"aliases": [], "description": ""}, "SEUPCLASS": {"aliases": [], "description": ""}, "FEUPENERGYCONSUMPTION": {"aliases": [], "description": ""}, "SEUPENERGYCLASS": {"aliases": [], "description": ""}, "SEUPEEI": {"aliases": [], "description": ""}, "BFROMCONFIGURATOR": {"aliases": [], "description": ""}, "BISKIT": {"aliases": [], "description": ""}, "BNOMAXLINE": {"aliases": [], "description": ""}, "BEXCLUDEFROMQESEXPORT": {"aliases": [], "description": ""}, "FEPRELEQUIVALENTMODEL": {"aliases": [], "description": ""}, "FEPRELREGISTRATIONCODE": {"aliases": [], "description": ""}, "DEPRELREGISTRATIONDATE": {"aliases": [], "description": ""}, "SEPRELPRODUCTGROUP": {"aliases": [], "description": ""}, "SEPRELIDENTIFIER": {"aliases": [], "description": ""}, "BCREATEDBYCONFIG": {"aliases": [], "description": ""}, "SENERGYGROUP": {"aliases": [], "description": ""}, "BKMODEL": {"aliases": [], "description": ""}, "BENERGYLABEL": {"aliases": [], "description": ""}, "SPNCMODEL": {"aliases": [], "description": ""}, "IROWORDER": {"aliases": [], "description": ""}, "SEPRELSTATUS": {"aliases": [], "description": ""}, "SKW": {"aliases": [], "description": ""}, "SCEIDNUMBERPRESSURE": {"aliases": [], "description": ""}, "SCEIDNUMBERGAS": {"aliases": [], "description": ""}, "SUKCAIDNUMBERPRESSURE": {"aliases": [], "description": ""}, "SUKCAIDNUMBERGAS": {"aliases": [], "description": ""}, "SCHANNEL": {"aliases": [], "description": ""}, "STYPEFAMILY": {"aliases": [], "description": ""}, "SAPPROVALAGGREGATIONSYMBOLS": {"aliases": [], "description": ""}, "SETLCONTROLNUMBER": {"aliases": [], "description": ""}, "CONSTRAINT": {"aliases": [], "description": ""}}}, "DWH_PUBLIC.PRIDE_PDB_LOGISTIC_IMPROVED": {"schema": "-- Fixed version of the CREATE TABLE statement\nCREATE TABLE DWH_PUBLIC.PRIDE_PDB_LOGISTIC_IMPROVED (\n  \"SINTCODE\" VARCHAR2(25 CHAR) NOT NULL ENABLE,\n  \"DPLANPRODSTART\" DATE,\n  \"DPRODSTART\" DATE,\n  \"DPRODSTOP\" DATE,\n  \"DDISTRIBSTART\" DATE,\n  \"DDISTRIBSTOP\" DATE,\n  \"SCUSTOMCLASS\" VARCHAR2(12 CHAR),\n  \"STAXCODE\" VARCHAR2(2 CHAR),\n  \"SACCOUNTING\" VARCHAR2(4 CHAR),\n  \"SACCOUNTING_DESCRIPTION\" NVARCHAR2(400),\n  \"CSALEWAY\" CHAR(1 CHAR),\n  \"CSALEWAY_DESCRIPTION\" NVARCHAR2(400),\n  \"BPICKLIST\" NUMBER(3,0),\n  \"CMATERIAL\" CHAR(2 CHAR),\n  \"CMATERIAL_DESCRIPTION\" NVARCHAR2(400),\n  \"ILOCKEDDAY\" NUMBER(10,0),\n  \"ILEADTIME\" NUMBER(10,0),\n  \"CSTATUS\" CHAR(1 CHAR),\n  \"SREPLACEDBY\" VARCHAR2(25 CHAR),\n  \"IREPLACEDQTY\" NUMBER(10,0),\n  \"SREPLACING\" VARCHAR2(500 CHAR),\n  \"CFINDING\" CHAR(1 CHAR),\n  \"CFINDING_DESCRIPTION\" NVARCHAR2(400),\n  \"CDELIVERY\" CHAR(1 CHAR),\n  \"CDELIVERY_DESCRIPTION\" NVARCHAR2(400),\n  \"COWNER\" CHAR(1 CHAR),\n  \"COWNER_DESCRIPTION\" NVARCHAR2(400),\n  \"SDIVISION\" VARCHAR2(2 CHAR),\n  \"SDIVISION_DESCRIPTION\" NVARCHAR2(400),\n  \"SPLANTLINE\" VARCHAR2(2 CHAR),\n  \"CMAKEBUY\" CHAR(1 CHAR),\n  \"CMAKEBUY_DESCRIPTION\" NVARCHAR2(400),\n  \"CSUBTYPE\" CHAR(1 CHAR),\n  \"CSUBTYPE_DESCRIPTION\" NVARCHAR2(400),\n  \"SCLASSIFICATION\" VARCHAR2(4 CHAR),\n  \"SCLASSIFICATION_DESCRIPTION\" NVARCHAR2(400),\n  \"SFATHER\" VARCHAR2(25 CHAR),\n  \"SCOUNTRYOR\" VARCHAR2(4 CHAR),\n  \"SCOUNTRYOR_DESCRIPTION\" NVARCHAR2(400),\n  \"SNOTICE\" VARCHAR2(10 CHAR),\n  \"SPMCLASSIFICATIONLEVEL1\" VARCHAR2(2 CHAR),\n  \"SPMCLASSIFICATIONLEVEL1_DESCRIPTION\" NVARCHAR2(400),\n  \"SPMCLASSIFICATIONLEVEL2\" VARCHAR2(2 CHAR),\n  \"SPMCLASSIFICATIONLEVEL2_DESCRIPTION\" NVARCHAR2(400),\n  \"SPMCLASSIFICATIONLEVEL3\" VARCHAR2(50 CHAR),\n  \"SPMCLASSIFICATIONLEVEL3_DESCRIPTION\" NVARCHAR2(400),\n  \"SPMCLASSIFICATIONLEVEL4\" VARCHAR2(50 CHAR),\n  \"SPMCLASSIFICATIONLEVEL4_DESCRIPTION\" NVARCHAR2(400),\n  \"SNOTELINE1\" VARCHAR2(60 CHAR),\n  \"SNOTELINE2\" VARCHAR2(60 CHAR),\n  \"SNOTELINE3\" VARCHAR2(60 CHAR),\n  \"SNOTELINE4\" VARCHAR2(60 CHAR),\n  \"SEXPCOUNTRYOFORIGIN\" VARCHAR2(6 CHAR),\n  \"SRECMARKETS\" VARCHAR2(500 CHAR),\n  \"SREPLACEDBY_ELS\" VARCHAR2(25 CHAR),\n  \"SREPLACING_ELS\" VARCHAR2(300 CHAR),\n  \"SCODIFICATION\" VARCHAR2(50 CHAR),\n  \"BSUGGESTED\" NUMBER(3,0),\n  \"BCOMPETITOR\" CHAR(10 CHAR),\n  \"BSERIALNUMBER\" NUMBER(3,0),\n  \"CP13REF\" CHAR(1 CHAR),\n  \"CP13REF_DESCRIPTION\" NVARCHAR2(400),\n  \"BSUGGACC\" NUMBER(3,0),\n  \"SSUBLINE\" CHAR(2 CHAR),\n  \"BFITTEDACC\" NUMBER(3,0),\n  \"BGOODSLABEL\" NUMBER(3,0),\n  \"BTECHNICIANFEE\" CHAR(1 CHAR),\n  \"BGETBACK\" NUMBER(3,0),\n  \"SGETBACKCLASS\" VARCHAR2(6 CHAR),\n  \"SMORENOTICE\" VARCHAR2(200 CHAR),\n  \"SMOREREPLACING\" VARCHAR2(500 CHAR),\n  \"SPLANFATHER\" VARCHAR2(25 CHAR),\n  \"DORIGINALDISTRIBSTART\" DATE,\n  \"SCOO_TYPE\" VARCHAR2(1 CHAR),\n  \"FCOO_PERCENTAGE\" BINARY_DOUBLE,\n  \"BMANUALCOMPETITOR\" NUMBER(3,0),\n  \"SBOI_UM\" VARCHAR2(3 CHAR),\n  \"BSERVICEPART\" NUMBER(3,0),\n  \"BBIPPART\" NUMBER(3,0),\n  \"BLASTBUYORDER\" NUMBER(3,0),\n  \"SGREENCODE\" VARCHAR2(25 CHAR),\n  \"BAUTOMATICDISTRIBSTOPCALCULATION\" NUMBER(3,0),\n  \"SBULKYITEM\" VARCHAR2(1 CHAR),\n  CONSTRAINT \"PK_TB_PDB_LOGISTIC\" PRIMARY KEY (\"SINTCODE\") USING INDEX ENABLE\n);", "aliases": [], "description": "", "example_id": "", "columns": {"SINTCODE": {"aliases": [], "description": ""}, "DPLANPRODSTART": {"aliases": [], "description": ""}, "DPRODSTART": {"aliases": [], "description": ""}, "DPRODSTOP": {"aliases": [], "description": ""}, "DDISTRIBSTART": {"aliases": [], "description": ""}, "DDISTRIBSTOP": {"aliases": [], "description": ""}, "SCUSTOMCLASS": {"aliases": [], "description": ""}, "STAXCODE": {"aliases": [], "description": ""}, "SACCOUNTING": {"aliases": [], "description": ""}, "SACCOUNTING_DESCRIPTION": {"aliases": [], "description": ""}, "CSALEWAY": {"aliases": [], "description": ""}, "CSALEWAY_DESCRIPTION": {"aliases": [], "description": ""}, "BPICKLIST": {"aliases": [], "description": ""}, "CMATERIAL": {"aliases": [], "description": ""}, "CMATERIAL_DESCRIPTION": {"aliases": [], "description": ""}, "ILOCKEDDAY": {"aliases": [], "description": ""}, "ILEADTIME": {"aliases": [], "description": ""}, "CSTATUS": {"aliases": [], "description": ""}, "SREPLACEDBY": {"aliases": [], "description": ""}, "IREPLACEDQTY": {"aliases": [], "description": ""}, "SREPLACING": {"aliases": [], "description": ""}, "CFINDING": {"aliases": [], "description": ""}, "CFINDING_DESCRIPTION": {"aliases": [], "description": ""}, "CDELIVERY": {"aliases": [], "description": ""}, "CDELIVERY_DESCRIPTION": {"aliases": [], "description": ""}, "COWNER": {"aliases": [], "description": ""}, "COWNER_DESCRIPTION": {"aliases": [], "description": ""}, "SDIVISION": {"aliases": [], "description": ""}, "SDIVISION_DESCRIPTION": {"aliases": [], "description": ""}, "SPLANTLINE": {"aliases": [], "description": ""}, "CMAKEBUY": {"aliases": [], "description": ""}, "CMAKEBUY_DESCRIPTION": {"aliases": [], "description": ""}, "CSUBTYPE": {"aliases": [], "description": ""}, "CSUBTYPE_DESCRIPTION": {"aliases": [], "description": ""}, "SCLASSIFICATION": {"aliases": [], "description": ""}, "SCLASSIFICATION_DESCRIPTION": {"aliases": [], "description": ""}, "SFATHER": {"aliases": [], "description": ""}, "SCOUNTRYOR": {"aliases": [], "description": ""}, "SCOUNTRYOR_DESCRIPTION": {"aliases": [], "description": ""}, "SNOTICE": {"aliases": [], "description": ""}, "SPMCLASSIFICATIONLEVEL1": {"aliases": [], "description": ""}, "SPMCLASSIFICATIONLEVEL1_DESCRIPTION": {"aliases": [], "description": ""}, "SPMCLASSIFICATIONLEVEL2": {"aliases": [], "description": ""}, "SPMCLASSIFICATIONLEVEL2_DESCRIPTION": {"aliases": [], "description": ""}, "SPMCLASSIFICATIONLEVEL3": {"aliases": [], "description": ""}, "SPMCLASSIFICATIONLEVEL3_DESCRIPTION": {"aliases": [], "description": ""}, "SPMCLASSIFICATIONLEVEL4": {"aliases": [], "description": ""}, "SPMCLASSIFICATIONLEVEL4_DESCRIPTION": {"aliases": [], "description": ""}, "SNOTELINE1": {"aliases": [], "description": ""}, "SNOTELINE2": {"aliases": [], "description": ""}, "SNOTELINE3": {"aliases": [], "description": ""}, "SNOTELINE4": {"aliases": [], "description": ""}, "SEXPCOUNTRYOFORIGIN": {"aliases": [], "description": ""}, "SRECMARKETS": {"aliases": [], "description": ""}, "SREPLACEDBY_ELS": {"aliases": [], "description": ""}, "SREPLACING_ELS": {"aliases": [], "description": ""}, "SCODIFICATION": {"aliases": [], "description": ""}, "BSUGGESTED": {"aliases": [], "description": ""}, "BCOMPETITOR": {"aliases": [], "description": ""}, "BSERIALNUMBER": {"aliases": [], "description": ""}, "CP13REF": {"aliases": [], "description": ""}, "CP13REF_DESCRIPTION": {"aliases": [], "description": ""}, "BSUGGACC": {"aliases": [], "description": ""}, "SSUBLINE": {"aliases": [], "description": ""}, "BFITTEDACC": {"aliases": [], "description": ""}, "BGOODSLABEL": {"aliases": [], "description": ""}, "BTECHNICIANFEE": {"aliases": [], "description": ""}, "BGETBACK": {"aliases": [], "description": ""}, "SGETBACKCLASS": {"aliases": [], "description": ""}, "SMORENOTICE": {"aliases": [], "description": ""}, "SMOREREPLACING": {"aliases": [], "description": ""}, "SPLANFATHER": {"aliases": [], "description": ""}, "DORIGINALDISTRIBSTART": {"aliases": [], "description": ""}, "SCOO_TYPE": {"aliases": [], "description": ""}, "FCOO_PERCENTAGE": {"aliases": [], "description": ""}, "BMANUALCOMPETITOR": {"aliases": [], "description": ""}, "SBOI_UM": {"aliases": [], "description": ""}, "BSERVICEPART": {"aliases": [], "description": ""}, "BBIPPART": {"aliases": [], "description": ""}, "BLASTBUYORDER": {"aliases": [], "description": ""}, "SGREENCODE": {"aliases": [], "description": ""}, "BAUTOMATICDISTRIBSTOPCALCULATION": {"aliases": [], "description": ""}, "SBULKYITEM": {"aliases": [], "description": ""}, "CONSTRAINT": {"aliases": [], "description": ""}}}, "DWH_PUBLIC.get_bom_explosion": {"schema": "CREATE TABLE DWH_PUBLIC.get_bom_explosion (\n    PARENT NVARCHAR2(25),\n    CHILD NVARCHAR2(25),\n    SPARE VARCHAR2(30),\n    COMPANY NVARCHAR2(5),\n    PLANT NVARCHAR2(12),\n    <PERSON><PERSON><PERSON><PERSON>,\n    CHILD_QTY NUMBER(25, 0)\n);", "aliases": [], "description": "", "example_id": "spare-parts_", "columns": {"PARENT": {"aliases": [], "description": ""}, "CHILD": {"aliases": [], "description": ""}, "SPARE": {"aliases": [], "description": ""}, "COMPANY": {"aliases": [], "description": ""}, "PLANT": {"aliases": [], "description": ""}, "LEVEL": {"aliases": [], "description": ""}, "CHILD_QTY": {"aliases": [], "description": ""}}}}}}