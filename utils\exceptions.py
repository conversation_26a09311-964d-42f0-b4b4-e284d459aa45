from typing import List, Optional, Tuple


class StoppedException(Exception):
    def __init__(self, *args: object) -> None:
        super().__init__(*args)


class IncapableException(Exception):
    """
    Raised when BOT is not capable of answering the inquiry given.
    """

    def __init__(
        self, message: str, failed_candidates: Optional[List[Tuple[str, str]]] = None
    ) -> None:
        super().__init__(message)
        self.failed_candidates = failed_candidates
        self.message = message


class DatabaseException(Exception):
    def __init__(self, message: str) -> None:
        self.message = message
        super().__init__(message)

class UndetectedIntendException(Exception):
    def __init__(self, message: str) -> None:
        self.message = message
        super().__init__(message)
        
        
class DocumentNotFoundException(Exception):
    def __init__(self, message: str) -> None:
        self.message = message
        super().__init__(message)



class DocumentNotAuthorizedException(Exception):
    def __init__(self, message: str) -> None:
        self.message = message
        super().__init__(message)

class AgentNotAuthorizedException(Exception):
    def __init__(self, message: str) -> None:
        self.message = message
        super().__init__(message)    

class SecurityException(Exception):
    """
    Raised in case of security issue.
    """

    def __init__(self, message: str) -> None:
        self.message = message
        super().__init__(message)


class AgentValueException(Exception):
    """
    Raised when an agent name is not in the enum
    """

    def __init__(self, message: str) -> None:
        self.message = message
        super().__init__(message)


class BotValueException(Exception):
    """
    Raised when an agent name is not in the enum
    """

    def __init__(self, message: str) -> None:
        self.message = message
        super().__init__(message)

