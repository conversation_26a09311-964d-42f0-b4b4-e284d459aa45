from abc import ABC, abstractmethod
from typing import Any, Collection, Dict, List, Optional

from src.backend.contracts.chat_data import Agent<PERSON><PERSON>
from src.backend.models import QuestionHistory
from src.common_tools.history.parsers import HistoryParser
from utils.core import Singleton


class HistoryCompiler:
    """Interface for a compiler for a conversation history."""

    def __init__(self, parser: HistoryParser) -> None:
        """Creates an history compiler that employs a specific parser.

        Args:
            parser (HistoryParser): A parser for the messages' representation.
        """
        super().__init__()
        self.parser = parser

    @abstractmethod
    def compile(self, messages: Collection[Any]) -> List[Dict[str, str]]:
        """Translates the provided messages into the best format usable by an agent.

        Args:
            messages (Collection[Any]): A collection of messages in a specific representation.

        Returns:
            List[Dict[str, str]]: The messages in a format the agent understands.
        """
        pass


class SQLAlchemyCompiler(HistoryCompiler, metaclass=Singleton):
    def __init__(
        self,
        parser: <PERSON><PERSON>arser,
    ) -> None:
        super().__init__(parser)

    def compile(self, messages: List[QuestionHistory]) -> List[Dict[str, str]]:
        compiled_messages = []

        for message in messages:
            agent = self.parser.get_agent(message)
            user = self.parser.get_user_message(message)
            bot = self.parser.get_bot_message(message,agent)
            extra_messages = []

            match agent:
                case AgentName.TEXT_TO_SQL.name:
                    user = f"/* Answer the following: {user} */"
                    query_result = self._compile_query_result(message)

                    if query_result:
                        extra_messages.append(query_result)
                        
                    compiled_messages += [
                        {"role": "user", "content": user, "agent": agent},
                        {"role": "assistant", "content": bot, "agent": agent},
                    ]

                case AgentName.RAG.name:
                    bot += self._compile_references(message)
                    question_status = self.parser.get_question_status_message(message)
                    status = self._compile_status(message)

                    missing_metadata = self._compile_missing_metadata(message)
                    metadata_gathered = self._compile_metadata_gathered(message)
                        
                    compiled_messages += [
                        {"role": "user", "content": user, "agent": agent, "question_status": question_status},
                        {"role": "assistant", "content": bot, "agent": agent, "status": status, "missing_metadata": missing_metadata, "metadata_gathered":metadata_gathered },
                    ]

                case AgentName.RAG_DOCUMENT.name:
                    question_status = self.parser.get_question_status_message(message)
                    status = self._compile_status(message)
                    metadata_gathered = self._compile_metadata_gathered(message)
                    
                    if status == "complete":
                        missing_metadata = []
                    else:
                        missing_metadata = self._compile_missing_metadata(message)
                        
                    compiled_messages += [
                        {"role": "user", "content": user, "agent": agent, "question_status": question_status},
                        {"role": "assistant", "content": bot, "agent": agent, "status": status, "missing_metadata": missing_metadata, "metadata_gathered":metadata_gathered },
                    ]
                
                case AgentName.IMAGE_GEN.name:
                    question_status = self.parser.get_question_status_message(message)
                    status = self._compile_status(message)
                    
                    metadata_gathered = []
                    missing_metadata = []
                   
                        
                    compiled_messages += [
                        {"role": "user", "content": user, "agent": agent, "question_status": question_status},
                        {"role": "assistant", "content": bot, "agent": agent, "status": status, "missing_metadata": missing_metadata, "metadata_gathered":metadata_gathered },
                    ]

            if extra_messages:
                compiled_messages += extra_messages

        return compiled_messages

    def _compile_references(self, message: QuestionHistory) -> str:
        references = self.parser.get_references(message)
        references = "\n".join(references)
        return f"\n\nReferences:\n{references}"
    
    def _compile_status(self, message: QuestionHistory) -> str:
        status = self.parser.get_chat_status(message)
        return status
    
    def _compile_missing_metadata(self, message: QuestionHistory) -> list:
        missing_metadata = self.parser.get_missing_metadata(message)
        return missing_metadata

    def _compile_metadata_gathered(self, message: QuestionHistory) -> list:
        metadata_gathered = self.parser.get_metadata_gathered(message)
        return metadata_gathered
    
    def _compile_query_result(
        self, message: QuestionHistory
    ) -> Optional[Dict[str, str]]:
        data = self.parser.get_query_result(message)
        data = (
            f"/* Yielded data: */\n{data.to_markdown()}" if data is not None else None
        )

        if data is not None:
            return {"role": "system", "content": data, "agent": AgentName.TEXT_TO_SQL.name}
        else:
            return data
