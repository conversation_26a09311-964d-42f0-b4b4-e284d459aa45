from configparser import RawConfigParser
from os import environ, getlogin
from urllib.parse import quote_plus

from utils.core import Singleton, get_relative_path

DEFAULT_CONFIG_PATH = get_relative_path(__file__, "../config.ini")


class PrimaryConfig(metaclass=Singleton):
    def __init__(self) -> None:
        # Default config file
        config_file = DEFAULT_CONFIG_PATH
        self.config_env = ''

        # Check overrides from local config_xyz.ini files
        env = environ.get("FLASK_ENV")
        username = getlogin().lower()

        if env == "PRD":
            config_file = "config_prd.ini"
            self.config_env = ''
        elif env == "STG":
            config_file = "config_stg.ini"
            self.config_env = 'STAGING'
        elif env == "DEV":
            config_file = "config_dev.ini"
            self.config_env = 'DEVEL'
        elif username:
            config_file = f"config_{username}.ini"
            self.config_env = 'LOCAL'

        self.config = RawConfigParser(allow_no_value=True)
        config_file = get_relative_path(__file__, "../" + config_file)
        self.config.read([DEFAULT_CONFIG_PATH, config_file])

    @property
    def env(self) -> str:
        return self.config_env


class SubordinateConfig:
    def __init__(self, module: str) -> None:
        self.__module = module
        self.primary_config = PrimaryConfig()

    def get_setting(self, name: str, is_in_env: bool = False) -> str:
        vars = environ if is_in_env else None
        return self.primary_config.config.get(
            self.__module, name, vars=vars, fallback=None
        )


class CommonConfig(metaclass=Singleton):
    def __init__(self) -> None:
        self.flask = SubordinateConfig("web")
        self.common = SubordinateConfig("common")
        self.log = SubordinateConfig("log")
        self.ad = SubordinateConfig("ad")
        self.db = SubordinateConfig("db")
        self.image_generator = SubordinateConfig("image_generator")


    @property
    def flask_app(self) -> str:
        return self.flask.get_setting("flask_app")

    @property
    def flask_debug(self) -> int:
        return int(self.flask.get_setting("flask_debug"))

    @property
    def flask_secret_key(self) -> str:
        return self.flask.get_setting("FLASK_SECRET_KEY", True)

    @property
    def pride_product_root(self) -> str:
        return self.flask.get_setting("PRIDE_PRODUCT_ROOT", True)


    @property
    def default_run_mode(self) -> str:
        return self.common.get_setting("run_mode")

    @property
    def default_version(self) -> str:
        return str(self.common.get_setting("version_gittag") + ' ' + self.common.get_setting("version_gitrev"))

    @property
    def default_version_release(self) -> str:
        return str(self.common.get_setting("version_release"))

    @property
    def default_logout_url(self) -> str:
        return self.common.get_setting("logout_url")

    @property
    def proxy(self) -> str:
        return self.common.get_setting("proxy")


    @property
    def log_level(self) -> str:
        return self.log.get_setting("loglevel")

    @property
    def log_folder(self) -> str:
        return self.log.get_setting("log_folder")


    @property
    def ad_client_id(self) -> str:
        return self.ad.get_setting("client_id")

    @property
    def ad_authority_uri(self) -> str:
        return self.ad.get_setting("authority_uri")

    @property
    def ad_redirect_uri(self) -> str:
        return self.ad.get_setting("redirect_uri")

    @property
    def ad_scope(self) -> str:
        return self.ad.get_setting("ad_scope")

    @property
    def ad_endpoint(self) -> str:
        return self.ad.get_setting("ad_endpoint_api")

    @property
    def ad_schema_callback(self) -> str:
        return self.ad.get_setting("ad_schema_callback")


    @property
    def ad_secret(self) -> str:
        return self.ad.get_setting("AD_SECRET", True)

    @property
    def db_name(self) -> str:
        return self.db.get_setting("db_name")

    @property
    def db_host(self) -> str:
        return self.db.get_setting("db_host")

    @property
    def db_port(self) -> str:
        return self.db.get_setting("db_port")

    @property
    def db_user(self) -> str:
        return self.db.get_setting("db_user")

    @property
    def db_driver(self) -> str:
        return self.db.get_setting("db_driver")

    @property
    def db_password(self) -> str:
        return quote_plus(self.db.get_setting("DB_PASSWORD", True))


    # @property
    # def image_generator_api_version(self) -> str:
    #     return self.image_generator.get_setting("version")

    # @property
    # def image_generator_endpoint(self) -> str:
    #     return self.image_generator.get_setting("endpoint")

    # @property
    # def image_generator_deployment(self) -> str:
    #     return self.image_generator.get_setting("deployment")


class RAGConfig(CommonConfig, metaclass=Singleton):
    def __init__(self) -> None:
        super().__init__()

        self.__embedder = SubordinateConfig("rag.embedding")
        self.__llm = SubordinateConfig("rag.llm")

        self.__indexer_loader = SubordinateConfig("rag_indexer.document_intelligence")
        self.__indexer_llm= SubordinateConfig("rag_indexer.llm")

        self.__azure_ai_search_field_params = SubordinateConfig("call_center_bot.rag.azure_ai_search")

        self.__call_center_bot_azure_ai_search = SubordinateConfig("call_center_bot.rag.azure_ai_search")
        self.__call_center_bot_azure_ai_search_parameters = SubordinateConfig("call_center_bot.rag.azure_ai_search.param")
        self.__call_center_bot_fetcher = SubordinateConfig("call_center_bot.rag_indexer.fetcher")

        self.__how_to_bot_azure_ai_search = SubordinateConfig("how_to_bot.rag.azure_ai_search")
        self.__how_to_bot_azure_ai_search_parameters = SubordinateConfig("how_to_bot.rag.azure_ai_search.param")
        self.__how_to_bot_fetcher = SubordinateConfig("how_to_bot.rag_indexer.fetcher")

        self.__jdanallo_azure_ai_search = SubordinateConfig("jdanallo.rag.azure_ai_search")
        self.__jdanallo_azure_ai_search_parameters = SubordinateConfig("jdanallo.rag.azure_ai_search.param")
        self.__jdanallo_fetcher = SubordinateConfig("jdanallo.rag_indexer.fetcher")

        self.__seobot_azure_ai_search = SubordinateConfig("seobot.rag.azure_ai_search")
        self.__seobot_azure_ai_search_parameters = SubordinateConfig("seobot.rag.azure_ai_search.param")
        self.__seobot_fetcher = SubordinateConfig("seobot.rag_indexer.fetcher")

    #RAG LLM GENERAL CONFIG
    @property
    def rag_llm_version(self) -> str:
        return self.__llm.get_setting("version")

    @property
    def rag_llm_endpoint(self) -> str:
        return self.__llm.get_setting("endpoint")

    @property
    def rag_llm_deployment(self) -> str:
        return self.__llm.get_setting("deployment")

    @property
    def rag_llm_key(self) -> str:
        return self.__llm.get_setting("OPENAI_LLM_KEY", True)


    #RAG EMBEDDER GENERAL CONFIG
    @property
    def rag_embedder_version(self) -> str:
        return self.__embedder.get_setting("version")

    @property
    def rag_embedder_endpoint(self) -> str:
        return self.__embedder.get_setting("endpoint")

    @property
    def rag_embedder_deployment(self) -> str:
        return self.__embedder.get_setting("deployment")

    @property
    def rag_embedder_key(self) -> str:
        return self.__embedder.get_setting("RAG_EMBEDDING_KEY", True)


    #RAG INDEXER DOCUMENT INTELLIGENCE GENERAL CONFIG
    @property
    def rag_indexer_document_loader_endpoint(self) -> str:
        return self.__indexer_loader.get_setting("endpoint")

    @property
    def rag_indexer_document_loader_key(self) -> str:
        return self.__indexer_loader.get_setting("RAG_DOCUMENT_INTELLIGENCE_KEY", True)


    #RAG INDEXER LLM GENERAL CONFIG
    @property
    def rag_indexer_llm_version(self) -> str:
        return self.__indexer_llm.get_setting("version")

    @property
    def rag_indexer_llm_endpoint(self) -> str:
        return self.__indexer_llm.get_setting("endpoint")

    @property
    def rag_indexer_llm_deployment(self) -> str:
        return self.__indexer_llm.get_setting("deployment")

    @property
    def rag_indexer_temperature(self) -> float:
        return float(self.__indexer_llm.get_setting("temperature"))

    @property
    def rag_indexer_llm_key(self) -> str:
        return self.__indexer_llm.get_setting("RAG_GENERATOR_KEY", True)


    #AZURE AI SEARCH GENERAL CONFIG
    @property
    def azure_ai_search_field_id_name(self) -> str:
        return self.__azure_ai_search_field_params.get_setting("AZURESEARCH_FIELDS_ID", True)

    @property
    def azure_ai_search_field_content_name(self) -> str:
        return self.__azure_ai_search_field_params.get_setting("AZURESEARCH_FIELDS_CONTENT", True)

    @property
    def azure_ai_search_field_vector_name(self) -> str:
        return self.__azure_ai_search_field_params.get_setting("AZURESEARCH_FIELDS_CONTENT_VECTOR", True)

    @property
    def azure_ai_search_field_metadata_name(self) -> str:
        return self.__azure_ai_search_field_params.get_setting("AZURESEARCH_FIELDS_TAG", True)


    #CALL CENTER BOT RAG CONFIG
    @property
    def call_center_bot_azure_ai_search_index_name(self) -> str:
        return self.__call_center_bot_azure_ai_search.get_setting("index")

    @property
    def call_center_bot_azure_ai_search_endpoint(self) -> str:
        return self.__call_center_bot_azure_ai_search.get_setting("endpoint")

    @property
    def call_center_bot_azure_ai_search_key(self) -> str:
        return self.__call_center_bot_azure_ai_search.get_setting("CALL_CENTER_BOT_RAG_AZURESEARCH_ADMIN_KEY", True)

    @property
    def call_center_bot_azure_ai_search_vector_profile(self) -> str:
        return self.__call_center_bot_azure_ai_search_parameters.get_setting("search_profile")

    @property
    def call_center_bot_azure_ai_search_vector_algorithm(self) -> str:
        return self.__call_center_bot_azure_ai_search_parameters.get_setting("vector_algorithm")

    @property
    def call_center_bot_azure_ai_search_semantic_reranker(self) -> str:
        return self.__call_center_bot_azure_ai_search_parameters.get_setting("semantic_configuration")

    @property
    def call_center_bot_local_root(self) -> str:
        return self.__call_center_bot_fetcher.get_setting("local_root")

    @property
    def call_center_bot_pride_products_root(self) -> str:
        return self.__call_center_bot_fetcher.get_setting("pride_products_root")

    @property
    def call_center_bot_pride_document_types(self) -> str:
        return self.__call_center_bot_fetcher.get_setting("pride_document_types")


    #HOW TO BOT RAG CONFIG
    @property
    def how_to_bot_azure_ai_search_index_name(self) -> str:
        return self.__how_to_bot_azure_ai_search.get_setting("index")

    @property
    def how_to_bot_azure_ai_search_endpoint(self) -> str:
        return self.__how_to_bot_azure_ai_search.get_setting("endpoint")

    @property
    def how_to_bot_azure_ai_search_key(self) -> str:
        return self.__how_to_bot_azure_ai_search.get_setting("HOW_TO_BOT_RAG_AZURESEARCH_ADMIN_KEY", True)

    @property
    def how_to_bot_azure_ai_search_vector_profile(self) -> str:
        return self.__how_to_bot_azure_ai_search_parameters.get_setting("search_profile")

    @property
    def how_to_bot_azure_ai_search_vector_algorithm(self) -> str:
        return self.__how_to_bot_azure_ai_search_parameters.get_setting("vector_algorithm")

    @property
    def how_to_bot_azure_ai_search_semantic_reranker(self) -> str:
        return self.__how_to_bot_azure_ai_search_parameters.get_setting("semantic_configuration")

    @property
    def how_to_bot_local_root(self) -> str:
        return self.__how_to_bot_fetcher.get_setting("local_root")

    @property
    def how_to_bot_pride_products_root(self) -> str:
        return self.__how_to_bot_fetcher.get_setting("pride_products_root")


    #JDANALLO RAG CONFIG
    @property
    def jdanallo_azure_ai_search_index_name(self) -> str:
        return self.__jdanallo_azure_ai_search.get_setting("index")

    @property
    def jdanallo_azure_ai_search_endpoint(self) -> str:
        return self.__jdanallo_azure_ai_search.get_setting("endpoint")

    @property
    def jdanallo_azure_ai_search_key(self) -> str:
        return self.__jdanallo_azure_ai_search.get_setting("JDANALLO_RAG_AZURESEARCH_ADMIN_KEY", True)

    @property
    def jdanallo_azure_ai_search_vector_profile(self) -> str:
        return self.__jdanallo_azure_ai_search_parameters.get_setting("search_profile")

    @property
    def jdanallo_azure_ai_search_vector_algorithm(self) -> str:
        return self.__jdanallo_azure_ai_search_parameters.get_setting("vector_algorithm")

    @property
    def jdanallo_azure_ai_search_semantic_reranker(self) -> str:
        return self.__jdanallo_azure_ai_search_parameters.get_setting("semantic_configuration")

    @property
    def jdanallo_local_root(self) -> str:
        return self.__jdanallo_fetcher.get_setting("local_root")

    @property
    def jdanallo_pride_products_root(self) -> str:
        return self.__jdanallo_fetcher.get_setting("pride_products_root")


    #SEOBOT RAG CONFIG
    @property
    def seobot_azure_ai_search_index_name(self) -> str:
        return self.__seobot_azure_ai_search.get_setting("index")

    @property
    def seobot_azure_ai_search_endpoint(self) -> str:
        return self.__seobot_azure_ai_search.get_setting("endpoint")

    @property
    def seobot_azure_ai_search_key(self) -> str:
        return self.__seobot_azure_ai_search.get_setting("SEOBOT_RAG_AZURESEARCH_ADMIN_KEY", True)

    @property
    def seobot_azure_ai_search_vector_profile(self) -> str:
        return self.__seobot_azure_ai_search_parameters.get_setting("search_profile")

    @property
    def seobot_azure_ai_search_vector_algorithm(self) -> str:
        return self.__seobot_azure_ai_search_parameters.get_setting("vector_algorithm")

    @property
    def seobot_azure_ai_search_semantic_reranker(self) -> str:
        return self.__seobot_azure_ai_search_parameters.get_setting("semantic_configuration")

    @property
    def seobot_local_root(self) -> str:
        return self.__seobot_fetcher.get_setting("local_root")

    @property
    def seobot_pride_products_root(self) -> str:
        return self.__seobot_fetcher.get_setting("pride_products_root")



class Text2SQLConfig(CommonConfig, metaclass=Singleton):
    def __init__(self) -> None:
        super().__init__()
        self.__oci = SubordinateConfig("txt2sql.oracle_oci")
        self.__text_to_sql_llm = SubordinateConfig("txt2sql.llm")
        self.__text_to_sql_embedding = SubordinateConfig("txt2sql.embedding")
        self.__text_to_sql_preliminary = SubordinateConfig("txt2sql.preliminary")
        self.__compli_bot_azure_ai_search = SubordinateConfig("compli_bot.txt2sql.azure_ai_search")
        self.__call_center_bot_azure_ai_search = SubordinateConfig("call_center_bot.txt2sql.azure_ai_search")

    @property
    def oci_client_path(self) -> str:
        return self.__oci.get_setting("oci_client_path")

    @property
    def oci_dsn(self) -> str:
        return self.__oci.get_setting("oci_dsn")

    @property
    def oci_schema(self) -> str:
        schema = self.__oci.get_setting("oci_schema")
        if schema:
            schema = schema + "." if len(schema) > 0 else schema
        return schema

    @property
    def oci_username(self) -> str:
        return self.__oci.get_setting("OCI_USERNAME", True)

    @property
    def oci_password(self) -> str:
        return self.__oci.get_setting("OCI_PASSWORD", True)


    @property
    def text_to_sql_llm_version(self) -> str:
        return self.__text_to_sql_llm.get_setting("version")

    @property
    def text_to_sql_llm_endpoint(self) -> str:
        return self.__text_to_sql_llm.get_setting("endpoint")

    @property
    def text_to_sql_llm_deployment(self) -> str:
        return self.__text_to_sql_llm.get_setting("deployment")

    @property
    def text_to_sql_llm_key(self) -> str:
        return self.__text_to_sql_llm.get_setting("OPENAI_LLM_KEY", True)


    @property
    def text_to_sql_preliminary_deployment(self) -> str:
        return self.__text_to_sql_preliminary.get_setting("deployment")

    @property
    def text_to_sql_preliminary_key(self) -> str:
        return self.__text_to_sql_preliminary.get_setting("PRELIMINARY_KEY", True)


    @property
    def text_to_sql_embedding_version(self) -> str:
        return self.__text_to_sql_embedding.get_setting("version")

    @property
    def text_to_sql_embedding_endpoint(self) -> str:
        return self.__text_to_sql_embedding.get_setting("endpoint")

    @property
    def text_to_sql_embedding_deployment(self) -> str:
        return self.__text_to_sql_embedding.get_setting("deployment")

    @property
    def text_to_sql_embedding_key(self) -> str:
        return self.__text_to_sql_embedding.get_setting("OPENAI_EMBEDDING_KEY", True)


    @property
    def compli_bot_azure_ai_search_index(self) -> str:
        return self.__compli_bot_azure_ai_search.get_setting("index")

    @property
    def compli_bot_azure_ai_search_endpoint(self) -> str:
        return self.__compli_bot_azure_ai_search.get_setting("endpoint")

    @property
    def compli_bot_azure_ai_search_query_key(self) -> str:
        return self.__compli_bot_azure_ai_search.get_setting("COMPLI_BOT_SEARCH_QUERY_KEY", True)

    @property
    def compli_bot_azure_ai_search_admin_key(self) -> str:
        return self.__compli_bot_azure_ai_search.get_setting("COMPLI_BOT_SEARCH_ADMIN_KEY", True)


    @property
    def call_center_bot_azure_ai_search_index(self) -> str:
        return self.__call_center_bot_azure_ai_search.get_setting("index")

    @property
    def call_center_bot_azure_ai_search_endpoint(self) -> str:
        return self.__call_center_bot_azure_ai_search.get_setting("endpoint")

    @property
    def call_center_bot_azure_ai_search_query_key(self) -> str:
        return self.__call_center_bot_azure_ai_search.get_setting("CALL_CENTER_BOT_SEARCH_QUERY_KEY", True)

    @property
    def call_center_bot_azure_ai_search_admin_key(self) -> str:
        return self.__call_center_bot_azure_ai_search.get_setting("CALL_CENTER_BOT_SEARCH_ADMIN_KEY", True)



class BlobStorageConfig(CommonConfig, metaclass=Singleton):
    def __init__(self) -> None:
        super().__init__()
        self.__call_center_bot_blob_storage_credentials = SubordinateConfig("call_center_bot.rag.azure_blob_storage")
        self.__how_to_bot_blob_storage_credentials = SubordinateConfig("how_to_bot.rag.azure_blob_storage")
        self.__jdanallo_blob_storage_credentials = SubordinateConfig("jdanallo.rag.azure_blob_storage")
        self.__seobot_blob_storage_credentials = SubordinateConfig("seobot.rag.azure_blob_storage")

    @property
    def call_center_bot_blob_storage_url(self) -> str:
        return self.__call_center_bot_blob_storage_credentials.get_setting("url")

    @property
    def call_center_bot_blob_storage_name(self) -> str:
        return self.__call_center_bot_blob_storage_credentials.get_setting("name")

    @property
    def call_center_bot_blob_storage_table_container(self) -> str:
        return self.__call_center_bot_blob_storage_credentials.get_setting("table_container")

    @property
    def call_center_bot_blob_storage_container_image(self) -> str:
        return self.__call_center_bot_blob_storage_credentials.get_setting("image_container")

    @property
    def call_center_bot_blob_storage_key(self) -> str:
        return self.__call_center_bot_blob_storage_credentials.get_setting("CALL_CENTER_BOT_BLOB_STORAGE_KEY", True)


    @property
    def how_to_bot_blob_storage_url(self) -> str:
        return self.__how_to_bot_blob_storage_credentials.get_setting("url")

    @property
    def how_to_bot_blob_storage_name(self) -> str:
        return self.__how_to_bot_blob_storage_credentials.get_setting("name")

    @property
    def how_to_bot_blob_storage_table_container(self) -> str:
        return self.__how_to_bot_blob_storage_credentials.get_setting("table_container")

    @property
    def how_to_bot_blob_storage_container_image(self) -> str:
        return self.__how_to_bot_blob_storage_credentials.get_setting("image_container")

    @property
    def how_to_bot_blob_storage_key(self) -> str:
        return self.__how_to_bot_blob_storage_credentials.get_setting("HOW_TO_BOT_BLOB_STORAGE_KEY", True)


    @property
    def jdanallo_blob_storage_url(self) -> str:
        return self.__jdanallo_blob_storage_credentials.get_setting("url")

    @property
    def jdanallo_blob_storage_name(self) -> str:
        return self.__jdanallo_blob_storage_credentials.get_setting("name")

    @property
    def jdanallo_blob_storage_table_container(self) -> str:
        return self.__jdanallo_blob_storage_credentials.get_setting("table_container")

    @property
    def jdanallo_blob_storage_container_image(self) -> str:
        return self.__jdanallo_blob_storage_credentials.get_setting("image_container")

    @property
    def jdanallo_blob_storage_key(self) -> str:
        return self.__jdanallo_blob_storage_credentials.get_setting("JDANALLO_BLOB_STORAGE_KEY", True)


    @property
    def seobot_blob_storage_url(self) -> str:
        return self.__seobot_blob_storage_credentials.get_setting("url")

    @property
    def seobot_blob_storage_name(self) -> str:
        return self.__seobot_blob_storage_credentials.get_setting("name")

    @property
    def seobot_blob_storage_table_container(self) -> str:
        return self.__seobot_blob_storage_credentials.get_setting("table_container")

    @property
    def seobot_blob_storage_container_image(self) -> str:
        return self.__seobot_blob_storage_credentials.get_setting("image_container")

    @property
    def seobot_blob_storage_key(self) -> str:
        return self.__seobot_blob_storage_credentials.get_setting("SEOBOT_BLOB_STORAGE_KEY", True)



class EproExcelLaConfig(CommonConfig, metaclass=Singleton):
    def __init__(self) -> None:
        super().__init__()
        self.__translator = SubordinateConfig("translator")
        self.__translator_llm = SubordinateConfig("translator.llm")

    @property
    def translator_azure_ai_translator_endpoint(self) -> str:
        return self.__translator.get_setting("endpoint")

    @property
    def translator_azure_ai_translator_key(self) -> str:
        return self.__translator.get_setting("TRANSLATOR_KEY", True)


    @property
    def translator_llm_version(self) -> str:
        return self.__translator_llm.get_setting("version")

    @property
    def translator_llm_endpoint(self) -> str:
        return self.__translator_llm.get_setting("endpoint")

    @property
    def translator_llm_deployment(self) -> str:
        return self.__translator_llm.get_setting("deployment")

    @property
    def translator_llm_key(self) -> str:
        return self.__translator_llm.get_setting("OPENAI_LLM_KEY", True)



class TranslatorBotConfig(CommonConfig, metaclass=Singleton):
    """
    Configuration for the translator bot.
    """
    def __init__(self) -> None:
        super().__init__()
        self.__translator_llm = SubordinateConfig("translator.llm")

    @property
    def translator_llm_api_version(self) -> str:
        return self.__translator_llm.get_setting("version")

    @property
    def translator_llm_endpoint(self) -> str:
        return self.__translator_llm.get_setting("endpoint")

    @property
    def translator_llm__deployment(self) -> str:
        return self.__translator_llm.get_setting("deployment")

    @property
    def translator_llm_key(self) -> str:
        return self.__translator_llm.get_setting("TRANSLATOR_AZURE_AI_API_KEY", True)



class ImageGenConfig(CommonConfig, metaclass=Singleton):

    def __init__(self) -> None:
        super().__init__()
        self.__image_generator = SubordinateConfig("image_generator")

    @property
    def image_generator_version(self) -> str:
        return self.__image_generator.get_setting("version")

    @property
    def image_generator_endpoint(self) -> str:
        return self.__image_generator.get_setting("endpoint")

    @property
    def image_generator_deployment(self) -> str:
        return self.__image_generator.get_setting("deployment")

    @property
    def image_generator_temperature(self) -> float:
        return float(self.__image_generator.get_setting("temperature"))

    @property
    def image_generator_key(self) -> str:
        return self.__image_generator.get_setting("IMAGE_GENERATOR_KEY", True)