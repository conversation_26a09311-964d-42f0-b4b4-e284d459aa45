import json
import os
import time
from typing import Dict
from flask import current_app
from openai import AzureOpenAI
from config.config import TranslatorBotConfig
from utils.core import Singleton

class Translator (metaclass=Singleton):


    def __init__(self):
        self.config = TranslatorBotConfig()
        
        # Azure OpenAI client 
        self.client = current_app.config["TRANSLATOR_LLM"]._client
        # Store deployment name for later use
        self.deployment_name = self.config.translator_llm__deployment

    def submit_to_gpt(self, json_obj: Dict, prompt: str, file_context: str = None, file_extension: str = None) -> str:
        """
        Submit a JSON object to Azure GPT with a translation prompt.

        Args:
            json_obj (Dict): JSON object to submit
            prompt (str): Translation prompt to use
            file_context (str, optional): Additional file context to include in the user message
        Returns:
            str: Response from GPT
        """
        start_call = time.time()
        payload_size = len(json.dumps(json_obj))
        items_count = len(json_obj.get('content', {}))

        current_app.logger.info(f"Sending request to OpenAI: {payload_size} characters, {items_count} items")
        current_app.logger.debug(f"Using deployment: {self.deployment_name}")

        try:
            # Prepare the messages
            messages = [
                {"role": "system", "content": prompt},
            ]
            if file_context:
                user_content = f"Additional context for the translation model:\n{file_context}\n\n{json.dumps(json_obj)}"
                current_app.logger.debug("Including file context in request")
            else:
                user_content = json.dumps(json_obj)
            messages.append({"role": "user", "content": user_content})

            current_app.logger.debug(f"Request messages prepared, system prompt length: {len(prompt)} chars")

              # Save messages to a text file as valid JSON
            log_dir = os.path.join(os.getcwd(), "translator_prompts")
            os.makedirs(log_dir, exist_ok=True)
            log_filename = f"messages_{file_extension}.log"
            log_path = os.path.join(log_dir, log_filename)
            
            # Read existing messages or create new array
            existing_messages = []
            if os.path.exists(log_path):
                try:
                    with open(log_path, "r", encoding="utf-8") as f:
                        content = f.read().strip()
                        if content:
                            existing_messages = json.loads(content)
                except (json.JSONDecodeError, FileNotFoundError):
                    existing_messages = []
            
            # Add new messages to existing array
            existing_messages.extend(messages)
            
            # Write back the complete array
            with open(log_path, "w", encoding="utf-8") as f:
                f.write(json.dumps(existing_messages, ensure_ascii=False, indent=2))
            current_app.logger.debug(f"Appended request messages to {log_path}")

            
            # Make the API request using Azure OpenAI client
            response = self.client.chat.completions.create(
                model=self.deployment_name,
                messages=messages,
                temperature=0,
                top_p=0.1,
            )
            duration = time.time() - start_call
            response_content = response.choices[0].message.content
            response_length = len(response_content) if response_content else 0

            current_app.logger.info(f"OpenAI response received in {duration:.2f}s, {response_length} chars")
            current_app.logger.debug(f"Response preview: {response_content[:100]}..." if response_content else "Empty response")

            return response_content
        except Exception as e:
            duration = time.time() - start_call
            error_message = f"Azure OpenAI API request failed after {duration:.2f} seconds: {str(e)}"
            current_app.logger.error(error_message)
            current_app.logger.error(f"Request details - Model: {self.deployment_name}, Items: {items_count}, Size: {payload_size}")
            raise Exception(error_message)
        
    def get_default_prompt(self, target_language: str, source_language: str = 'auto') -> str:
        """
        Get the default translation prompt for the specified target and source languages.

        Args:
            target_language (str): Target language for translation
            source_language (str): Source language for translation (default: 'auto' for auto-detection)

        Returns:
            str: Default translation prompt
        """
        if source_language == 'auto':
            source_instruction = "You will be provided a json structure with words or sentences that require translation."
        else:
            # Map language codes to full names for better prompt clarity
            language_names = {
                'en': 'English', 'es': 'Spanish', 'fr': 'French', 'de': 'German',
                'it': 'Italian', 'pt': 'Portuguese', 'ru': 'Russian', 'ja': 'Japanese',
                'ko': 'Korean', 'zh': 'Chinese', 'ar': 'Arabic', 'hi': 'Hindi',
                'th': 'Thai', 'vi': 'Vietnamese', 'nl': 'Dutch', 'sv': 'Swedish',
                'no': 'Norwegian', 'da': 'Danish', 'fi': 'Finnish', 'pl': 'Polish'
            }
            source_name = language_names.get(source_language, source_language)
            target_name = language_names.get(target_language, target_language)
            source_instruction = f"The source language is {source_name} and the target language is {target_name}. Do not translate words that are not in the source language."

        return f"""
        You are a professional translator. {source_instruction}
        Translate each element into clear, accurate {target_language}, preserving technical terminology and meaning.
        Double check the meaning of words and phrases to ensure accuracy and correct context. 
        If spaces are present in the source text, preserve them in the translation.
        If the value of some keys are a single word add a space before and after the word in the translation.
        If the walue is a single letter and the previous value was not a single letter return nothing.

        IMPORTANT: Additional context for the translation may be provided in the user message. Use it to improve translation accuracy if present.

        You must format your response as a valid JSON object where the keys are the row numbers and the values are the translated {target_language} text. For example:
        {{"1": "30kg Industrial Washing Machine", "2": "Stainless Steel Dryer with Steam Function", "3": "Commercial Ironing System"}}

        Do not include any explanations or notes outside of this JSON structure.
        """
