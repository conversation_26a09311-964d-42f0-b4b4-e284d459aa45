import json
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from src import db
from src.backend.models import User, Role, UserRoles
from sqlalchemy.orm import joinedload
from src.backend.blueprints.auth.decorators import login_epr, admin_required
from src.backend.contracts.chat_data import BotType, SQLAnswerType


rbot_admin_users = Blueprint('rbot_admin_users', __name__, template_folder='templates')

# Load user settings template
USER_SETTINGS_TEMPLATE = {
    "allowed_bots": [b.name for b in BotType],
    "search_settings": {
        "suggest_followup_questions": False,
        "top": 10,
        "show_explanation": False,
        "show_sql": False,
        "sql_answer_type": "html",
        "answer_type": "html"
    }
}

def get_user_settings_template():
    return USER_SETTINGS_TEMPLATE


@rbot_admin_users.route('/users')
@login_epr
@admin_required
def users_list():
    page = request.args.get('page', 1, type=int)
    per_page = 10
    search = request.args.get('search', '', type=str)
    query = User.query.options(joinedload(User.roles))
    if search:
        query = query.filter(User.email.ilike(f"%{search}%"))
    users = query.order_by(User.id).paginate(page=page, per_page=per_page)
    return render_template('rbot_admin/users.html', users=users)


@rbot_admin_users.route('/users/add', methods=['GET', 'POST'])
@login_epr
@admin_required
def add_user():
    roles = Role.query.all()
    bot_types = [b.name for b in BotType]
    sql_answer_types = [a.value for a in SQLAnswerType]
    if request.method == 'POST':
        email = request.form['email']
        name = request.form['name']
        surname = request.form['surname']
        role_id = request.form['role']
        allowed_bots = request.form.getlist('allowed_bots')
        suggest_followup_questions = 'suggest_followup_questions' in request.form
        show_explanation = 'show_explanation' in request.form
        show_sql = 'show_sql' in request.form
        top = int(request.form.get('top', 1))
        sql_answer_type = request.form.get('sql_answer_type', 'html')
        answer_type = request.form.get('answer_type', 'html')
        user_settings_json = {
            "allowed_bots": allowed_bots,
            "search_settings": {
                "suggest_followup_questions": suggest_followup_questions,
                "top": top,
                "show_explanation": show_explanation,
                "show_sql": show_sql,
                "sql_answer_type": sql_answer_type,
                "answer_type": answer_type
            }
        }
        user = User(email=email, name=name, surname=surname, user_settings=user_settings_json)
        db.session.add(user)
        db.session.commit()
        user_role = UserRoles(user_id=user.id, role_id=role_id)
        db.session.add(user_role)
        db.session.commit()
        flash('Utente aggiunto con successo', 'success')
        return redirect(url_for('rbot_admin.rbot_admin_users.users_list'))
    return render_template('rbot_admin/user_form.html', roles=roles, user_settings_template=get_user_settings_template(), bot_types=bot_types, sql_answer_types=sql_answer_types)


@rbot_admin_users.route('/users/edit/<int:user_id>', methods=['GET', 'POST'])
@login_epr
@admin_required
def edit_user(user_id):
    user = User.query.get_or_404(user_id)
    roles = Role.query.all()
    user_role = UserRoles.query.filter_by(user_id=user.id).first()
    bot_types = [b.name for b in BotType]
    sql_answer_types = [a.value for a in SQLAnswerType]
    if request.method == 'POST':
        user.email = request.form['email']
        if request.form.get('password'):
            user.password = request.form['password']
        user.name = request.form['name']
        user.surname = request.form['surname']
        role_id = request.form['role']
        allowed_bots = request.form.getlist('allowed_bots')
        suggest_followup_questions = 'suggest_followup_questions' in request.form
        show_explanation = 'show_explanation' in request.form
        show_sql = 'show_sql' in request.form
        top = int(request.form.get('top', 1))
        sql_answer_type = request.form.get('sql_answer_type', 'html')
        answer_type = request.form.get('answer_type', 'html')
        user_settings_json = {
            "allowed_bots": allowed_bots,
            "search_settings": {
                "suggest_followup_questions": suggest_followup_questions,
                "top": top,
                "show_explanation": show_explanation,
                "show_sql": show_sql,
                "sql_answer_type": sql_answer_type,
                "answer_type": answer_type
            }
        }
        user.user_settings = user_settings_json
        if user_role:
            user_role.role_id = role_id
        else:
            db.session.add(UserRoles(user_id=user.id, role_id=role_id))
        db.session.commit()
        flash('Utente modificato con successo', 'success')
        return redirect(url_for('rbot_admin.rbot_admin_users.users_list'))
    return render_template('rbot_admin/user_form.html', user=user, roles=roles, user_settings_template=get_user_settings_template(), edit=True, bot_types=bot_types, sql_answer_types=sql_answer_types)


@rbot_admin_users.route('/users/delete/<int:user_id>', methods=['POST'])
@login_epr
@admin_required
def delete_user(user_id):
    user = User.query.get_or_404(user_id)
    db.session.delete(user)
    db.session.commit()
    flash('Utente eliminato', 'success')
    return redirect(url_for('rbot_admin_users.users_list'))
