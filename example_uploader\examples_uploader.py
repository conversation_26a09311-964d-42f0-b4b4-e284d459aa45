import argparse
import logging
import os
from concurrent.futures import wait
from concurrent.futures.thread import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>xecutor
from io import String<PERSON>
from json import dump, dumps, load
from re import MULTILINE, sub
from threading import current_thread
from typing import List, Union

import pandas as pd
import sqlparse
from azure.core.credentials import AzureKeyCredential
from azure.search.documents import SearchClient

from config.config import Text2SQLConfig
from src.common_tools.embedders.inquiry_embedder import InquiryEmbedder
from src.common_tools.embedders.sql_embedder import SqlEmbedder
from utils.core import get_logger

OLD_EXAMPLES_PATH = os.path.join(os.path.dirname(__file__), "./examples.parquet")
TEST_SET_PATH = os.path.join(os.path.dirname(__file__), "../tests/test_set.json")

logger = get_logger(__file__)


def _create_examples(synonyms_path: str) -> pd.DataFrame:
    """
    Loads the synonyms JSO<PERSON> as a DataFrame with the examples produced so far.

    It automatically dumps the JSON containing the test set in the test folder.
    """
    cfg = Text2SQLConfig()
    with open(synonyms_path, encoding="utf8") as file:
        raw = load(file)

    restructured = {}
    test_set = {}

    for key, examples in raw.items():
        for example in examples:
            new_key = key + "_" + str(example["id"])

            # Replace all whitespaces with a single space in SQL query
            sql = sub("\s+", " ", example["sql"], flags=MULTILINE)
            # Turn all SQL keywords in uppercase
            sql = sqlparse.format(sql, keyword_case="upper")
            # Replace the schema mask with the name of the schema used
            sql = sql.replace("{SCHEMA}", cfg.oci_schema)
            bot_array = example["bot"].split(",")

            new_value = {"inquiry": example["inquiry"], "sql": sql, "bot": bot_array}

            if example["id"] != "test":
                restructured[new_key] = new_value
            else:
                test_set[new_key] = new_value

    new_examples = pd.read_json(StringIO(dumps(restructured)), orient="index")

    with open(os.path.join(os.path.dirname(__file__), TEST_SET_PATH), "w") as file:
        dump(test_set, file)

    return new_examples


def __create_document(
    id: Union[int, str],
    inquiry: str,
    inquiry_embedder: InquiryEmbedder,
    sql: str,
    sql_embedder: SqlEmbedder,
    bot_category: List[str],
):
    """ "
    Creates the document to upload to a cloud index.
    """
    document = {
        "example_id": str(id),
        "plain_inquiry": inquiry,
        "plain_sql": sql,
        "embedded_inquiry": inquiry_embedder.embed(inquiry),
        "embedded_sql": sql_embedder.embed(sql),
        "bot": bot_category,
    }
    logger.info(f"Document {id} composed!")
    return document


def upload_examples(examples: pd.DataFrame, mode: str) -> None:
    """
    Uploads examples from dataset made of at least the columns [question, query] and an index.
    """
    # Loading client configuration
    logger.debug("Loading configuration...")
    cfg = Text2SQLConfig()
    # Init client
    logger.debug("Loading client...")
    client = SearchClient(
        cfg.compli_bot_azure_ai_search_endpoint,
        cfg.compli_bot_azure_ai_search_index,
        AzureKeyCredential(str(cfg.compli_bot_azure_ai_search_admin_key)),
    )
    logger.info("Client successfully initialised!")

    # Init embedders
    logger.debug("Initialising embedders...")
    inqury_embedder = InquiryEmbedder()
    sql_embedder = SqlEmbedder(
        os.path.join(
            os.path.dirname(__file__),
            "../src/common_tools/embedders/keywords_dictionary.json",
        )
    )
    logger.info("Embedders initialised!")

    to_upload = pd.DataFrame()

    if mode == "insert":
        logger.debug("Inserting new examples...")

        to_upload = (
            examples.reset_index()
            .merge(
                pd.read_parquet(OLD_EXAMPLES_PATH).reset_index(),
                how="left",
                on=["index", "inquiry", "sql"],
                indicator=True,
            )
            .set_index("index")
        )
        to_upload = to_upload[to_upload["_merge"] == "left_only"]
        to_upload = to_upload.drop(columns=["_merge"])
    else:
        logging.debug("Upserting all examples...")
        to_upload = examples

    logger.info(f"{len(to_upload)} examples have been selected for upload!")

    # Threaded creation of documents
    logger.debug("Turning examples into documents for upload...")
    executor = ThreadPoolExecutor(
        len(examples.index), thread_name_prefix=current_thread().name + "_uploader"
    )
    futures = [
        executor.submit(
            __create_document,
            id,
            data["inquiry"],
            inqury_embedder,
            data["sql"],
            sql_embedder,
            data["bot"] if mode == "upsert" else data["bot_x"],
        )
        for id, data in to_upload.iterrows()
    ]
    futures = wait(futures)

    # Extracting documents to upload
    to_upload = [future.result() for future in iter(futures.done)]
    logger.info(
        f"{len(to_upload)} examples have been successfully converted to documents!"
    )

    # Upload documents
    logger.debug("Uploading documents...")
    client.upload_documents(to_upload)
    logger.info("Documents have been uploaded!")


def get_args():
    parser = argparse.ArgumentParser(
        description="Upload the examples provided in an indexed JSON file"
    )

    parser.add_argument(
        "--JSON",
        "-J",
        type=str,
        dest="examples_path",
        default=os.path.join(os.path.dirname(__file__), "./synonyms.json"),
    )

    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument(
        "--upsert",
        "-u",
        dest="upsert",
        action=argparse.BooleanOptionalAction,
        default=False,
    )
    group.add_argument(
        "--insert",
        "-i",
        dest="insert",
        action=argparse.BooleanOptionalAction,
        default=False,
    )

    return parser.parse_args()


if __name__ == "__main__":
    args = get_args()

    mode = "upsert" if args.upsert else "insert"
    current_examples = _create_examples(args.examples_path)

    upload_examples(current_examples, mode)

    current_examples.to_parquet(OLD_EXAMPLES_PATH)
