import re
from json import load
from typing import Dict, Iterator, List, Optional, Union

import sqlparse


def is_create_statement(string: str) -> bool:
    """
    Check if a string is a "CREATE [...] TABLE..." statement
    """
    # Assuming `string` is an SQL statement, change the case of keywords to uppercase
    string = sqlparse.format(string, keyword_case="upper")
    # Parse `string` and isolate the first keyword
    first_keyword = [token for token in sqlparse.parse(string)[0].flatten()][0].value
    # String can create a table if the first keyword contains "CREATE" and `string` contains "TABLE"
    return "CREATE" in first_keyword and "TABLE" in string


class Column:
    def __init__(
        self,
        name: str,
        aliases: Optional[List[str]] = [],
        description: Optional[str] = "",
        metadata: Optional[dict] = {}
    ) -> None:
        """
        Represents a database column and tries to capture its semantic.
        """
        self.name = name
        self.aliases = aliases.copy()
        self.description = description
        self.metadata = metadata
    

    def add_alias(self, alias: str) -> None:
        """
        Adds another alias for the column's name if not already present.
        """
        if alias not in self.aliases:
            self.aliases.append(alias)

    def to_dict(self) -> Dict[str, Union[str, List[str]]]:
        """
        Converts the column's representation representation to a dictionary
        """
        return {
            self.name: {"aliases": self.aliases.copy(), "description": self.description}
        }


class Table:
    def __init__(
        self,
        name: str,
        columns: List[Column],
        schema: str,
        aliases: Optional[List[str]] = [],
        description: Optional[str] = "",
        example_id: Optional[str] = ""
    ) -> None:
        """
        Represents a table's semantics using a column-major representation.
        """
        self.name = name
        if is_create_statement(schema):
            self.schema = sqlparse.format(
                schema.strip(), keyword_case="upper", use_space_around_operators=True
            )
        else:
            raise Exception(
                f"Schema not valid for table {name}. Inserted schema:\n{schema}"
            )
        self._columns = columns.copy()
        self.aliases = aliases.copy()
        self.description = description
        self.example_id = example_id

    def add_column(self, col: Column) -> None:
        """
        Adds a column to the table's representation. If a column with the same name as `col` is present, then its values are updated.
        """
        # Check for duplicates
        duplicate = [column for column in self._columns if column.name == col.name]

        if duplicate:
            # Update the duplicate
            duplicate[0].aliases = col.aliases
            duplicate[0].description = col.description
        else:
            # Add new column
            self._columns.append(col)

    def add_alias(self, alias: str) -> None:
        """
        Adds an alias for the table's name if not already present.
        """
        if alias not in self.aliases:
            self.aliases.append(alias)

    def columns(self) -> Iterator[Column]:
        return iter(self._columns)

    def to_dict(self) -> Dict[str, Union[str, List[str]]]:
        """
        Converts the column's representation representation to a dictionary.
        """
        raw_columns = {}

        for column in self._columns:
            raw_columns[column.name] = column.to_dict()

        return {
            self.name: {
                "columns": raw_columns,
                "schema": self.schema,
                "aliases": self.aliases.copy(),
                "description": self.description,
            }
        }


class DatabaseSchema:
    def __init__(self, name: str, tables: List[Table]) -> None:
        """
        Represents a database schema as a set of `Table`s.
        """
        self.name = name
        self._tables = tables.copy()

    def add_table(self, table: Table) -> None:
        """
        Adds a table to the dataset's representation. Does nothing if there already is a table sharing the same name as `table`.
        """
        # Check for duplicates
        duplicate = [dup_tab for dup_tab in self._tables if dup_tab.name == table.name]

        if not duplicate:
            # Add new column
            self._tables.append(table)

    def tables(self) -> Iterator[Table]:
        """
        Returns an iterator over the databases's tables representations.
        """
        return iter(self._tables)

    def to_dict(self) -> Dict[str, Union[str, List[Table]]]:
        """
        Returns the dictionary representation of the database.
        """
        raw_tables = {}

        for table in self._tables:
            raw_tables[table.name] = table.to_dict()

        return {self.name: {"tables": raw_tables}}
