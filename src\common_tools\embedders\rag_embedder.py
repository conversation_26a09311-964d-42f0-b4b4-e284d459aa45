import argparse
import time
from typing import List

import httpx
from openai import AzureOpenAI, RateLimitError

from config.config import RAGConfig
from utils.core import Singleton, get_logger

logger = get_logger(__file__)


class RAGEmbedder(metaclass=Singleton):

    def __init__(self) -> None:
        """
        Initialises the embedder by connecting to the Azure OpenAI server with the parameters in the environment.
        """
        # Fetching the embedder's configuration
        logger.debug("Initialising clients...")
        self.cfg = RAGConfig()

        self.client = AzureOpenAI(
            api_key=self.cfg.rag_embedder_key,
            api_version=self.cfg.rag_embedder_version,
            azure_endpoint=self.cfg.rag_embedder_endpoint,
            proxy=None,
        )

        logger.info("Clients have been initialised!")

    def embed(self, inquiry: str) -> List[float]:
        """
        Embeds `inquiry` into a representative float array with length 1536.
        """
        logger.debug(f"Embedding inquiry...")
        # Inovkes the OpenAI embedder
        for i in range(5):
            try:
                response = self.client.embeddings.create(
                    input=inquiry, model=self.cfg.rag_embedder_deployment
                )
            except RateLimitError:
                time.sleep((i + 1) * 10)
                continue
            break

        embedding = response.data[0].embedding

        logger.info(
            f"Client succesfully embedded the inquiry, returning a {len(embedding)}D vector!"
        )

        return embedding


def get_args() -> argparse.Namespace:
    """
    Extract arguments from the CLI where this file is invoked.
    """
    parser = argparse.ArgumentParser(
        description="Embeds a string representing an inqury into a float array with fixed dimension."
    )
    parser.add_argument("inquiry", type=str, help="A single inquiry")
    return parser.parse_args()


if __name__ == "__main__":
    args = get_args()

    print(RAGEmbedder().embed(args.inquiry))
