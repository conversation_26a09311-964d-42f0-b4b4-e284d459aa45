import argparse
from io import TextIOWrapper
from json import dump, load
from os.path import isfile
from typing import Dict

import sqlparse

from src.backend.contracts.chat_data import BotType
from utils.core import get_logger, get_relative_path

logger = get_logger(__file__)


def get_table_name(tokens: list) -> str:
    for token in reversed(tokens):
        if token.ttype is None:
            return token.value.strip('"')
    return ""


def get_table_schema(sql: str) -> Dict[str, Dict]:
    statement = sqlparse.format(sql, keyword_case="upper", strip_comments=True).strip()
    statement = sqlparse.parse(statement)[0]

    # Get all the tokens except whitespaces
    tokens = [
        t
        for t in sqlparse.sql.TokenList(statement.tokens)
        if t.ttype != sqlparse.tokens.Whitespace
    ]

    raw_table = {}

    is_create_stmt = False
    for i, token in enumerate(tokens):
        # Is it a create statement?
        if token.match(sqlparse.tokens.DDL, "CREATE"):
            is_create_stmt = True
            continue

        # If it was a create statement and the current token starts with "("
        if is_create_stmt and token.value.startswith("("):
            # Get the table name by looking at the tokens in reverse order till you find
            # a token with None type
            table_name = get_table_name(tokens[:i])
            raw_table[table_name] = {
                "schema": sql,
                "aliases": [],
                "description": "",
                "example_id": "",
                "columns": {},
            }

            # Now parse the columns
            txt = token.value
            columns = txt[1 : txt.rfind(")")].replace("\n", "").split(",")
            for column in columns:
                c = " ".join(column.split()).split()
                c_name = c[0].replace('"', "")
                if not c_name.endswith(")") and not (
                    "PRIMARY" in c_name or "FOREIGN" in c_name or "--" in c_name
                ):
                    raw_table[table_name]["columns"][c_name] = {
                        "aliases": [],
                        "description": "",
                    }

            break

    return raw_table


def to_json(ddl: str, output_file: str) -> None:
    logger.info(f"DDL read:\n{ddl}")

    table = get_table_schema(ddl)
    logger.info(f"DDL parsed:\n{table}")

    with open(output_file) as file:
        schema = load(file)
        schema["DWH_PUBLIC"]["tables"].update(table)
        logger.info(f"Table added")

    with open(output_file, "w") as file:
        logger.debug("Dumping new JSON")
        dump(schema, file, indent=4)
        logger.info("JSON has been dumped")


def get_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser(
        description="Transforms an SQL DDL file, making it JSON-storable"
    )
    parser.add_argument(
        "bot_name",
        type=str,
        help="The name of the bot that will use the database schema.",
    )

    parser.add_argument(
        "-d",
        "--dir",
        "--directory",
        type=str,
        help="The directory containing all the schemas.",
        default=get_relative_path(__file__, "."),
    )
    return parser.parse_args()


if __name__ == "__main__":
    # logger.basicConfig(level=logger.INFO)
    args = get_args()

    match args.bot_name:
        case BotType.COMPLI_BOT.name:
            tables = BotType.COMPLI_BOT.tables
            output_file_path = BotType.COMPLI_BOT.db_schema_path
        case BotType.CALL_CENTER_BOT.name:
            tables = BotType.CALL_CENTER_BOT.tables
            output_file_path = BotType.CALL_CENTER_BOT.db_schema_path
        case _:
            print(
                f'Bot "{args.bot_name}" does not have access to any table. If you believe this message is wrong, check the spelling.'
            )
            tables = None

    if not isfile(output_file_path):
        with open(output_file_path, "w") as file:
            base = {"DWH_PUBLIC": {"description": "", "tables": {}}}
            dump(base, file)

    for table_name in tables:
        with open(get_relative_path(__file__, f"./{table_name}.sql")) as ddl_file:
            ddl = ddl_file.read()
        to_json(ddl, output_file_path)

    if tables:
        print("Conversion done!")
