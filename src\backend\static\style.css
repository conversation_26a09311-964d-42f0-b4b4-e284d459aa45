/* finally, import Bootstrap */
:root {
    --epr-blue: #14133B;
    --epr-gold: #cea941;
    --epr-spartan-blue: #7b8a9c;
    --epr-mushroom-forest: #8d8060;
    --epr-trailblazer: #bfb08f;
    --epr-cotton-seed: #bfbab0;
    --epr-fog: #d9d5d2;
    --epr-danger: #ee6a1f;
    --epr-secondary: #e1edf4;

    --epr-green: #00C89A;
    --epr-flower-blue: #433D6B;
    --epr-magnolia: #F4ECFF;
}

.typewriter {
    font-size: 2.5rem;
    overflow: hidden;
    margin-top: 6%;
    border-right: .15em solid orange;
    white-space: nowrap;
    width: 0;
    animation:
        typing 2s steps(30, end) forwards,
        blink .75s infinite;
    animation-delay: 5.5s;
}

@keyframes typing {
    from {
        width: 0
    }

    to {
        width: 100%
    }
}

@keyframes blink {
    from {
        border-color: transparent
    }

    to {
        border-color: orange;
    }
}


main {
    padding-bottom: 76px; /*footer height */
}

main>.container {
    padding: 60px 15px 0;
}

.bg-epr {
    --bs-bg-opacity: 1;
    background-color: var(--epr-blue) !important;
}

.btn-primary {
    background-color: var(--epr-blue) !important;
    border-color: var(--epr-blue) !important;
    transition: opacity 0.2s ease-in-out;
}

.btn-primary:hover {
    opacity: 0.76;
}

.btn-warning {
    background-color: var(--epr-gold) !important;
    border-color: var(--epr-gold) !important;
    transition: opacity 0.2s ease-in-out;
    color: white !important;
}

.btn-warning:hover {
    opacity: 0.76;
}

.btn-secondary {
    background-color: var(--epr-secondary) !important;
    border-color: var(--epr-secondary) !important;
    transition: opacity 0.2s ease-in-out;
    color: var(--epr-blue);
}

.btn-secondary:hover {
    opacity: 0.76;
}

.btn-danger {
    background-color: var(--epr-danger) !important;
    border-color: var(--epr-danger) !important;
    transition: opacity 0.2s ease-in-out;
}

.btn-danger:hover {
    opacity: 0.76;
}

.active>.page-link,
.page-link.active {
    background-color: var(--epr-blue);
    border-color: var(--epr-blue);
}

.page-link {
    color: var(--epr-blue);
}

.page-link:hover {
    color: var(--epr-blue);
}

/* Enhanced Navbar Styles */
.navbar {
    box-shadow: 0 2px 12px rgba(20, 19, 59, 0.15);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    padding: 0.75rem 0;
}

.navbar-brand {
    display: flex;
    align-items: center;
    font-weight: 600;
    font-size: 1.25rem;
    color: white !important;
    text-decoration: none;
    transition: opacity 0.2s ease;
}

.navbar-brand:hover {
    opacity: 0.9;
}

.navbar-brand img {
    margin-right: 12px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
    transition: transform 0.2s ease;
}

.navbar-brand:hover img {
    transform: scale(1.05);
}

.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    margin: 0 0.25rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
    text-decoration: none;
}

.navbar-nav .nav-link:hover {
    color: white !important;
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.navbar-nav .nav-link.active,
.navbar-nav .nav-item.active .nav-link {
    color: white !important;
    background-color: rgba(255, 255, 255, 0.15);
    font-weight: 600;
}

.navbar-nav .nav-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--epr-gold);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::before,
.navbar-nav .nav-link.active::before {
    width: 80%;
}

.navbar-toggler {
    border: none;
    padding: 0.25rem 0.5rem;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.25);
}

.navbar-toggler:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.9%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='m4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* Version and Environment Display Enhancements */
#runningEnvDisplay {
    font-size: 0.75rem;
    font-weight: 600;
    letter-spacing: 0.5px;
    padding: 0.4rem 0.8rem;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(5px);
    transition: all 0.2s ease;
}

#runningEnvDisplay:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

#versionDisplay {
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.8) !important;
    transition: all 0.2s ease;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    border: 1px solid transparent;
}

#versionDisplay:hover {
    color: white !important;
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

/* Additional navbar enhancements */
.navbar-brand span {
    font-size: 1.1rem;
    font-weight: 600;
    margin-left: 0.5rem;
}

.navbar-nav .nav-link i {
    font-size: 0.9rem;
    opacity: 0.8;
    transition: opacity 0.2s ease;
}

.navbar-nav .nav-link:hover i {
    opacity: 1;
}

/* Smooth collapse animation */
.navbar-collapse {
    transition: all 0.3s ease-in-out;
}

.navbar-collapse.collapsing {
    transition: height 0.3s ease;
}

/* Enhanced badge styling for environment display */
.badge.bg-secondary {
    background: linear-gradient(135deg, var(--epr-spartan-blue), var(--epr-blue)) !important;
    border: 1px solid rgba(255, 255, 255, 0.2);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Version display improvements */
#versionDisplay .version-text {
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    font-weight: 500;
}

/* Responsive navbar improvements */
@media (max-width: 768px) {
    .navbar {
        padding: 0.5rem 0;
    }

    .navbar-brand span {
        font-size: 1rem;
    }

    .navbar-collapse {
        background-color: rgba(20, 19, 59, 0.95);
        margin-top: 1rem;
        border-radius: 12px;
        padding: 1rem;
        border: 1px solid rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
    }

    .navbar-nav .nav-link {
        margin: 0.25rem 0;
        padding: 0.75rem 1rem !important;
        border-radius: 8px;
    }

    .navbar-nav .nav-link::before {
        display: none;
    }

    .navbar-nav .nav-link:hover {
        background-color: rgba(255, 255, 255, 0.15);
    }
}

@media (max-width: 576px) {
    .navbar-brand span {
        display: none !important;
    }

    #runningEnvDisplay {
        font-size: 0.7rem;
        padding: 0.3rem 0.6rem;
    }

    #versionDisplay {
        font-size: 0.8rem;
    }
}