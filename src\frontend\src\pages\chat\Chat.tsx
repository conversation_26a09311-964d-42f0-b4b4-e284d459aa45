import { Checkbox, ChoiceGroup, DefaultButton, IChoiceGroupOption, PrimaryButton, Label, Panel, SpinButton } from "@fluentui/react";
import { Delete24Regular, Person24Regular, Settings24Regular, SparkleFilled } from "@fluentui/react-icons";
import { Dropdown, IDropdownOption } from '@fluentui/react/lib/Dropdown';
import { nanoid } from 'nanoid';
import { useEffect, useRef, useState } from "react";

import styles from "./Chat.module.css";

import {
    ApproachType,
    BotChangeRequest,
    ChatError,
    ChatRequest,
    ChatResponse,
    ChatResponseError,
    FeedbackRequest,
    FeedbackType,
    SearchSettings,
    UserProfile,
    UserQuestion,
    botChange,
    chatApi,
    chatStop,
    clearChatSession,
    feedbackApi,
    Agents,
    Bots,
} from "../../api";
import { AnalysisPanel, AnalysisPanelTabs } from "../../components/AnalysisPanel";
import { Answer, AnswerLoading } from "../../components/Answer";
import { ErrorToast } from "../../components/ErrorToast";
import { ExampleList } from "../../components/Example";
import { QuestionInput } from "../../components/QuestionInput";
import { TopBarButton } from "../../components/TopBarButton";
import { UserChatMessage } from "../../components/UserChatMessage";
import { UploadFile } from "../../components/LoadFile/LoadFile";
import { current } from "immer";

// Estendo l'interfaccia UserQuestion per includere le immagini
interface ExtendedUserQuestion extends UserQuestion {
    images?: File[];
}

type Props = {
    users: UserProfile[];
    searchSettings: SearchSettings;
    botList: {key: string; text: string}[];
};

const Chat = ({ users, searchSettings, botList }: Props) => {
    const [isUserPanelOpen, setIsUserPanelOpen] = useState(false);
    const [isConfigPanelOpen, setIsConfigPanelOpen] = useState(false);
    const [retrieveCount, setRetrieveCount] = useState<number>(searchSettings.top ?? 10);
    const [showExplanation, setShowExplanation] = useState<boolean>(searchSettings.show_explanation ?? false);
    const [showSQL, setShowSQL] = useState<boolean>(searchSettings.show_sql ?? false);
    const [useSemanticRanker, setUseSemanticRanker] = useState<boolean>(true);
    const [useSuggestFollowupQuestions, setUseSuggestFollowupQuestions] = useState<boolean>(searchSettings.suggest_followup_questions ?? false);

    const chatMessageStreamEnd = useRef<HTMLDivElement | null>(null);

    const [conversationID, setConversationID] = useState<string>();

    const [selectedUser, setSelectedUser] = useState<UserProfile>();

    // Modifico il tipo per includere le immagini
    const [lastQuestion, setLastQuestion] = useState<ExtendedUserQuestion | undefined>(undefined);
    const [isAnswerLoading, setIsAnswerLoading] = useState<boolean>(false);
    const [answerError, setAnswerError] = useState<ChatError | undefined>();
    const [isChatStopped, setIsChatStopped] = useState<boolean>(false);

    const [activeCitation, setActiveCitation] = useState<string>();
    const [activeAnalysisPanelTab, setActiveAnalysisPanelTab] = useState<AnalysisPanelTabs | undefined>(undefined);

    const [selectedAnswer, setSelectedAnswer] = useState<number>(0);
    // Modifico il tipo per includere le immagini nel dialog
    const [dialog, setDialog] = useState<[user: string, response: ChatResponse | ReadableStreamDefaultReader, images?: File[]][]>([]);
    const [selectedBot, setSelectedBot] = useState<string | number | null>(null);
    const [toUpload, setToUpload] = useState<boolean>();
    const [uploadStatus, setUploadStatus] = useState<boolean>(false);
    const [preview, setPreview] = useState<boolean>(true);
    const [context, setContext] = useState<ChatResponse[]>([])

    useEffect(() => {
        if (users.length > 0) {
            setSelectedUser(users[0]);
        }

        setConversationID(nanoid());
    }, []);

    useEffect(() => chatMessageStreamEnd.current?.scrollIntoView({ behavior: "smooth" }), [isAnswerLoading]);

    useEffect(() => {
        if (lastQuestion) {
            if (lastQuestion.agent_to_call) {
                if (lastQuestion.document_types){
                    getQuestionAnswer(lastQuestion.question, lastQuestion.classificationOverride, lastQuestion.agent_to_call, lastQuestion.document_types, lastQuestion.images);
                }else{
                    getQuestionAnswer(lastQuestion.question, lastQuestion.classificationOverride, lastQuestion.agent_to_call, undefined, lastQuestion.images);
                }
            }
            else {
                getQuestionAnswer(lastQuestion.question, lastQuestion.classificationOverride, undefined, undefined, lastQuestion.images);
            }
        }
    }, [lastQuestion]);

    // Funzione helper per convertire File in base64
    const fileToBase64 = (file: File): Promise<string> => {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => resolve(reader.result as string);
            reader.onerror = error => reject(error);
        });
    };

    // Funzione helper per processare le immagini
    const processImages = async (images?: File[]): Promise<string[]> => {
        if (!images || images.length === 0) return [];
        
        try {
            const base64Images = await Promise.all(
                images.map(async (file) => await fileToBase64(file))
            );
            return base64Images;
        } catch (error) {
            console.error('Errore nella conversione delle immagini:', error);
            return [];
        }
    };

    // Aggiorno la funzione updateLastQuestion per gestire le immagini
    const updateLastQuestion = (
        question?: string, 
        classificationOverride: ApproachType | undefined = undefined, 
        agent_to_call: Agents | undefined = undefined, 
        document_types: Array<string> | undefined = undefined,
        images?: File[]
    ) => {
        if (question)  {
            if (agent_to_call) {
                if (document_types) {
                    setLastQuestion({ question, classificationOverride, agent_to_call, document_types, images});
                }
                else{
                    setLastQuestion({ question, classificationOverride, agent_to_call, images});
                }
            }
            else{
                setLastQuestion({ question, classificationOverride, images});
            }
        } else {
            setLastQuestion(undefined);
        }
    };

    // Aggiorno getQuestionAnswer per gestire le immagini
    const getQuestionAnswer = async (
        question: string, 
        classificationOverride: ApproachType | undefined = undefined, 
        agent_to_call: Agents | undefined = undefined, 
        document_types: Array<string> | undefined = undefined,
        images?: File[]
    ) => {
        answerError && setAnswerError(undefined);
        if (preview){
            setIsAnswerLoading(true);
        }
        setIsChatStopped(false);
        setActiveCitation('active');
        setActiveAnalysisPanelTab(undefined);

        try {
            // Processo le immagini se presenti
            const processedImages = await processImages(images);

            const request: ChatRequest = {
                userID: selectedUser ? selectedUser.user_id : "",
                conversationID: conversationID ? conversationID : "",
                dialogID: nanoid(),
                dialog: question,
                overrides: {
                    showExplanation: showExplanation,
                    showSQL : showSQL,
                    top: retrieveCount,
                    //*temperature: temperature,
                    suggestFollowupQuestions: useSuggestFollowupQuestions,
                },
                preview: preview,
                agent_to_call: agent_to_call,
                document_types: document_types,
                // Aggiungo le immagini alla richiesta
                images: processedImages.length > 0 ? processedImages : undefined
            };

            const result = await chatApi(request);

            // Aggiungo le immagini al dialog
            setDialog([...dialog, [question, result, images]]);

        } catch (e) {
            if (e instanceof ChatResponseError) {
                setAnswerError({ message: e.message, retryable: e.retryable });
            } else if (e instanceof Error) {
                setAnswerError({ message: e.message, retryable: true });
            }
            console.log(`Error getting answer from /chat API: ${e}`);
        } finally {
            setIsAnswerLoading(false);
        }
    };

    const handleStopChat = async () => {
        try {
            if (selectedUser && conversationID) {
                await chatStop(conversationID);
                setIsChatStopped(true);
            }
        } catch (e) {
            console.log(`Failed to stop chat: ${e}`);
        }
        setAnswerError({ message: "Stopped", retryable: false });
        setIsAnswerLoading(false);
    };

    const handleBotChange = (event: React.FormEvent<HTMLDivElement>, option?: IDropdownOption) => {
        if (option) {
            setSelectedBot(option.key);
            const request: BotChangeRequest = {
                userID: selectedUser ? selectedUser.user_id : "",
                conversationID: conversationID ? conversationID : "",
                dialogID: '',
                botType: option.key as string,
            };
            botChange(request);
            if (request.botType === "EPROEXCELLA_BOT") {
                setToUpload(true)
                setUploadStatus(false)
                setPreview(true)
            } else {
                setToUpload(false)
                setUploadStatus(true)
                setPreview(true)
            }
        }
    };

    const handleContextData = (contextData: any | ChatResponse) => {
        setContext(currentContext => {
            const filteredContext = currentContext.filter(
                (data) =>
                    !(
                        (
                            data.answer.explanation === "metadata-incomplete" &&
                            contextData.answer.explanation !== "metadata-incomplete" &&
                            data.question === contextData.question
                        ) ||
                        (
                            data.answer.explanation === "metadata-incomplete" &&
                            contextData.answer.explanation === "metadata-incomplete" &&
                            data.question === contextData.question &&
                            data.dialog_id !== contextData.dialog_id

                        ) || (
                            data.answer.agent !== contextData.answer.agent &&
                            data.question === contextData.question
                        )
                    )
            );

            const exists = filteredContext.some((data) => JSON.stringify(data) === JSON.stringify(contextData));

            return exists ? currentContext : [...filteredContext, contextData];
        });
    };

    const retryWithOverride = (classificationOverride: ApproachType) => {
        setIsAnswerLoading(true);
        const lastUserQuestion = dialog[dialog.length - 1][0];
        const lastUserImages = dialog[dialog.length - 1][2]; // Recupero le immagini
        setActiveAnalysisPanelTab(undefined);
        setDialog(dialog.slice(0, -1));
        updateLastQuestion(lastUserQuestion, classificationOverride, undefined, undefined, lastUserImages);
    };

    const retryWithoutPreview = (classificationOverride: ApproachType) => {
        setPreview(false)
        setIsAnswerLoading(true);
        const lastUserQuestion = dialog[dialog.length - 1][0];
        const lastUserImages = dialog[dialog.length - 1][2]; // Recupero le immagini
        setDialog(dialog.slice(0, -1));
        updateLastQuestion(lastUserQuestion, classificationOverride, undefined, undefined, lastUserImages);
    };

    const changeAgent = (agent_to_call: Agents, document_types: Array<string> | undefined = undefined) => {
        setIsAnswerLoading(true);
        setPreview(true)
        const lastUserQuestion = dialog[dialog.length - 1][0];
        const lastUserImages = dialog[dialog.length - 1][2]; // Recupero le immagini
        setActiveAnalysisPanelTab(undefined);
        setDialog(dialog.slice(0, -1));
        updateLastQuestion(lastUserQuestion, undefined, agent_to_call, document_types, lastUserImages);
    };

    const clearChat = async () => {
        updateLastQuestion(undefined);
        answerError && setAnswerError(undefined);
        setActiveCitation('active');
        setActiveAnalysisPanelTab(undefined);
        setDialog([]);
        setContext([])
        if (selectedBot === "EPROEXCELLA_BOT") {
            setToUpload(true)
            setUploadStatus(false)
            setPreview(true);
        }
        try {
            if (selectedUser && conversationID) {
                await clearChatSession(conversationID);
                setConversationID(nanoid());
            }
        } catch (e) {
            console.log(`Failed to clear chat session in server: ${e}`);
        }
    };

    const onUserSelectionChange = (_ev?: React.FormEvent<HTMLElement | HTMLInputElement>, option?: IChoiceGroupOption) => {
        if (option) {
            const profile = users.find(user => user.user_id == option.key);
            if (profile) {
                updateLastQuestion(undefined);
                answerError && setAnswerError(undefined);
                setActiveCitation('active');
                setActiveAnalysisPanelTab(undefined);
                setDialog([]);
                setConversationID(nanoid());
                setSelectedUser(profile);
                setPreview(true)
            }
        }
    };

    const onRetrieveCountChange = (_ev?: React.SyntheticEvent<HTMLElement, Event>, newValue?: string) => {
        setRetrieveCount(parseInt(newValue || "20"));
    };

    const onShowExplanation = (_ev?: React.FormEvent<HTMLElement | HTMLInputElement>, checked?: boolean) => {
        setShowExplanation(!!checked);
    };

    const onShowSQL = (_ev?: React.FormEvent<HTMLElement | HTMLInputElement>, checked?: boolean) => {
        setShowSQL(!!checked);
    };

    const onUseSuggestFollowupQuestionsChange = (_ev?: React.FormEvent<HTMLElement | HTMLInputElement>, checked?: boolean) => {
        setUseSuggestFollowupQuestions(!!checked);
    };

    const onExampleClicked = (example: string) => {
        updateLastQuestion(example);
    };

    const onShowCitation = (citation: string, index: number) => {
        if (activeCitation === citation && activeAnalysisPanelTab === AnalysisPanelTabs.CitationTab && selectedAnswer === index) {
            setActiveAnalysisPanelTab(undefined);
            setActiveCitation('active');
        } else {
            setActiveCitation(citation);
            setActiveAnalysisPanelTab(AnalysisPanelTabs.CitationTab);
        }

        setSelectedAnswer(index);
    };

    const onToggleTab = (tab: AnalysisPanelTabs, index: number) => {
        if (activeAnalysisPanelTab === tab && selectedAnswer === index) {
            setActiveAnalysisPanelTab(undefined);
            setActiveCitation('active');
        } else {
            setActiveAnalysisPanelTab(tab);

            if (selectedAnswer !== index) {
                setActiveCitation('active');
            }
        }

        setSelectedAnswer(index);
    };

    const onFeedbackClicked = async (dialogID: string, feedback: FeedbackType) => {
        setIsChatStopped(false);

        try {
            const request: FeedbackRequest = {
                userID: selectedUser ? selectedUser.user_id : "",
                conversationID: conversationID ? conversationID : "",
                dialogID: dialogID,
                feedback: feedback
            };

            const result = await feedbackApi(request);

            console.log(`onFeedbackClicked result: `, result);
            if (result.statusText == "OK" ) {
                dialog.forEach((element) => {
                    if (!(element[1] instanceof ReadableStreamDefaultReader)){
                        if (element[1]?.dialog_id == dialogID) {
                            element[1].answer.feedback = feedback;
                        }
                    }
                });

                context.forEach((element) => {
                    if (element?.dialog_id == dialogID) {
                        element.answer.feedback = feedback;
                    }
                });

                setDialog(dialog)
                setContext(context)
            }

        } catch (e) {
            if (e instanceof ChatResponseError) {
                setAnswerError({ message: e.message, retryable: e.retryable });
            } else if (e instanceof Error) {
                setAnswerError({ message: e.message, retryable: true });
            }
            console.log(`Error getting answer from /chat-feedback API: ${e}`);
        } finally {
            setIsChatStopped(true);
        }
    };

    const onExcelClicked = (dialogId: string) => {

    };

    const handleUploadSuccess = () => {
        setUploadStatus(true);
    };

    const changePreviewState = (bool: boolean) => {
        setPreview(bool)
    }

    return (
        <div className={styles.container}>
            <div className={styles.topRow}>
                <div className={styles.dropdownContainer}>
                    <label className={styles.dropdownLabel}>You are chatting with</label>
                    <Dropdown
                        placeholder="Select a bot"
                        options={botList}
                        onChange={handleBotChange}
                        styles={{ dropdown: { width: 160 } }}
                    />
                </div>
                <div className={styles.commandsContainer}>
                    <TopBarButton
                        className={styles.commandButton}
                        label={"Clear chat"}
                        icon={<Delete24Regular />}
                        onClick={clearChat}
                        disabled={!lastQuestion || isAnswerLoading}
                    />
                    <TopBarButton
                        className={styles.commandButton}
                        label={"User profile"}
                        icon={<Person24Regular />}
                        onClick={() => setIsUserPanelOpen(!isUserPanelOpen)}
                    />
                    <TopBarButton
                        className={styles.commandButton}
                        label={"Settings"}
                        icon={<Settings24Regular />}
                        onClick={() => setIsConfigPanelOpen(!isConfigPanelOpen)}
                    />
                </div>
            </div>

            <div className={styles.chatRoot}>
                <div className={styles.chatContainer}>
                    {!lastQuestion ? (
                        <div className={styles.chatEmptyState}>
                            <SparkleFilled fontSize={"120px"} primaryFill={"rgba(115, 118, 225, 1)"} aria-hidden="true" aria-label="Chat logo" />
                            <h1 className={styles.chatEmptyStateTitle}>Chat with your data</h1>
                            {uploadStatus && (
                                <h2 className={styles.chatEmptyStateSubtitle}>Ask anything or try an example</h2>
                            )}
                            {toUpload && (
                                <UploadFile onUploadSuccess={handleUploadSuccess}/>
                            )}
                            <ExampleList onExampleClicked={onExampleClicked} examples={selectedUser?.sample_questions ? selectedUser.sample_questions : []} />
                        </div>
                    ) : (
                        <div className={styles.chatMessageStream}>
                            {dialog.map((answer, index) => (
                                <div key={index}>
                                    {/* Passo anche le immagini al componente UserChatMessage */}
                                    <UserChatMessage message={answer[0]} images={answer[2]} />
                                    <div className={styles.chatMessageGpt}>
                                        {<Answer
                                            key={index}
                                            chatResponse={answer[1]}
                                            isSelected={selectedAnswer === index && activeAnalysisPanelTab !== undefined}
                                            onCitationClicked={c => onShowCitation(c, index)}
                                            onThoughtProcessClicked={() => onToggleTab(AnalysisPanelTabs.ThoughtProcessTab, index)}
                                            onSupportingContentClicked={() => onToggleTab(AnalysisPanelTabs.SupportingContentTab, index)}
                                            onFollowupQuestionClicked={q => updateLastQuestion(q)}
                                            showFollowupQuestions={useSuggestFollowupQuestions && dialog.length - 1 === index}
                                            onRetryClicked={() => {
                                                if (!(answer[1] instanceof ReadableStreamDefaultReader)) {
                                                    if (answer[1].suggested_classification) {
                                                        retryWithOverride(answer[1].suggested_classification as ApproachType);
                                                    }
                                                } else {
                                                    if (context[selectedAnswer].suggested_classification){
                                                        retryWithOverride(context[selectedAnswer].suggested_classification as ApproachType);
                                                    }
                                                }
                                            }}
                                            onChangeAgent={() => {
                                                if (!(answer[1] instanceof ReadableStreamDefaultReader)){
                                                    changeAgent(answer[1].answer.agent == Agents.ragDocument ? Agents.rag : answer[1].answer.agent== Agents.rag? Agents.textToSql : Agents.rag)
                                                }else{
                                                    changeAgent(context[selectedAnswer].answer.agent == Agents.ragDocument ? Agents.rag : context[selectedAnswer].answer.agent== Agents.rag? Agents.textToSql : Agents.rag)
                                                }
                                            }}
                                            callAgent= {(choice, document_types) => {changeAgent(choice, document_types)}}
                                            retryable={!(answer[1] instanceof ReadableStreamDefaultReader) ? index == dialog.length - 1 && !!answer[1].show_retry && !isAnswerLoading && !answerError: false}
                                            onFeedbackClicked={(dialogId, feedback) => onFeedbackClicked(dialogId, feedback)}
                                            onExcelClicked={() => answer[1] instanceof ReadableStreamDefaultReader ? onExcelClicked(context[selectedAnswer].dialog_id): onExcelClicked(answer[1].dialog_id)}
                                            conversationId={conversationID ? conversationID : ""}
                                            dialogId={!(answer[1] instanceof ReadableStreamDefaultReader) ? answer[1].dialog_id: ""}
                                            selectedBot = {selectedBot}
                                            onRetryWithoutPreview = {() => {
                                                if (!(answer[1] instanceof ReadableStreamDefaultReader)) {
                                                    if (answer[1].suggested_classification) {
                                                        retryWithoutPreview(answer[1].suggested_classification as ApproachType);
                                                    }
                                                } else {
                                                    if (context[selectedAnswer].suggested_classification) {
                                                        retryWithoutPreview(context[selectedAnswer].suggested_classification as ApproachType);
                                                    }
                                                }
                                            }}
                                            preview = {preview}
                                            setPreview= {p => changePreviewState(p)}
                                            onContext = {handleContextData}
                                        />}
                                    </div>
                                </div>

                        ))}
                        {isAnswerLoading && (
                            <>
                                <UserChatMessage message={lastQuestion.question} images={lastQuestion.images} />
                                <div className={styles.chatMessageGptMinWidth}>
                                <AnswerLoading
                                    onStopChat={handleStopChat}
                                    isChatStopped={isChatStopped}
                                />
                                </div>
                            </>
                        )}
                        {answerError ? (
                            <>
                                <UserChatMessage message={lastQuestion.question} images={lastQuestion.images} />
                                <div className={styles.chatMessageGptMinWidth}>
                                    <ErrorToast
                                        message={answerError.message}
                                        retryable={answerError.retryable}
                                        onRetry={() => updateLastQuestion(lastQuestion.question, undefined, undefined, undefined, lastQuestion.images)}
                                    />
                                </div>
                            </>
                        ) : null}
                        <div ref={chatMessageStreamEnd} />
                    </div>
                )}
                    {uploadStatus && <div className={styles.chatInput}>
                        <QuestionInput
                            clearOnSend
                            placeholder={selectedBot == null ? "Select a bot" : "Type a new question"}
                            disabled={isAnswerLoading || selectedBot == null}
                            typingDisabled={selectedBot == null}
                            onSend={(question, images) => updateLastQuestion(question, undefined, undefined, undefined, images)}
                            selectedBot={selectedBot}
                        />
                    </div>}
                </div>

                {dialog.length > 0 && activeAnalysisPanelTab && (
                    <AnalysisPanel
                        className={styles.chatAnalysisPanel}
                        activeCitation={activeCitation}
                        onActiveTabChanged={x => onToggleTab(x, selectedAnswer)}
                        citationHeight="810px"
                        chatResponse={context[selectedAnswer]}
                        activeTab={activeAnalysisPanelTab}
                    />
                )}

                <Panel
                    headerText="Select user profile"
                    isOpen={isUserPanelOpen}
                    isBlocking={false}
                    onDismiss={() => setIsUserPanelOpen(false)}
                    closeButtonAriaLabel="Close"
                    onRenderFooterContent={() => <DefaultButton onClick={() => setIsUserPanelOpen(false)}>Close</DefaultButton>}
                    isFooterAtBottom={true}
                >
                    <ChoiceGroup
                        className={styles.chatSettingsSeparator}
                        label="User Profile"
                        options={users.map(user => ({ key: user.user_id, text: user.user_name }))}
                        onChange={onUserSelectionChange}
                        defaultSelectedKey={selectedUser?.user_id}
                    />

                    <Label className={styles.chatSettingsSeparator}>{selectedUser?.user_id}</Label>
                    <Label className={styles.chatSettingsSeparator}>{selectedUser?.description}</Label>
                    <a href="/auth/logout">Logout</a><br/>
                    <a href="/auth/logout?logout_type=AD">Logout</a>
                </Panel>
                <Panel
                    headerText="Configure answer generation"
                    isOpen={isConfigPanelOpen}
                    isBlocking={false}
                    onDismiss={() => setIsConfigPanelOpen(false)}
                    closeButtonAriaLabel="Close"
                    onRenderFooterContent={() => <DefaultButton onClick={() => setIsConfigPanelOpen(false)}>Close</DefaultButton>}
                    isFooterAtBottom={true}
                >
                    <SpinButton
                        className={styles.chatSettingsSeparator}
                        label="Number of rows in preview:"
                        min={1}
                        max={500}
                        defaultValue={retrieveCount.toString()}
                        onChange={onRetrieveCountChange}
                    />
                    <Checkbox
                        className={styles.chatSettingsSeparator}
                        checked={showExplanation}
                        label="Show Explanation"
                        onChange={onShowExplanation}
                    />
                    <Checkbox
                        className={styles.chatSettingsSeparator}
                        checked={showSQL}
                        label="Show SQL"
                        onChange={onShowSQL}
                    />
                </Panel>
            </div>
        </div>
    );
};

export default Chat;