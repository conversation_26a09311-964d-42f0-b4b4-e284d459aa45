import threading
import time
import json
from src.backend.models import AppConfigDB
from src import db

class ApiKeyManager:
    """
    Singleton for managing internal API keys.
    Caches keys from AppConfigDB for a configurable time (cache_minutes).
    Each key must be saved in AppConfigDB (app_config table) with key_name="api_int_key" and value as JSON:
        {
            "name": "Authorized User",
            "api_key": "abc123",
            "authorized_routes": ["route_chat", "chat", "user-profile"]
        }

    The special value "*" in authorized_routes allows access to all endpoints.
        "authorized_routes": ["*"]

    The method get_by_api_key(api_key) returns the dict associated with the key or None.
    """
    _instance = None
    _lock = threading.Lock()

    def __new__(cls, cache_minutes=60):
        if not cls._instance:
            with cls._lock:
                if not cls._instance:
                    cls._instance = super(ApiKeyManager, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance

    def __init__(self, cache_minutes=60):
        if self._initialized:
            return
        self.cache_minutes = cache_minutes
        self._cache = {}
        self._last_refresh = 0
        self._initialized = True

    def _refresh_cache(self):
        self._cache = {}
        configs = db.session.query(AppConfigDB).filter_by(key_name="api_int_key").all()
        for config in configs:
            try:
                data = json.loads(config.value)
                api_key = data.get("api_key")
                if api_key:
                    self._cache[api_key] = data
            except Exception:
                continue
        self._last_refresh = time.time()

    def get_api_key_dict(self):
        if time.time() - self._last_refresh > self.cache_minutes * 60:
            self._refresh_cache()
        return self._cache

    def get_by_api_key(self, api_key):
        api_keys = self.get_api_key_dict()
        return api_keys.get(api_key)
