import { useState } from "react";
import { Image, ImageFit, Stack, TextField } from "@fluentui/react";
import { Send28Filled } from "@fluentui/react-icons";

import styles from "./QuestionInput.module.css";

import { UploadImage } from "../LoadImage";

// Tipi per le immagini
export interface ImageItem {
    file: File;
    url: string;
}

// Struttura del contenuto della domanda (interno al componente)
export interface QuestionContent {
    text: string;
    images: ImageItem[];
}

// Tipi esistenti dal backend
export type ApproachType = any; // Sostituisci con il tipo reale
export type Agents = any; // Sostituisci con il tipo reale

export type UserQuestion = {
    question: string;
    classificationOverride?: ApproachType;
    agent_to_call?: Agents;
    document_types?: Array<string>;
    images?: File[]; // Aggiungiamo le immagini al tipo esistente
};

interface QuestionInputConfig {
    allowImages: boolean;
    maxTextLength: number;
    maxImages: number;
}

interface Props {
    onSend: (question: string, images?: File[]) => void;
    disabled: boolean;
    typingDisabled: boolean;
    placeholder?: string;
    clearOnSend?: boolean;
    selectedBot: string | number | null;
    config?: Partial<QuestionInputConfig>;
}

// Configurazione di default
const DEFAULT_CONFIG: QuestionInputConfig = {
    allowImages: false,
    maxTextLength: 1000,
    maxImages: 10
};

// Configurazioni specifiche per bot
const BOT_CONFIGS: Record<string, Partial<QuestionInputConfig>> = {
    'CREATOR_BOT': {
        allowImages: true,
        maxImages: 5
    },
    // Altri bot possono essere aggiunti facilmente
    'TEXT_ONLY_BOT': {
        allowImages: false
    }
};

export const QuestionInput = ({ 
    onSend, 
    disabled, 
    typingDisabled, 
    placeholder, 
    clearOnSend, 
    selectedBot,
    config: customConfig
}: Props) => {
    // Determina la configurazione da usare
    const botConfig = selectedBot ? BOT_CONFIGS[selectedBot.toString()] : {};
    const finalConfig: QuestionInputConfig = {
        ...DEFAULT_CONFIG,
        ...botConfig,
        ...customConfig
    };

    // Stato interno per il contenuto della domanda
    const [questionContent, setQuestionContent] = useState<QuestionContent>({
        text: "",
        images: []
    });

    const sendQuestion = () => {
        if (disabled || !questionContent.text.trim()) {
            return;
        }

        // Invia il testo e le immagini separatamente per mantenere compatibilità
        const images = questionContent.images.length > 0 ? questionContent.images.map(img => img.file) : undefined;
        onSend(questionContent.text, images);

        if (clearOnSend) {
            // Cleanup delle URL degli oggetti blob
            questionContent.images.forEach(img => URL.revokeObjectURL(img.url));
            setQuestionContent({
                text: "",
                images: []
            });
        }
    };

    const onEnterPress = (ev: React.KeyboardEvent<Element>) => {
        if (ev.key === "Enter" && !ev.shiftKey) {
            ev.preventDefault();
            sendQuestion();
        }
    };

    const onQuestionChange = (_ev: React.FormEvent<HTMLInputElement | HTMLTextAreaElement>, newValue?: string) => {
        const textValue = newValue || "";
        if (textValue.length <= finalConfig.maxTextLength) {
            setQuestionContent(prev => ({
                ...prev,
                text: textValue
            }));
        }
    };

    const handleImagesSelected = (files: File[]) => {
        // Controlla il limite massimo di immagini
        const remainingSlots = finalConfig.maxImages - questionContent.images.length;
        const filesToAdd = files.slice(0, remainingSlots);

        const newImageItems: ImageItem[] = filesToAdd.map(file => ({
            file,
            url: URL.createObjectURL(file)
        }));
        
        setQuestionContent(prev => ({
            ...prev,
            images: [...prev.images, ...newImageItems]
        }));
    };

    const removeImage = (index: number) => {
        setQuestionContent(prev => {
            const updatedImages = [...prev.images];
            URL.revokeObjectURL(updatedImages[index].url);
            updatedImages.splice(index, 1);
            
            return {
                ...prev,
                images: updatedImages
            };
        });
    };

    const sendQuestionDisabled = disabled || !questionContent.text.trim();
    const canAddMoreImages = questionContent.images.length < finalConfig.maxImages;

    return <Stack horizontal className={styles.questionInputContainer}>
            <TextField
                className={styles.questionInputTextArea}
                placeholder={placeholder}
                multiline
                disabled={typingDisabled}
                resizable={false}
                borderless
                value={questionContent.text}
                onChange={onQuestionChange}
                onKeyDown={onEnterPress}
            />

            {/* Image preview column */}
            {questionContent.images.length > 0 && (
                <div className={styles.previewContainer}>
                    {questionContent.images.map((imageItem, idx) => (
                        <div key={idx} className={styles.previewImageWrapper}>
                            <Image
                                src={imageItem.url}
                                alt={`img-${idx}`}
                                width={48}
                                height={48}
                                imageFit={ImageFit.cover}
                                className={styles.previewImage}
                            />
                            <span
                                className={styles.previewRemove}
                                onClick={() => removeImage(idx)}
                                title="Rimuovi immagine"
                            >
                                ×
                            </span>
                        </div>
                    ))}
                </div>
            )}
            
            <div className={styles.questionInputButtonsContainer}>
                {/* Mostra il pulsante di upload solo se le immagini sono abilitate */}
                {finalConfig.allowImages && canAddMoreImages && (
                    <UploadImage onImagesSelected={handleImagesSelected} />
                )}
                
                <div
                    className={`${styles.questionInputSendButton} ${sendQuestionDisabled ? styles.questionInputSendButtonDisabled : ""}`}
                    aria-label="Ask question button"
                    onClick={sendQuestion}
                >
                    <Send28Filled primaryFill="rgba(115, 118, 225, 1)" />
                </div>
            </div>
        </Stack>
};

// Hook helper per semplificare l'uso del componente
export const useQuestionInput = () => {
    const createUserQuestion = (
        text: string, 
        images: File[] = [], 
        options: {
            classificationOverride?: ApproachType;
            agentToCall?: Agents;
            documentTypes?: Array<string>;
        } = {}
    ): UserQuestion => ({
        question: text,
        ...(options.classificationOverride && { classificationOverride: options.classificationOverride }),
        ...(options.agentToCall && { agent_to_call: options.agentToCall }),
        ...(options.documentTypes && { document_types: options.documentTypes }),
        ...(images.length > 0 && { images })
    });

    return { createUserQuestion };
};