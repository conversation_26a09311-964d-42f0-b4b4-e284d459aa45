import logging
import httpx
import requests
from azure.search.documents.indexes.models import (
    HnswAlgorithmConfiguration,
    HnswParameters,
    SearchableField,
    SearchField,
    SearchFieldDataType,
    SemanticConfiguration,
    SemanticField,
    SemanticPrioritizedFields,
    SimpleField,
    ComplexField,
    VectorSearch,
    VectorSearchAlgorithmKind,
    VectorSearchAlgorithmMetric,
    VectorSearchProfile,
)
from azure.core.pipeline.transport import RequestsTransport
from azure.core.credentials import AzureKeyCredential
from azure.search.documents import SearchClient
from azure.search.documents.indexes import SearchIndexClient
from langchain_community.vectorstores.azuresearch import AzureSearch
from langchain_openai import AzureOpenAIEmbeddings

from config.config import RAGConfig
from src.backend.contracts.chat_data import BotType


def get_general_rag_config(rag_config: RAGConfig):

    __vector_profile = [
    VectorSearchProfile(
        name=rag_config.call_center_bot_azure_ai_search_vector_profile,
        algorithm_configuration_name=rag_config.call_center_bot_azure_ai_search_vector_algorithm,
    )
    ]

    __vector_algorithm = [
        HnswAlgorithmConfiguration(
            name=rag_config.call_center_bot_azure_ai_search_vector_algorithm,
            kind=VectorSearchAlgorithmKind.HNSW,
            parameters=HnswParameters(
                m=4,
                ef_construction=400,
                ef_search=500,
                metric=VectorSearchAlgorithmMetric.COSINE,
            ),
        ),
    ]

    __vector_search = VectorSearch(algorithms=__vector_algorithm, profiles=__vector_profile)

    logging.getLogger().info("✔ __vector_search DONE")

    __embedding_function = AzureOpenAIEmbeddings(
        azure_endpoint=rag_config.rag_embedder_endpoint,
        api_key=rag_config.rag_embedder_key,
        api_version=rag_config.rag_embedder_version,
        model=rag_config.rag_embedder_deployment,
        retry_min_seconds=30,
        retry_max_seconds=70,
        http_client = httpx.Client(proxy=None, trust_env=False)
    )

    logging.getLogger().info("✔ __embedding_function DONE")

    return __vector_search, __embedding_function


def get_rag_bot_configuration(rag_config: RAGConfig, bot_name: str, vector_search: VectorSearch, embedding_function: AzureOpenAIEmbeddings):

    field_id_name = rag_config.azure_ai_search_field_id_name
    field_content_name = rag_config.azure_ai_search_field_content_name
    field_vector_name = rag_config.azure_ai_search_field_vector_name
    field_metadata_name = rag_config.azure_ai_search_field_metadata_name

    if bot_name == BotType.CALL_CENTER_BOT.name:

        storage_endpoint = rag_config.call_center_bot_azure_ai_search_endpoint
        index_name = rag_config.call_center_bot_azure_ai_search_index_name
        storage_key = rag_config.call_center_bot_azure_ai_search_key

        __index_fields = [
            SimpleField(
                name=field_id_name,
                type=SearchFieldDataType.String,
                key=True,
                filterable=True,
            ),
            SearchableField(
                name=field_content_name,
                type=SearchFieldDataType.String,
                search_analyzer_name="standard.lucene",
                index_analyzer_name="keyword",
            ),
            SearchField(
                name=field_vector_name,
                type=SearchFieldDataType.Collection(SearchFieldDataType.Single),
                searchable=True,
                vector_search_dimensions=1536,
                vector_search_profile_name=rag_config.call_center_bot_azure_ai_search_vector_profile,
            ),
            SearchableField(
                name=field_metadata_name,
                type=SearchFieldDataType.String,
            ),
            SimpleField(
                name="internal_code",
                type=SearchFieldDataType.Collection(SearchFieldDataType.String),
                filterable=True,
            ),
            SimpleField(
                name="product_number",
                type=SearchFieldDataType.Collection(SearchFieldDataType.String),
                filterable=True,
            ),
            SimpleField(
                name="factory_model",
                type=SearchFieldDataType.Collection(SearchFieldDataType.String),
                filterable=True,
            ),
            SimpleField(
                name="commercial_model",
                type=SearchFieldDataType.Collection(SearchFieldDataType.String),
                filterable=True,
                nullable= True
            ),
            SimpleField(
                name="document_type_description",
                type=SearchFieldDataType.String,
                filterable=True,
                nullable= True
            ),
            SimpleField(
                name="document_type",
                type=SearchFieldDataType.String,
                filterable=True,
                nullable= True
            ),
            SimpleField(
                name="document_number",
                type=SearchFieldDataType.Collection(SearchFieldDataType.String),
                filterable=True,
                nullable= True
            ),
            SimpleField(
                name="language_description",
                type=SearchFieldDataType.String,
                filterable=True,
                nullable= True
            ),
            SimpleField(
                 name="document_edition",
                type=SearchFieldDataType.String,
                filterable=True,
                nullable= True
            ),
            SimpleField(
                name="document_brand",
                type=SearchFieldDataType.String,
                filterable=True,
                nullable= True
            ),
        ]

    elif bot_name == BotType.APPLICATION_HOWTO_BOT.name:

        storage_endpoint = rag_config.how_to_bot_azure_ai_search_endpoint
        index_name = rag_config.how_to_bot_azure_ai_search_index_name
        storage_key = rag_config.how_to_bot_azure_ai_search_key

        __index_fields = [
            SimpleField(
                name=field_id_name,
                type=SearchFieldDataType.String,
                key=True,
                filterable=True,
            ),
            SearchableField(
                name=field_content_name,
                type=SearchFieldDataType.String,
                search_analyzer_name="standard.lucene",
                index_analyzer_name="keyword",
            ),
            SearchField(
                name=field_vector_name,
                type=SearchFieldDataType.Collection(SearchFieldDataType.Single),
                searchable=True,
                vector_search_dimensions=1536,
                vector_search_profile_name=rag_config.how_to_bot_azure_ai_search_vector_profile,
            ),
            SearchableField(
                name=field_metadata_name,
                type=SearchFieldDataType.String,
            ),
        ]

    elif bot_name == BotType.JDAILO.name:

        storage_endpoint = rag_config.jdanallo_azure_ai_search_endpoint
        index_name = rag_config.jdanallo_azure_ai_search_index_name
        storage_key = rag_config.jdanallo_azure_ai_search_key

        __index_fields = [
            SimpleField(
                name=field_id_name,
                type=SearchFieldDataType.String,
                key=True,
                filterable=True,
            ),
            SearchableField(
                name=field_content_name,
                type=SearchFieldDataType.String,
                search_analyzer_name="standard.lucene",
                index_analyzer_name="keyword",
            ),
            SearchField(
                name=field_vector_name,
                type=SearchFieldDataType.Collection(SearchFieldDataType.Single),
                searchable=True,
                vector_search_dimensions=1536,
                vector_search_profile_name=rag_config.jdanallo_azure_ai_search_vector_profile,
            ),
            SearchableField(
                name=field_metadata_name,
                type=SearchFieldDataType.String,
            ),
            SearchableField(
                name="file_name",
                type=SearchFieldDataType.String,
                filterable=True,
                nullable= True,
                search_analyzer_name="standard.lucene",
                index_analyzer_name="keyword",
            ),
            SearchableField(
                name="text_summary",
                type=SearchFieldDataType.String,
                filterable=True,
                nullable= True,
                search_analyzer_name="standard.lucene",
                index_analyzer_name="keyword",
            ),
            SearchField(
                name="text_embeddings",
                type=SearchFieldDataType.Collection(SearchFieldDataType.Single),
                searchable=True,
                vector_search_dimensions=1536,
                vector_search_profile_name=rag_config.jdanallo_azure_ai_search_vector_profile,
            ),
            ComplexField(
                name="images",
                fields=[
                    SearchableField(
                        name="summary",
                        type=SearchFieldDataType.String,
                        search_analyzer_name="standard.lucene",
                        index_analyzer_name="keyword"
                    ),
                    SimpleField(name="image_name", type=SearchFieldDataType.String)
                ],
                collection=True
            ),
            ComplexField(
                name="tables",
                fields=[
                    SearchableField(
                        name="summary",
                        type=SearchFieldDataType.String,
                        search_analyzer_name="standard.lucene",
                        index_analyzer_name="keyword"
                    ),
                    SimpleField(name="table_name", type=SearchFieldDataType.String)
                ],
                collection=True
            )
        ]

    elif bot_name == BotType.SEO_BOT.name:

        storage_endpoint = rag_config.seobot_azure_ai_search_endpoint
        index_name = rag_config.seobot_azure_ai_search_index_name
        storage_key = rag_config.seobot_azure_ai_search_key

        __index_fields = [
            SimpleField(
                name=field_id_name,
                type=SearchFieldDataType.String,
                key=True,
                filterable=True,
            ),
            SearchableField(
                name=field_content_name,
                type=SearchFieldDataType.String,
                search_analyzer_name="standard.lucene",
                index_analyzer_name="keyword",
            ),
            SearchField(
                name=field_vector_name,
                type=SearchFieldDataType.Collection(SearchFieldDataType.Single),
                searchable=True,
                vector_search_dimensions=1536,
                vector_search_profile_name=rag_config.seobot_azure_ai_search_vector_profile,
            ),
            SearchableField(
                name=field_metadata_name,
                type=SearchFieldDataType.String,
            ),
            SearchableField(
                name="file_name",
                type=SearchFieldDataType.String,
                filterable=True,
                nullable= True,
                search_analyzer_name="standard.lucene",
                index_analyzer_name="keyword",
            ),
            SearchableField(
                name="text_summary",
                type=SearchFieldDataType.String,
                filterable=True,
                nullable= True,
                search_analyzer_name="standard.lucene",
                index_analyzer_name="keyword",
            ),
            SearchField(
                name="text_embeddings",
                type=SearchFieldDataType.Collection(SearchFieldDataType.Single),
                searchable=True,
                vector_search_dimensions=1536,
                vector_search_profile_name=rag_config.seobot_azure_ai_search_vector_profile,
            ),
            ComplexField(
                name="images",
                fields=[
                    SearchableField(
                        name="summary",
                        type=SearchFieldDataType.String,
                        search_analyzer_name="standard.lucene",
                        index_analyzer_name="keyword"
                    ),
                    SimpleField(name="image_name", type=SearchFieldDataType.String)
                ],
                collection=True
            ),
            ComplexField(
                name="tables",
                fields=[
                    SearchableField(
                        name="summary",
                        type=SearchFieldDataType.String,
                        search_analyzer_name="standard.lucene",
                        index_analyzer_name="keyword"
                    ),
                    SimpleField(name="table_name", type=SearchFieldDataType.String)
                ],
                collection=True
            )
        ]

    if bot_name == BotType.JDAILO.name:
        semantic_reranker = rag_config.jdanallo_azure_ai_search_semantic_reranker
    elif bot_name == BotType.SEO_BOT.name:
        semantic_reranker = rag_config.seobot_azure_ai_search_semantic_reranker
    elif bot_name == BotType.APPLICATION_HOWTO_BOT:
        semantic_reranker = rag_config.how_to_bot_azure_ai_search_semantic_reranker
    else:
        semantic_reranker = rag_config.call_center_bot_azure_ai_search_semantic_reranker

    __semantic_configuration = [
        SemanticConfiguration(
            name=semantic_reranker,
            prioritized_fields=SemanticPrioritizedFields(
                keywords_fields=[
                    SemanticField(field_name=field_metadata_name)
                ],
                content_fields=[
                    SemanticField(field_name=field_content_name)
                ],
            ),
        )
    ]

    session = requests.Session()
    session.trust_env = False
    session.proxies = {}        
    
    transport = RequestsTransport(session=session)


    storage = AzureSearch(
        azure_search_endpoint=storage_endpoint,
        azure_search_key=storage_key,
        index_name=index_name,
        embedding_function=embedding_function,
        semantic_configuration_name=semantic_reranker,
        semantic_configurations=__semantic_configuration,
        fields=__index_fields,
        vector_search= vector_search,
        additional_search_client_options= {"transport": transport},
    )

    logging.getLogger().info("✔ storage DONE")

    return storage


def get_rag_storage(bot_name: str) -> AzureSearch:

    rag_config = RAGConfig()
    vector_search, embedding_function = get_general_rag_config(rag_config)
    storage = get_rag_bot_configuration(rag_config, bot_name, vector_search, embedding_function)

    return storage
