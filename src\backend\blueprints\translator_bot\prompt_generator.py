import json
import os
import re
from typing import Dict, List, Optional, <PERSON><PERSON>
from flask import current_app
import pandas as pd
from docx import Document
from pptx import Presentation
from PyPDF2 import PdfReader
import zipfile
from lxml import etree

# Removed Docling integration - using enhanced traditional analysis instead


class PromptGenerator:
    """
    Generates contextual prompts for file translation based on file content analysis.
    Includes special handling for EES (Employee Engagement Survey) reports.
    """

    def __init__(self):
        self.config_path = os.path.join(
            os.path.dirname(__file__),
            'config',
            'ees_report_config.json'
        )
        self.ees_translations_dir = os.path.join(
            os.path.dirname(__file__),
            'ees'
        )
        self.config = self._load_config()

    def _load_config(self) -> Dict:
        """Load the EES report configuration."""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            current_app.logger.error(f"Failed to load EES config: {e}")
            return {}

    def _load_ees_translations(self, target_language: str) -> Optional[Dict]:
        """Load EES translation references for the target language."""
        try:
            current_app.logger.debug(f"Loading EES translations for: {target_language}")

            # Map language codes and names to file names
            language_mapping = {
                # Language codes (from frontend)
                'zh': 'Chinese',
                'en': 'English',
                'fr': 'French',
                'de': 'German',
                'it': 'Italian',
                'ja': 'Japanese',
                'es': 'Spanish',
                'sv': 'Swedish',
                'th': 'Thai',
                'tr': 'Turkish',
            }

            lang_key = target_language.lower()
            current_app.logger.debug(f"Language key: {lang_key}")

            if lang_key in language_mapping:
                filename = f"translations_{language_mapping[lang_key]}.json"
                filepath = os.path.join(self.ees_translations_dir, filename)
                current_app.logger.debug(f"Looking for file: {filepath}")

                if os.path.exists(filepath):
                    current_app.logger.debug(f"File exists, loading translations")
                    with open(filepath, 'r', encoding='utf-8') as f:
                        translations = json.load(f)
                        current_app.logger.debug(f"Successfully loaded {len(translations)} translations")
                        return translations
                else:
                    current_app.logger.warning(f"Translation file not found: {filepath}")
            else:
                current_app.logger.warning(f"Language '{lang_key}' not in mapping: {list(language_mapping.keys())}")

            return None
        except Exception as e:
            current_app.logger.error(f"Failed to load EES translations for {target_language}: {e}")
            return None

    def generate_prompt(self, file_path: str, target_languages: List[str] = None, 
                       original_filename: str = None) -> str:
        """
        Generate a contextual prompt based on file analysis.
        
        Args:
            file_path: Path to the uploaded file
            target_languages: List of target languages for translation
            original_filename: Original filename for additional context
            
        Returns:
            Generated prompt string
        """
        try:
            # Analyze file content
            file_analysis = self._analyze_file(file_path, original_filename)
            
            # Check if it's an EES report
            is_ees_report = self._is_ees_report(file_analysis)
            
            # Generate base prompt
            base_prompt = self._generate_base_prompt(file_analysis, is_ees_report, target_languages)
            
            # Add EES-specific content if applicable
            if is_ees_report and target_languages:
                current_app.logger.debug(f"Generating EES content for languages: {target_languages}")
                ees_content = self._generate_ees_content(target_languages)
                current_app.logger.debug(f"EES content generated: {len(ees_content) if ees_content else 0} characters")
                if ees_content:
                    base_prompt += "\n\n" + ees_content
                    current_app.logger.debug("EES content added to base prompt")
                else:
                    current_app.logger.warning("EES content is empty despite being EES report")
            
            return base_prompt
            
        except Exception as e:
            current_app.logger.error(f"Error generating prompt: {e}")
            return "Please translate this document accurately while maintaining the original meaning and context."

    def _analyze_file(self, file_path: str, original_filename: str = None) -> Dict:
        """Analyze file content to extract key information."""
        analysis = {
            'file_type': self._get_file_type(file_path),
            'filename': original_filename or os.path.basename(file_path),
            'content_sample': '',
            'keywords_found': [],
            'estimated_content_type': 'general',
            'columns': []
        }

        current_app.logger.debug(f"Analyzing file: {analysis['filename']} (type: {analysis['file_type']})")

        try:
            # Use enhanced traditional analysis - fast and reliable
            current_app.logger.info("Using enhanced document analysis")
            if analysis['file_type'] == 'excel':
                analysis.update(self._analyze_excel_enhanced(file_path))
            elif analysis['file_type'] == 'word':
                analysis.update(self._analyze_word_enhanced(file_path))
            elif analysis['file_type'] == 'powerpoint':
                analysis.update(self._analyze_powerpoint_enhanced(file_path))
            elif analysis['file_type'] == 'pdf':
                analysis.update(self._analyze_pdf_enhanced(file_path))
            elif analysis['file_type'] == 'text':
                analysis.update(self._analyze_text_enhanced(file_path))
            elif analysis['file_type'] == 'csv':
                analysis.update(self._analyze_csv_enhanced(file_path))

        except Exception as e:
            current_app.logger.error(f"Error analyzing file {file_path}: {e}")

        # Log analysis results for debugging
        current_app.logger.info(f"=== FILE ANALYSIS RESULTS ===")
        current_app.logger.info(f"File: {analysis['filename']}")
        current_app.logger.info(f"Type: {analysis['file_type']}")
        current_app.logger.info(f"Content sample length: {len(analysis.get('content_sample', ''))}")
        current_app.logger.info(f"Content sample preview: {analysis.get('content_sample', '')[:300]}...")
        current_app.logger.info(f"Columns found: {analysis.get('columns', [])}")
        current_app.logger.info(f"Keywords found: {analysis.get('keywords_found', [])}")
        current_app.logger.info(f"=== END ANALYSIS ===")

        # Also log to console for immediate visibility
        print(f"DEBUG: File analysis for {analysis['filename']}")
        print(f"DEBUG: Content length: {len(analysis.get('content_sample', ''))}")
        print(f"DEBUG: Keywords: {analysis.get('keywords_found', [])}")
        print(f"DEBUG: Content preview: {analysis.get('content_sample', '')[:200]}...")

        return analysis

    def _get_file_type(self, file_path: str) -> str:
        """Determine file type from extension."""
        ext = os.path.splitext(file_path)[1].lower()
        if ext in ['.xlsx', '.xls']:
            return 'excel'
        elif ext in ['.docx', '.doc']:
            return 'word'
        elif ext in ['.pptx', '.ppt']:
            return 'powerpoint'
        elif ext == '.pdf':
            return 'pdf'
        elif ext in ['.txt', '.rtf']:
            return 'text'
        elif ext in ['.csv']:
            return 'csv'
        return 'unknown'

    def _analyze_excel(self, file_path: str) -> Dict:
        """Analyze Excel file content."""
        try:
            df = pd.read_excel(file_path, nrows=50)  # Sample first 50 rows
            
            # Get column names and sample data
            columns = df.columns.tolist()
            sample_data = []
            
            for col in columns[:5]:  # First 5 columns
                sample_values = df[col].dropna().astype(str).head(3).tolist()
                sample_data.extend(sample_values)
            
            content_sample = ' '.join(sample_data)[:2000]  # Increased to 2000 chars

            # Extract keywords for classification
            full_content = ' '.join(columns) + ' ' + content_sample
            keywords_found = self._extract_keywords(full_content)

            current_app.logger.debug(f"Excel analysis:")
            current_app.logger.debug(f"  Columns: {columns}")
            current_app.logger.debug(f"  Rows: {len(df)}")
            current_app.logger.debug(f"  Keywords found: {keywords_found}")

            return {
                'content_sample': content_sample,
                'columns': columns,
                'row_count': len(df),
                'keywords_found': keywords_found
            }
        except Exception as e:
            current_app.logger.error(f"Error analyzing Excel file: {e}")
            return {'content_sample': '', 'columns': [], 'row_count': 0, 'keywords_found': []}

    def _analyze_word(self, file_path: str) -> Dict:
        """Analyze Word document content with enhanced detail extraction."""
        try:
            doc = Document(file_path)
            paragraphs = [p.text for p in doc.paragraphs if p.text.strip()]

            # Extract much more content for better analysis
            content_sample = ' '.join(paragraphs[:20])[:2000]  # First 20 paragraphs, 2000 chars

            # Analyze document structure
            headings = []
            tables_count = 0

            for paragraph in doc.paragraphs:
                if paragraph.style.name.startswith('Heading'):
                    headings.append(paragraph.text.strip())

            # Count tables
            tables_count = len(doc.tables)

            # Extract table content if present
            table_content = []
            for table in doc.tables[:3]:  # First 3 tables
                for row in table.rows[:5]:  # First 5 rows
                    row_text = [cell.text.strip() for cell in row.cells]
                    table_content.extend(row_text)

            # Combine all content for analysis
            full_content = content_sample + ' ' + ' '.join(table_content) + ' ' + ' '.join(headings)

            # Extract keywords for classification
            keywords_found = self._extract_keywords(full_content)

            current_app.logger.debug(f"Word document analysis:")
            current_app.logger.debug(f"  Paragraphs: {len(paragraphs)}")
            current_app.logger.debug(f"  Headings: {len(headings)}")
            current_app.logger.debug(f"  Tables: {tables_count}")
            current_app.logger.debug(f"  Keywords found: {keywords_found}")

            return {
                'content_sample': full_content[:2000],  # Limit to 2000 chars
                'paragraph_count': len(paragraphs),
                'headings': headings[:10],  # First 10 headings
                'tables_count': tables_count,
                'keywords_found': keywords_found,
                'has_tables': tables_count > 0,
                'has_headings': len(headings) > 0
            }
        except Exception as e:
            current_app.logger.error(f"Error analyzing Word document: {e}")
            return {'content_sample': '', 'paragraph_count': 0, 'keywords_found': []}

    def _analyze_powerpoint(self, file_path: str) -> Dict:
        """Analyze PowerPoint presentation content."""
        try:
            prs = Presentation(file_path)
            text_content = []
            slide_count = 0

            # Iterate through slides more safely
            for slide in prs.slides:
                slide_count += 1
                try:
                    for shape in slide.shapes:
                        if hasattr(shape, "text") and shape.text.strip():
                            text_content.append(shape.text.strip())
                except Exception as shape_error:
                    current_app.logger.debug(f"Error processing slide {slide_count}: {shape_error}")
                    continue

                # Limit to first 10 slides for performance
                if slide_count >= 10:
                    break

            content_sample = ' '.join(text_content)[:2000]  # First 2000 chars for better detection

            # Extract keywords for classification
            keywords_found = self._extract_keywords(content_sample)

            current_app.logger.debug(f"PowerPoint analysis:")
            current_app.logger.debug(f"  Slides: {slide_count}")
            current_app.logger.debug(f"  Keywords found: {keywords_found}")

            return {
                'content_sample': content_sample,
                'slide_count': slide_count,
                'keywords_found': keywords_found
            }
        except Exception as e:
            current_app.logger.error(f"Error analyzing PowerPoint: {e}")
            return {'content_sample': '', 'slide_count': 0, 'keywords_found': []}

    def _analyze_pdf(self, file_path: str) -> Dict:
        """Analyze PDF content."""
        try:
            reader = PdfReader(file_path)
            text_content = []
            
            # Extract text from first 3 pages
            for page_num in range(min(3, len(reader.pages))):
                page = reader.pages[page_num]
                text_content.append(page.extract_text())
            
            content_sample = ' '.join(text_content)[:2000]  # Increased to 2000 chars

            # Extract keywords for classification
            keywords_found = self._extract_keywords(content_sample)

            current_app.logger.debug(f"PDF analysis:")
            current_app.logger.debug(f"  Pages: {len(reader.pages)}")
            current_app.logger.debug(f"  Keywords found: {keywords_found}")

            return {
                'content_sample': content_sample,
                'page_count': len(reader.pages),
                'keywords_found': keywords_found
            }
        except Exception as e:
            current_app.logger.error(f"Error analyzing PDF: {e}")
            return {'content_sample': '', 'page_count': 0, 'keywords_found': []}

    def _analyze_text(self, file_path: str) -> Dict:
        """Analyze plain text file content."""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            # Take first 2000 characters for analysis
            content_sample = content[:2000]

            # Extract keywords for classification
            keywords_found = self._extract_keywords(content_sample)

            current_app.logger.debug(f"Text file analysis:")
            current_app.logger.debug(f"  Content length: {len(content)}")
            current_app.logger.debug(f"  Keywords found: {keywords_found}")

            return {
                'content_sample': content_sample,
                'character_count': len(content),
                'keywords_found': keywords_found
            }
        except Exception as e:
            current_app.logger.error(f"Error analyzing text file: {e}")
            return {'content_sample': '', 'character_count': 0, 'keywords_found': []}

    def _analyze_csv(self, file_path: str) -> Dict:
        """Analyze CSV file content."""
        try:
            import pandas as pd
            df = pd.read_csv(file_path, nrows=20)  # Read first 20 rows
            columns = df.columns.tolist()

            # Get sample content from columns and first few rows
            content_parts = []
            content_parts.extend(columns)  # Add column headers

            # Add sample data from first few rows
            for _, row in df.head(10).iterrows():
                content_parts.extend([str(val) for val in row.values if pd.notna(val)])

            content_sample = ' '.join(content_parts)[:2000]

            # Extract keywords for classification
            full_content = ' '.join(columns) + ' ' + content_sample
            keywords_found = self._extract_keywords(full_content)

            current_app.logger.debug(f"CSV analysis:")
            current_app.logger.debug(f"  Columns: {columns}")
            current_app.logger.debug(f"  Rows: {len(df)}")
            current_app.logger.debug(f"  Keywords found: {keywords_found}")

            return {
                'content_sample': content_sample,
                'columns': columns,
                'row_count': len(df),
                'keywords_found': keywords_found
            }
        except Exception as e:
            current_app.logger.error(f"Error analyzing CSV file: {e}")
            return {'content_sample': '', 'columns': [], 'row_count': 0, 'keywords_found': []}

    def _is_ees_report(self, file_analysis: Dict) -> bool:
        """Determine if the file is an EES (Employee Engagement Survey) report."""
        if not self.config.get('ees_report_detection'):
            return False

        detection_config = self.config['ees_report_detection']
        strong_indicators_list = detection_config.get('strong_indicators', [])
        weak_indicators_list = detection_config.get('weak_indicators', [])

        filename = file_analysis.get('filename', '').lower()
        content = file_analysis.get('content_sample', '').lower()
        columns = file_analysis.get('columns', [])

        strong_score = 0
        weak_score = 0
        strong_matches = []
        weak_matches = []

        # Check filename patterns (strong indicator if matches specific EES patterns)
        ees_filename_patterns = ['*ees*', '*employee*engagement*survey*']
        for pattern in ees_filename_patterns:
            pattern_regex = pattern.replace('*', '.*')
            if re.search(pattern_regex, filename):
                strong_score += 2
                strong_matches.append(f"filename pattern: {pattern}")
                break

        # Check for strong indicators in content
        for indicator in strong_indicators_list:
            if indicator.lower() in content:
                strong_score += 2
                strong_matches.append(f"strong keyword: {indicator}")

        # Check for weak indicators in content
        for indicator in weak_indicators_list:
            if indicator.lower() in content:
                weak_score += 1
                weak_matches.append(f"weak keyword: {indicator}")

        # Check for EES-specific column patterns in Excel files
        if columns:
            column_text = ' '.join(str(col).lower() for col in columns)

            # Look for numbered questions pattern (like "1", "2a", "3b" etc.)
            numbered_pattern = re.findall(r'\b\d+[a-z]?\b', column_text)
            if len(numbered_pattern) >= 5:  # Multiple numbered questions
                strong_score += 2
                strong_matches.append(f"numbered questions: {len(numbered_pattern)} found")

            # Check for specific EES column content
            ees_column_indicators = [
                'how satisfied are you',
                'recommend the company',
                'proud working for',
                'engagement level',
                'electrolux professional'
            ]

            for indicator in ees_column_indicators:
                if indicator in column_text:
                    strong_score += 1
                    strong_matches.append(f"column indicator: {indicator}")

        total_score = strong_score + weak_score

        # Detection logic:
        # - If we have strong content indicators: require score >= 2 and total >= 4
        # - If we only have filename match but no content (due to parsing issues): allow filename match alone
        has_content = len(content.strip()) > 50  # Check if we actually extracted content

        if has_content:
            # Normal detection: require strong evidence
            is_ees = strong_score >= 2 and total_score >= 4
        else:
            # Fallback for parsing issues: allow strong filename patterns alone
            is_ees = strong_score >= 2

        # Debug logging
        current_app.logger.debug(f"EES Detection Analysis:")
        current_app.logger.debug(f"  Filename: {filename}")
        current_app.logger.debug(f"  Content length: {len(content)} chars")
        current_app.logger.debug(f"  Has content: {has_content}")
        current_app.logger.debug(f"  Strong matches: {strong_matches}")
        current_app.logger.debug(f"  Weak matches: {weak_matches}")
        current_app.logger.debug(f"  Strong score: {strong_score}")
        current_app.logger.debug(f"  Weak score: {weak_score}")
        current_app.logger.debug(f"  Total score: {total_score}")
        current_app.logger.debug(f"  Is EES: {is_ees}")

        return is_ees

    def _generate_base_prompt(self, file_analysis: Dict, is_ees_report: bool, target_languages: List[str] = None) -> str:
        """Generate the base prompt based on file analysis."""
        if is_ees_report:
            return self._generate_ees_prompt(file_analysis)

        # Generate content-specific prompts for different file types and content
        return self._generate_content_specific_prompt(file_analysis, target_languages)

    def _generate_content_specific_prompt(self, file_analysis: Dict, target_languages: List[str] = None) -> str:
        """Generate precise, content-aware prompts based on file analysis."""

        current_app.logger.info(f"=== PROMPT GENERATION ===")

        # Check if this was analyzed with enhanced methods
        if file_analysis.get('analysis_method') == 'enhanced_traditional':
            current_app.logger.info("Using enhanced structure-based prompt generation")
            print(f"DEBUG: Using enhanced structure-based prompt generation")
            return self._generate_enhanced_prompt(file_analysis, target_languages)

        # Fallback to traditional classification-based approach
        current_app.logger.info("Using traditional classification-based prompt generation")
        print(f"DEBUG: Using traditional prompt generation")

        document_type = self._classify_document_type(file_analysis)

        current_app.logger.info(f"Document classified as: {document_type}")
        print(f"DEBUG: Document classified as: {document_type}")

        # Generate LLM-based prompt using document analysis
        prompt = self._generate_llm_based_prompt(document_type, file_analysis, target_languages)
        current_app.logger.info(f"Generated LLM-based prompt: {prompt[:100]}...")
        print(f"DEBUG: Generated LLM-based prompt for {document_type}")
        return prompt

    def _generate_ees_content(self, target_languages: List[str]) -> str:
        """Generate EES-specific content including translation references."""
        ees_content_parts = []

        current_app.logger.debug(f"_generate_ees_content called with languages: {target_languages}")

        # Add translation references for each target language
        for language in target_languages:
            current_app.logger.debug(f"Processing language: {language}")
            translations = self._load_ees_translations(language)
            current_app.logger.debug(f"Loaded {len(translations) if translations else 0} translations for {language}")

            if translations:
                # Get the proper language name for display
                display_name = self._get_language_display_name(language)
                ees_content_parts.append(f"\nFor {display_name} translation:")
                ees_content_parts.append("Use these as a translation reference:")

                # Add ALL translation references from the JSON file
                for english_text, translated_text in translations.items():
                    ees_content_parts.append(f"'{english_text}' : '{translated_text}'")

                current_app.logger.debug(f"Added {len(translations)} translation pairs for {language}")
            else:
                current_app.logger.warning(f"No translations found for language: {language}")

        result = '\n'.join(ees_content_parts)
        current_app.logger.debug(f"Final EES content length: {len(result)} characters")
        return result

    def _get_language_display_name(self, language_code: str) -> str:
        """Convert language code to display name."""
        code_to_name = {
            'zh': 'Chinese',
            'en': 'English',
            'fr': 'French',
            'de': 'German',
            'it': 'Italian',
            'ja': 'Japanese',
            'es': 'Spanish',
            'sv': 'Swedish',
            'th': 'Thai',
            'tr': 'Turkish'
        }

        # If it's already a full name, return it capitalized
        if language_code.lower() in ['chinese', 'english', 'french', 'german', 'italian',
                                   'japanese', 'spanish', 'swedish', 'thai', 'turkish']:
            return language_code.title()

        # Otherwise, convert code to name
        return code_to_name.get(language_code.lower(), language_code.title())

    def _classify_document_type(self, file_analysis: Dict) -> str:
        """Classify document type based on comprehensive content analysis."""
        content = file_analysis.get('content_sample', '').lower()
        filename = file_analysis.get('filename', '').lower()
        columns = file_analysis.get('columns', [])
        keywords_found = file_analysis.get('keywords_found', [])

        current_app.logger.info(f"=== DOCUMENT CLASSIFICATION ===")
        current_app.logger.info(f"Classifying document with {len(keywords_found)} keywords found")
        current_app.logger.info(f"Keywords: {keywords_found}")

        # Count keywords by category
        category_scores = {}
        for keyword_entry in keywords_found:
            if ':' in keyword_entry:
                category = keyword_entry.split(':')[0]
                category_scores[category] = category_scores.get(category, 0) + 1

        current_app.logger.info(f"Category scores: {category_scores}")

        # Also log to console for immediate visibility
        print(f"DEBUG: Classification - Keywords found: {keywords_found}")
        print(f"DEBUG: Classification - Category scores: {category_scores}")

        # Find the category with the highest score
        if category_scores:
            best_category = max(category_scores.items(), key=lambda x: x[1])
            current_app.logger.info(f"Best category: {best_category[0]} with score {best_category[1]}")
            print(f"DEBUG: Best category: {best_category[0]} with score {best_category[1]}")

            if best_category[1] >= 2:  # At least 2 keywords in the category
                current_app.logger.info(f"Document classified as '{best_category[0]}' with score {best_category[1]}")
                print(f"DEBUG: Final classification: {best_category[0]}")
                return best_category[0]
            else:
                current_app.logger.info(f"Score {best_category[1]} too low, falling back to keyword matching")
                print(f"DEBUG: Score too low, falling back")
        else:
            current_app.logger.info("No category scores found, falling back to keyword matching")
            print("DEBUG: No category scores, falling back")

        # Fallback to original keyword-based classification
        # Financial documents
        financial_keywords = ['budget', 'revenue', 'profit', 'cost', 'expense', 'financial',
                            'accounting', 'invoice', 'payment', 'balance', 'cash flow',
                            'quarterly', 'annual report', 'p&l', 'income statement']
        financial_columns = ['amount', 'price', 'cost', 'revenue', 'budget', 'total', 'sum']

        if (any(keyword in content or keyword in filename for keyword in financial_keywords) or
            any(col.lower() in financial_columns for col in columns)):
            return 'financial'

        # Technical documents
        technical_keywords = ['api', 'software', 'system', 'technical', 'specification',
                            'architecture', 'database', 'server', 'configuration', 'code',
                            'implementation', 'development', 'programming', 'algorithm']
        if any(keyword in content or keyword in filename for keyword in technical_keywords):
            return 'technical'

        # Legal documents
        legal_keywords = ['contract', 'agreement', 'legal', 'terms', 'conditions', 'clause',
                         'liability', 'compliance', 'regulation', 'law', 'jurisdiction',
                         'whereas', 'hereby', 'party', 'obligations']
        if any(keyword in content or keyword in filename for keyword in legal_keywords):
            return 'legal'

        # Marketing documents
        marketing_keywords = ['marketing', 'campaign', 'brand', 'customer', 'market',
                            'promotion', 'advertising', 'social media', 'engagement',
                            'conversion', 'roi', 'target audience', 'demographics']
        if any(keyword in content or keyword in filename for keyword in marketing_keywords):
            return 'marketing'

        # Training documents
        training_keywords = ['training', 'course', 'learning', 'education', 'tutorial',
                           'workshop', 'certification', 'skills', 'competency', 'module',
                           'lesson', 'curriculum', 'assessment', 'quiz']
        if any(keyword in content or keyword in filename for keyword in training_keywords):
            return 'training'

        # Policy documents
        policy_keywords = ['policy', 'procedure', 'guideline', 'standard', 'protocol',
                          'governance', 'compliance', 'best practice', 'framework',
                          'methodology', 'process', 'workflow']
        if any(keyword in content or keyword in filename for keyword in policy_keywords):
            return 'policy'

        # Report documents
        report_keywords = ['report', 'analysis', 'findings', 'results', 'summary',
                          'conclusion', 'recommendation', 'data', 'statistics',
                          'metrics', 'kpi', 'performance', 'dashboard']
        if any(keyword in content or keyword in filename for keyword in report_keywords):
            return 'report'

        # Manual documents
        manual_keywords = ['manual', 'guide', 'handbook', 'instructions', 'how to',
                          'step by step', 'user guide', 'reference', 'documentation',
                          'help', 'support', 'troubleshooting']
        if any(keyword in content or keyword in filename for keyword in manual_keywords):
            return 'manual'

        # Presentation documents (based on file type and content)
        if file_analysis.get('file_type') == 'powerpoint':
            return 'presentation'

        # Default classification
        return 'general'

    def _generate_llm_based_prompt(self, document_type: str, file_analysis: Dict, target_languages: List[str] = None) -> str:
        """
        Generate intelligent translation prompts using OpenAI LLM based on document analysis.

        This sends the document analysis to an LLM to generate contextual, specific prompts.
        """
        try:
            from flask import current_app

            # Get the translator LLM from the app
            translator_llm = current_app.translator_llm

            # Prepare document analysis for LLM
            analysis_summary = self._prepare_document_analysis_summary(file_analysis, document_type, target_languages)

            # Create the system prompt for generating translation prompts
            system_prompt = """You are an expert translation consultant. Your task is to analyze document information and generate specific, actionable translation prompts that will guide translators to produce high-quality translations.

Based on the document analysis provided, create a concise but comprehensive translation prompt that:
1. Identifies the document type and purpose
2. Provides specific guidance for the document's content and structure
. Considers the target language(s) and cultural adaptation needs
5. Gives concrete instructions for maintaining document integrity

Keep the prompt focused, professional, and under 200 words. Return only the translation prompt, no explanations or meta-commentary."""

            # Generate the prompt using LLM
            response = translator_llm.invoke([
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": analysis_summary}
            ])

            generated_prompt = response.content.strip()

            current_app.logger.info(f"Generated LLM-based prompt: {generated_prompt[:100]}...")
            print(f"DEBUG: Generated LLM-based prompt: {generated_prompt[:150]}...")

            return generated_prompt

        except Exception as e:
            current_app.logger.error(f"Error generating LLM-based prompt: {e}")
            print(f"DEBUG: LLM prompt generation failed: {e}")

            # Fallback to simple template-based prompt
            return self._generate_fallback_prompt(document_type, file_analysis, target_languages)

    def _prepare_document_analysis_summary(self, file_analysis: Dict, document_type: str, target_languages: List[str] = None) -> str:
        """Prepare a structured summary of document analysis for the LLM."""

        summary_parts = []

        # Document basic info
        summary_parts.append(f"DOCUMENT ANALYSIS:")
        summary_parts.append(f"- File type: {file_analysis.get('file_type', 'unknown')}")
        summary_parts.append(f"- Filename: {file_analysis.get('filename', 'unknown')}")
        summary_parts.append(f"- Content length: {file_analysis.get('content_sample_length', len(file_analysis.get('content_sample', '')))} characters")
        summary_parts.append(f"- Classified as: {document_type}")

        # Document structure (if available from enhanced analysis)
        if file_analysis.get('analysis_method') == 'enhanced_traditional':
            summary_parts.append(f"\nDOCUMENT STRUCTURE:")
            summary_parts.append(f"- Paragraphs: {file_analysis.get('paragraph_count', 0)}")
            summary_parts.append(f"- Headings: {file_analysis.get('heading_count', 0)}")
            summary_parts.append(f"- Tables: {file_analysis.get('table_count', 0)}")

            # Document patterns
            patterns = file_analysis.get('document_patterns', {})
            if patterns:
                pattern_list = [k for k, v in patterns.items() if v and k != 'document_structure']
                if pattern_list:
                    summary_parts.append(f"- Patterns detected: {', '.join(pattern_list)}")

        # Keywords found
        keywords = file_analysis.get('keywords_found', [])
        if keywords:
            # Group keywords by category
            keyword_categories = {}
            for keyword in keywords[:10]:  # Limit to first 10
                if ':' in keyword:
                    category, term = keyword.split(':', 1)
                    if category not in keyword_categories:
                        keyword_categories[category] = []
                    keyword_categories[category].append(term)

            if keyword_categories:
                summary_parts.append(f"\nKEYWORDS DETECTED:")
                for category, terms in keyword_categories.items():
                    summary_parts.append(f"- {category.title()}: {', '.join(terms[:3])}")  # Max 3 per category

        # Content preview
        content_sample = file_analysis.get('content_sample', '')
        if content_sample:
            preview = content_sample[:300] + "..." if len(content_sample) > 300 else content_sample
            summary_parts.append(f"\nCONTENT PREVIEW:")
            summary_parts.append(f"{preview}")

        # Target languages
        if target_languages:
            lang_names = [self._get_language_display_name(lang) for lang in target_languages]
            summary_parts.append(f"\nTARGET LANGUAGES: {', '.join(lang_names)}")

        summary_parts.append(f"\nPlease generate a specific translation prompt for this {document_type} document.")

        return '\n'.join(summary_parts)

    def _generate_fallback_prompt(self, document_type: str, file_analysis: Dict, target_languages: List[str] = None) -> str:
        """Generate a simple fallback prompt when LLM generation fails."""

        prompt_parts = []

        # Basic document type prompt
        if document_type == 'manual':
            prompt_parts.append("This is an instruction manual or technical guide.")
        elif document_type == 'technical':
            prompt_parts.append("This is a technical document.")
        elif document_type == 'financial':
            prompt_parts.append("This is a financial document.")
        elif document_type == 'legal':
            prompt_parts.append("This is a legal or compliance document.")
        elif document_type == 'marketing':
            prompt_parts.append("This is a marketing or promotional document.")
        else:
            prompt_parts.append(f"This is a {document_type} document.")

        # Add structure information if available
        if file_analysis.get('table_count', 0) > 0:
            prompt_parts.append(f" It contains {file_analysis['table_count']} table(s).")

        if file_analysis.get('heading_count', 0) > 0:
            prompt_parts.append(f" It has {file_analysis['heading_count']} section heading(s).")

        # Basic guidance
        prompt_parts.append(" Please preserve the document structure, maintain terminology consistency, and ensure accurate translation of all content.")

        # Target language adaptation
        if target_languages:
            lang_names = [self._get_language_display_name(lang) for lang in target_languages]
            prompt_parts.append(f" Adapt content appropriately for {', '.join(lang_names)} audience(s).")

        return ''.join(prompt_parts)

    def _get_language_display_name(self, lang_code: str) -> str:
        """Convert language code to display name."""
        language_names = {
            'en': 'English',
            'es': 'Spanish',
            'fr': 'French',
            'de': 'German',
            'it': 'Italian',
            'pt': 'Portuguese',
            'nl': 'Dutch',
            'pl': 'Polish',
            'ru': 'Russian',
            'zh': 'Chinese',
            'ja': 'Japanese',
            'ko': 'Korean',
            'ar': 'Arabic'
        }
        return language_names.get(lang_code.lower(), lang_code.upper())

    def _generate_ees_prompt(self, file_analysis: Dict) -> str:
        """Generate EES-specific prompt."""
        templates = self.config.get('prompt_templates', {})

        if file_analysis['file_type'] == 'excel':
            return templates.get('survey_questions', templates.get('general_ees', ''))
        elif 'engagement' in file_analysis.get('content_sample', '').lower():
            return templates.get('engagement_focus', templates.get('general_ees', ''))
        elif 'culture' in file_analysis.get('content_sample', '').lower():
            return templates.get('organizational_culture', templates.get('general_ees', ''))
        else:
            return templates.get('general_ees', '')



    def _extract_keywords(self, content: str) -> List[str]:
        """Extract relevant keywords from content for classification with multi-language support."""
        content_lower = content.lower()

        current_app.logger.info(f"=== KEYWORD EXTRACTION ===")
        current_app.logger.info(f"Content to analyze: {content_lower[:200]}...")
        print(f"DEBUG: Extracting keywords from: {content_lower[:100]}...")

        # Define comprehensive keyword categories with multi-language support
        all_keywords = {
            'technical': [
                # English
                'api', 'software', 'system', 'technical', 'specification',
                'architecture', 'database', 'server', 'configuration', 'code',
                'implementation', 'development', 'programming', 'algorithm',
                'framework', 'library', 'deployment', 'integration', 'machine',
                'equipment', 'device', 'technology', 'engineering', 'performance',
                'efficiency', 'operation', 'maintenance', 'installation',
                # Italian
                'sistema', 'tecnico', 'tecnologia', 'macchina', 'apparecchio',
                'dispositivo', 'prestazioni', 'efficienza', 'operazione',
                'manutenzione', 'installazione', 'configurazione', 'sviluppo',
                'asciugatrice', 'pompa', 'calore', 'ergonomia', 'gas',
                # French
                'système', 'technique', 'technologie', 'machine', 'appareil',
                'dispositif', 'performances', 'efficacité', 'opération',
                'maintenance', 'installation', 'séchoir'
            ],
            'manual': [
                # English
                'manual', 'guide', 'handbook', 'instructions', 'how to',
                'step by step', 'user guide', 'reference', 'documentation',
                'help', 'support', 'troubleshooting', 'faq', 'care', 'precautions',
                'operation', 'use', 'usage', 'follow', 'guidelines',
                # Italian
                'manuale', 'guida', 'istruzioni', 'come', 'passo', 'riferimento',
                'documentazione', 'aiuto', 'supporto', 'cura', 'precauzioni',
                'operazioni', 'uso', 'utilizzo', 'seguire', 'seguendo',
                'pianificazione', 'prenditene', 'tienilo', 'presente',
                # French
                'manuel', 'guide', 'instructions', 'comment', 'étape', 'référence',
                'documentation', 'aide', 'support', 'soin', 'précautions',
                'opérations', 'utilisation', 'suivre', 'planification'
            ],
            'financial': [
                # English
                'budget', 'revenue', 'profit', 'cost', 'expense', 'financial',
                'accounting', 'invoice', 'payment', 'balance', 'cash flow',
                'quarterly', 'annual report', 'p&l', 'income statement', 'roi',
                'investment', 'capital', 'assets', 'liabilities', 'equity',
                # Italian
                'budget', 'ricavi', 'profitto', 'costo', 'spesa', 'finanziario',
                'contabilità', 'fattura', 'pagamento', 'bilancio', 'flusso',
                # French
                'budget', 'revenus', 'profit', 'coût', 'dépense', 'financier',
                'comptabilité', 'facture', 'paiement', 'bilan', 'flux'
            ],
            'marketing': [
                # English
                'marketing', 'campaign', 'brand', 'customer', 'market',
                'promotion', 'advertising', 'social media', 'engagement',
                'conversion', 'roi', 'target audience', 'demographics', 'seo',
                # Italian
                'marketing', 'campagna', 'marca', 'cliente', 'mercato',
                'promozione', 'pubblicità', 'social media', 'coinvolgimento',
                # French
                'marketing', 'campagne', 'marque', 'client', 'marché',
                'promotion', 'publicité', 'médias sociaux', 'engagement'
            ],
            'report': [
                # English
                'report', 'analysis', 'findings', 'results', 'summary',
                'conclusion', 'recommendation', 'data', 'statistics',
                'metrics', 'kpi', 'performance', 'dashboard', 'insights',
                # Italian
                'rapporto', 'analisi', 'risultati', 'riassunto', 'conclusione',
                'raccomandazione', 'dati', 'statistiche', 'metriche', 'prestazioni',
                # French
                'rapport', 'analyse', 'résultats', 'résumé', 'conclusion',
                'recommandation', 'données', 'statistiques', 'métriques', 'performances'
            ]
        }

        found_keywords = []
        for category, keywords in all_keywords.items():
            category_matches = 0
            for keyword in keywords:
                if keyword in content_lower:
                    found_keywords.append(f"{category}:{keyword}")
                    category_matches += 1
            if category_matches > 0:
                current_app.logger.info(f"Found {category_matches} {category} keywords")
                print(f"DEBUG: Found {category_matches} {category} keywords")

        current_app.logger.info(f"Total keywords found: {found_keywords}")
        print(f"DEBUG: Total keywords found: {found_keywords}")

        return found_keywords[:20]  # Limit to top 20 keywords



    def _analyze_word_enhanced(self, file_path: str) -> Dict:
        """
        Enhanced Word document analysis - fast and comprehensive.

        Extracts document structure, content patterns, and formatting details
        for intelligent prompt generation.
        """
        try:
            from docx import Document
            doc = Document(file_path)

            # Fast content extraction
            full_text = []
            headings = []
            tables_info = []

            # Analyze paragraphs efficiently
            for paragraph in doc.paragraphs:
                text = paragraph.text.strip()
                if text:
                    full_text.append(text)

                    # Detect headings by style
                    if paragraph.style.name.startswith('Heading'):
                        headings.append({
                            'text': text,
                            'level': paragraph.style.name
                        })

            # Analyze tables with content sampling
            for i, table in enumerate(doc.tables):
                if i < 5:  # Limit analysis to first 5 tables for speed
                    headers = []
                    sample_data = []

                    # Extract table headers (first row)
                    if table.rows:
                        headers = [cell.text.strip() for cell in table.rows[0].cells]

                    # Sample a few data rows
                    for row in table.rows[1:3]:  # Max 2 data rows
                        sample_data.append([cell.text.strip() for cell in row.cells])

                    tables_info.append({
                        'index': i,
                        'headers': headers,
                        'sample_data': sample_data,
                        'rows': len(table.rows),
                        'cols': len(table.columns) if table.rows else 0
                    })

            # Combine text for analysis
            content_text = ' '.join(full_text)
            content_sample = content_text[:2000]  # First 2000 chars

            # Extract keywords from content
            keywords_found = self._extract_keywords(content_text)

            # Detect document patterns
            document_patterns = self._detect_document_patterns(content_text, headings, tables_info)

            analysis = {
                'content_sample': content_sample,
                'full_text_length': len(content_text),
                'paragraph_count': len([p for p in doc.paragraphs if p.text.strip()]),
                'heading_count': len(headings),
                'table_count': len(tables_info),
                'keywords_found': keywords_found,
                'headings': headings[:5],  # First 5 headings
                'tables_info': tables_info,
                'document_patterns': document_patterns,
                'analysis_method': 'enhanced_traditional'
            }

            current_app.logger.info(f"Enhanced Word analysis complete:")
            current_app.logger.info(f"  - Paragraphs: {analysis['paragraph_count']}")
            current_app.logger.info(f"  - Headings: {analysis['heading_count']}")
            current_app.logger.info(f"  - Tables: {analysis['table_count']}")
            current_app.logger.info(f"  - Keywords: {len(keywords_found)}")
            current_app.logger.info(f"  - Patterns: {document_patterns}")

            return analysis

        except Exception as e:
            current_app.logger.error(f"Enhanced Word analysis failed: {e}")
            # Fallback to original method
            return self._analyze_word(file_path)

    def _detect_document_patterns(self, content: str, headings: List[Dict], tables_info: List[Dict]) -> Dict:
        """
        Fast pattern detection for document classification.

        Analyzes content structure and patterns to determine document characteristics.
        """
        patterns = {
            'has_numbered_sections': False,
            'has_bullet_points': False,
            'has_financial_data': False,
            'has_technical_specs': False,
            'has_procedures': False,
            'has_legal_language': False,
            'document_structure': 'simple'
        }

        content_lower = content.lower()

        # Quick pattern checks
        patterns['has_numbered_sections'] = bool(
            len([h for h in headings if any(char.isdigit() for char in h['text'][:10])]) > 2
        )

        patterns['has_bullet_points'] = bool(
            content.count('•') > 3 or content.count('*') > 5 or content.count('-') > 10
        )

        # Financial patterns
        financial_indicators = ['€', '$', '£', 'budget', 'cost', 'price', 'revenue', 'profit']
        patterns['has_financial_data'] = any(indicator in content_lower for indicator in financial_indicators)

        # Technical patterns
        technical_indicators = ['specification', 'parameter', 'configuration', 'system', 'technical']
        patterns['has_technical_specs'] = any(indicator in content_lower for indicator in technical_indicators)

        # Procedure patterns
        procedure_indicators = ['step', 'procedure', 'instruction', 'follow', 'complete', 'process']
        patterns['has_procedures'] = any(indicator in content_lower for indicator in procedure_indicators)

        # Legal patterns
        legal_indicators = ['shall', 'hereby', 'whereas', 'compliance', 'regulation', 'policy']
        patterns['has_legal_language'] = any(indicator in content_lower for indicator in legal_indicators)

        # Document structure analysis
        if len(headings) > 5 and len(tables_info) > 2:
            patterns['document_structure'] = 'complex'
        elif len(headings) > 2 or len(tables_info) > 0:
            patterns['document_structure'] = 'structured'

        return patterns

    def _generate_enhanced_prompt(self, file_analysis: Dict, target_languages: List[str] = None) -> str:
        """
        Generate intelligent prompts based on enhanced document analysis.

        Uses document structure, patterns, and content to create specific guidance.
        """
        try:
            patterns = file_analysis.get('document_patterns', {})
            tables_info = file_analysis.get('tables_info', [])
            keywords = file_analysis.get('keywords_found', [])

            # Start building the prompt
            prompt_parts = []

            # Document type identification based on patterns
            doc_type = self._identify_document_type_from_patterns(patterns, keywords, file_analysis)
            prompt_parts.append(f"This is a {doc_type}")
        except Exception as e:
            current_app.logger.error(f"Error in enhanced prompt generation: {e}")
            # Fallback to traditional classification
            return self._generate_content_specific_prompt_fallback(file_analysis, target_languages)

        # Add structural information
        structural_elements = []

        if file_analysis.get('heading_count', 0) > 0:
            structural_elements.append(f"{file_analysis['heading_count']} section heading(s)")

        if file_analysis.get('table_count', 0) > 0:
            table_details = self._describe_tables(tables_info)
            structural_elements.append(f"{file_analysis['table_count']} table(s) {table_details}")

        if patterns.get('has_bullet_points'):
            structural_elements.append("bullet points and lists")

        if structural_elements:
            prompt_parts.append(f" with {', '.join(structural_elements)}.")
        else:
            prompt_parts.append(".")

        # Add content-specific guidance
        guidance = self._generate_pattern_based_guidance(patterns, keywords, tables_info)

        if guidance:
            prompt_parts.append(f" {guidance}")

        # Add target language considerations
        if target_languages:
            lang_names = [self._get_language_display_name(lang) for lang in target_languages]
            prompt_parts.append(f" Adapt the content appropriately for {', '.join(lang_names)} audience(s).")

        # Add document integrity instruction
        prompt_parts.append(" Maintain the document's structure, formatting, and professional presentation throughout the translation.")

        final_prompt = ''.join(prompt_parts)

        current_app.logger.info(f"Generated enhanced prompt: {final_prompt[:100]}...")
        print(f"DEBUG: Generated enhanced prompt: {final_prompt[:150]}...")

        return final_prompt

    def _identify_document_type_from_patterns(self, patterns: Dict, keywords: List[str], file_analysis: Dict) -> str:
        """Identify document type based on patterns and content."""

        # Check for specific document types based on patterns
        if patterns.get('has_financial_data') and file_analysis.get('table_count', 0) > 0:
            return "financial report or budget document"

        if patterns.get('has_technical_specs') and patterns.get('has_procedures'):
            return "technical manual or specification document"

        if patterns.get('has_procedures') and patterns.get('has_numbered_sections'):
            return "procedural guide or instruction manual"

        if patterns.get('has_legal_language'):
            return "policy or compliance document"

        if patterns.get('document_structure') == 'complex':
            return "comprehensive report or detailed document"

        # Fallback to keyword-based classification
        keyword_categories = {}
        for keyword in keywords:
            if ':' in keyword:
                category = keyword.split(':')[0]
                keyword_categories[category] = keyword_categories.get(category, 0) + 1

        if keyword_categories:
            best_category = max(keyword_categories.items(), key=lambda x: x[1])[0]
            return f"{best_category} document"

        return "structured document"

    def _describe_tables(self, tables_info: List[Dict]) -> str:
        """Generate description of table content."""
        if not tables_info:
            return ""

        descriptions = []
        for table in tables_info[:2]:  # Describe first 2 tables
            if table.get('headers'):
                header_sample = ', '.join(table['headers'][:3])
                descriptions.append(f"with columns like '{header_sample}'")

        if descriptions:
            return f"({descriptions[0]})"
        return "(containing structured data)"

    def _generate_pattern_based_guidance(self, patterns: Dict, keywords: List[str], tables_info: List[Dict]) -> str:
        """Generate specific guidance based on document patterns."""
        guidance_parts = []

        if patterns.get('has_financial_data'):
            guidance_parts.append("Ensure accuracy of all numerical values, currency amounts, and financial terminology")

        if patterns.get('has_technical_specs'):
            guidance_parts.append("maintain consistency in technical terminology and specifications")

        if patterns.get('has_procedures'):
            guidance_parts.append("preserve the logical sequence of instructions and procedural steps")

        if patterns.get('has_numbered_sections'):
            guidance_parts.append("keep section numbering and hierarchical structure intact")

        if tables_info:
            guidance_parts.append("preserve table formatting and ensure data relationships remain clear")

        if patterns.get('has_bullet_points'):
            guidance_parts.append("maintain list formatting and bullet point structure")

        # Add keyword-based guidance
        technical_keywords = [k for k in keywords if k.startswith('technical:')]
        manual_keywords = [k for k in keywords if k.startswith('manual:')]

        if technical_keywords:
            guidance_parts.append("focus on technical accuracy and terminology consistency")

        if manual_keywords:
            guidance_parts.append("ensure instructional clarity and usability")

        if guidance_parts:
            return "Please " + ", ".join(guidance_parts) + "."

        return ""

    def _analyze_pdf_enhanced(self, file_path: str) -> Dict:
        """Enhanced PDF analysis - fallback to original for now."""
        analysis = self._analyze_pdf(file_path)
        analysis['analysis_method'] = 'enhanced_traditional'
        return analysis

    def _analyze_excel_enhanced(self, file_path: str) -> Dict:
        """Enhanced Excel analysis - fallback to original for now."""
        analysis = self._analyze_excel(file_path)
        analysis['analysis_method'] = 'enhanced_traditional'
        return analysis

    def _analyze_powerpoint_enhanced(self, file_path: str) -> Dict:
        """Enhanced PowerPoint analysis - fallback to original for now."""
        analysis = self._analyze_powerpoint(file_path)
        analysis['analysis_method'] = 'enhanced_traditional'
        return analysis

    def _analyze_text_enhanced(self, file_path: str) -> Dict:
        """Enhanced text analysis - fallback to original for now."""
        analysis = self._analyze_text(file_path)
        analysis['analysis_method'] = 'enhanced_traditional'
        return analysis

    def _analyze_csv_enhanced(self, file_path: str) -> Dict:
        """Enhanced CSV analysis - fallback to original for now."""
        analysis = self._analyze_csv(file_path)
        analysis['analysis_method'] = 'enhanced_traditional'
        return analysis

    def _generate_content_specific_prompt_fallback(self, file_analysis: Dict, target_languages: List[str] = None) -> str:
        """Fallback prompt generation when enhanced methods fail."""
        current_app.logger.info("Using fallback prompt generation")

        # Use traditional classification
        document_type = self._classify_document_type(file_analysis)

        # Generate basic structure-aware prompt
        prompt_parts = []

        if document_type == 'manual':
            prompt_parts.append("This is an instruction manual or technical guide")
        elif document_type == 'technical':
            prompt_parts.append("This is a technical document")
        else:
            prompt_parts.append(f"This is a {document_type} document")

        # Add structure information if available
        if file_analysis.get('table_count', 0) > 0:
            prompt_parts.append(f" with {file_analysis['table_count']} table(s)")

        if file_analysis.get('heading_count', 0) > 0:
            prompt_parts.append(f" and {file_analysis['heading_count']} section(s)")

        prompt_parts.append(". Please preserve the document structure and ensure accurate translation of technical terminology.")

        if target_languages:
            lang_names = [self._get_language_display_name(lang) for lang in target_languages]
            prompt_parts.append(f" Adapt content appropriately for {', '.join(lang_names)} audience(s).")

        return ''.join(prompt_parts)
