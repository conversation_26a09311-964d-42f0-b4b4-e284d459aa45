from openai import AzureOpenAI

from config.config import EproExcelLaConfig

class Interpreter:

    def __init__(self):
        self.cfg = EproExcelLaConfig()

        # Init client
        self.client = AzureOpenAI(
            api_key=self.cfg.translator_llm_key,
            api_version=self.cfg.translator_llm_version,
            azure_endpoint=self.cfg.translator_llm_endpoint,
        )

    def intent(self, request):
        system_prompt = """You are an assistant whose task is to understand and consider only the information necessary for the purpose. Pay attention to the input prompt language; if it isn't English, translate the request in english.
            Your purpose is to generate a JSON like this: \{ 'request': '<the_input_request_translated>', 'type':'<type>' \}
            Use the following pieces of retrieved context to generate the JSON. 
            If you don't know the answer, just return \{ 'type':'idk' \}.
            The type can be one of two values: cc or tt. You can distinguish the type as follows: If the prompt is to get a custom class, set the type to cc else If the request is for translation, set the type to tt.
            Example: translate the column B means type tt; get the custom class is type cc; give me the custom class is type cc.
            Pay attention to translate the input request into english, translate exactly the text input; the only information that you are allowed to add are the following one.
            If the word column isn't in the text add it to clarify, for example someone can ask you "Translate the article name in sweden" and he means "Translate the column article name in sweden"
            So use the input text, translate it in english without adding the information and than add the from and column information if needed.
        """
        
        messages = [{"role": "system", "content": system_prompt},
                    {"role": "user", "content": request}]
        
        response = self.client.chat.completions.create(
            model=self.cfg.translator_llm_deployment,
            messages=messages,
            temperature=0,
            top_p=0.1,
        )
        
        return str(response.choices[0].message.content).replace("\'", "\"")

    def interprets(self, request, columns, sample):
        """This function interprets the user's pormpt and returns a JSON file containing the information needed for translation.
        Args:
            request (str): The request in natural language posed by the user.
            columns (list): The list with the column ids.
            sample (DataFrame): A subset of the Excel elements used to identify the language of the file or column.
        
        Returns:
            Answer: An object containing all relevant values for the answer.

        """

        system_prompt = """
            You are an assistant whose task is to understand and consider only the information necessary for the purpose of translation.
            The most important information of this type of prompt are the column or the columns to be translated, the start language and the desired language or languages and at least the operation to do on the file, to be added as a new column or to update the existing column with the new data.
            For the language code use the codes for the representation of names of languages in this list https://learn.microsoft.com/en-us/azure/ai-services/translator/language-support#translation
            Those information must be codified as a JSON: \{ 'op': "new" or "same", 'info' : [ ['<col_name>', '<from>', ['<to_1>', '<to_2>', '<to_3>']], ['<col_name>', '<from>', ['<to_1>', '<to_2>', '<to_3>']], ..]\}
            The 'op' property is to identify the operation to do, it is "same" if in the prompt is specified that the translation must be done as an update on the same column and it is "add" if the request is to add a new column or if it isn't specified the operation 1 is set as default.
            The 'info' property is an array formed of array that contains the column to be translated and the information of the translation. If the language of output isn't specified the default one will be english so 'en'.
            Example 1: prompt = translate the column desc1 and desc2 in italian and spanish; JSON = \{'op':"new", 'info' : [ ['desc1', <from>, ['it', 'sp']], ['desc2', <from>, ['it', 'sp']]]\}
            Example 2: prompt = translate the column product and description; JSON = \{'op':"new", 'info' : [ ['product', '<from>', ['en']], ['description', '<from>', ['en']]]\}
            Example 3: prompt = translate the column desc1 in italian and desc2 in spanish; JSON = \{'op':"new", 'info' : [ ['desc1', '<from>', ['it']], ['desc2', '<from>', ['sp']]]\}
            The '<from>' value is the language of the column text, don't leave the field '<from>' as '<from>' or blank try to understand from which language the column is and it's impossible that the from_lang is into the to_lang for that column.
        """
        system_prompt += """
            The column name must be one of the names present in the list: {columns}. 
            If the request does not specify the column name but instead provides the column number or letter, use the associated name. 
            For example, if the list contains the columns: id, description, short_description, and internal_code, and the user asks to translate the third column, you should select the column named “description”. 
            Similarly, if the user refers to column “C”, you should also use “description”. 
            The mapping is as follows: First column = A = a, Second column = B = b, and so on. Ensure that in the JSON, you use the column name from the list. 
            For instance, if the user mentions “product name” and the list contains “name” or “prod_name”, you should use the corresponding name from the list in the JSON. 
            If the specified column is not in the columns list, leave the field blank. 
            If the language of the column and the output language are not specified in the prompt, proceed as follows: determine the language of the column to be translated in the Excel file for each column that needs translation. 
            Once you have identified the column to be translated from the list: {columns}, inspect the following JSON to find the language corresponding to the previously selected column and try to determine the language from the JSON {sample}. 
            When you have determined the language, change the "<from>" field of the output JSON to the corresponding language of the column. 
            In summary, the info property is an array of “col_name” which is the column name, “from” which is the language of the descriptions, and “to” which are the output languages, if not specified put "en". Your output must be a pure JSON.
            Don't put this in the output ```json or ``` or other thing that are not allowed in a JSON.
        """.format(columns = columns, sample = sample)
        
        assistant_prompt = """
            User: Translate the column B in French
            { “op”: “new”, “info”: [ [“description”, “en”, [“fr”]] ] }

            User: Translate the column description in Italian
            { “op”: “new”, “info”: [ [“description”, “en”, [“it”]] ] }

            User: Translate the column article_name in Spanish
            { “op”: “new”, “info”: [ [“article_name”, “en”, [“es”]] ] }

            User: Translate the column description and article_name in Italian and Spanish
            { “op”: “new”, “info”: [ [“description”, “en”, [“it”, “es”]], [“article_name”, “en”, [“it”, “es”]] ] }

            User: Translate the column code and item in French
            { “op”: “new”, “info”: [ [“code”, “en”, [“fr”]], [“item”, “en”, [“fr”]] ] }

            User: Translate the column description in Italian and the column article_name in Spanish
            { “op”: “new”, “info”: [ [“description”, “en”, [“it”]], [“article_name”, “en”, [“es”]] ] }

        """

        messages = [{"role": "system", "content": system_prompt},
                    {"role": "user", "content": request},
                    {"role": "assistant", "content": assistant_prompt}]

        response = self.client.chat.completions.create(
            model=self.cfg.translator_llm_deployment,
            messages=messages,
            temperature=0,
            top_p=0.1,
        )
        
        return str(response.choices[0].message.content).replace("\'", "\"")