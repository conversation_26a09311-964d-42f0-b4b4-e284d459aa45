import React, { useEffect, useRef, useState } from "react";
import { Stack, IconButton, PrimaryButton } from "@fluentui/react";
import styles from  "../Answer.module.css"
import { parseStreamAnswerToHtml } from "../AnswerParser";
import { AnswerIcon } from "../AnswerIcon";
import { ThumbLike20Regular, ThumbDislike20Regular, ThumbLike20Filled, ThumbDislike20Filled } from "@fluentui/react-icons";
import { FeedbackType, Agents, ChatResponse } from "../../../api";
import Zoom from 'react-medium-image-zoom';
import { AnswerHeaderButtons } from "./AnswerHeadersButtons";
import { AnswerActions } from "./AnswerActions";
import { ImageGallery } from "./ImageGallery";
import { DownloadButton } from "./DownloadButton";
import { AnswerBotActions } from "./AnswerBotActions";


interface Props {
    chatResponse: ChatResponse | ReadableStreamDefaultReader;
    isSelected?: boolean;
    onThoughtProcessClicked: () => void;
    onSupportingContentClicked: () => void;
    onFollowupQuestionClicked?: (question: string) => void;
    showFollowupQuestions?: boolean;
    onRetryClicked?: () => void;
    onChangeAgent?: () => void;
    onFeedbackClicked?: (dialogId: string, feedback: FeedbackType) => void;
    onExcelClicked?: (dialogId: string) => void;
    conversationId: string;
    dialogId: string;
    selectedBot: string | number | null;
    onRetryWithoutPreview?: () => void;
    preview: boolean;
    onContext: (contextData: Object | ChatResponse) => void;
    handleDownloadClick: () => void;
}


export const StreamedAnswer = ({
    chatResponse,
    isSelected,
    onThoughtProcessClicked,
    onSupportingContentClicked,
    onFeedbackClicked,
    onExcelClicked,
    conversationId,
    dialogId,
    selectedBot,
    preview,
    onChangeAgent,
    handleDownloadClick,
    onContext
}: Props) => {
    const [message, setMessage] = useState('');
    const [context, setContexData] = useState(Object);
    const responseRef = useRef('');
    let dataStream: ChatResponse;

    useEffect(() => {
        const streamData = async () => {
            const chunkData = parseStreamAnswerToHtml(chatResponse);

            let isFirstMessage = true;

            for await (const chunk of chunkData) {
                if (isFirstMessage && typeof chunk.v === 'object') {
                    dataStream = chunk.v;
                    isFirstMessage = false;
                } else {
                    responseRef.current = responseRef.current + chunk.v;
                    setMessage(responseRef.current);
                }
            }

            setContexData(dataStream);
            onContext(dataStream);
        };

        streamData();
    }, [chatResponse]);

    return (
        <Stack
            id={context.dialog_id}
            className={`${styles.answerContainer} ${isSelected && styles.selected}`}
            verticalAlign="space-between"
        >
            <Stack.Item>
                <Stack horizontal>
                    <AnswerIcon />
                    <AnswerHeaderButtons
                        chatResponse={context}
                        onSupportingContentClicked={onSupportingContentClicked}
                        onThoughtProcessClicked={onThoughtProcessClicked}
                    ></AnswerHeaderButtons>
                </Stack>
            </Stack.Item>

            <Stack.Item grow>
                <div className={styles.answerText} dangerouslySetInnerHTML={{ __html: message }}></div>
                {context?.appendix && (
                    <div className={styles.answerText} dangerouslySetInnerHTML={{ __html: context.appendix }}></div>
                )}
                <DownloadButton
                    chatResponse={context}
                ></DownloadButton>
            </Stack.Item>

             {/* Feedback and export buttons */}
            <AnswerActions
                onExcelClicked={onExcelClicked}
                selectedBot={selectedBot}
                chatResponse={context}
                conversationId={conversationId}
                dialogId={dialogId}
                onFeedbackClicked={onFeedbackClicked}
            />

            {/* Images section */}
            <ImageGallery images={context.images} selectedBot={selectedBot} />
            <AnswerBotActions
                chatResponse={context}
                selectedBot={selectedBot}
                preview={preview}
                onChangeAgent={onChangeAgent}
                handleDownloadClick={handleDownloadClick}
            ></AnswerBotActions>
        </Stack>
    );
};