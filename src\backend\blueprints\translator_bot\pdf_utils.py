import re

from PyPDF2 import <PERSON>d<PERSON><PERSON><PERSON><PERSON>, PdfWriter
import os

def extract_pdf_pages(input_pdf_path, output_pdf_path, max_pages=2):
    """
    Extract the first `max_pages` pages from a PDF and save to a new file.
    """
    reader = PdfReader(input_pdf_path)
    writer = PdfWriter()
    total_pages = len(reader.pages)
    pages_to_extract = min(max_pages, total_pages)
    if total_pages == 0:
        raise ValueError(f"PDF file '{input_pdf_path}' has no pages.")
    for i in range(pages_to_extract):
        writer.add_page(reader.pages[i])
    with open(output_pdf_path, 'wb') as f:
        writer.write(f)
    # Fallback: if original PDF is shorter than max_pages, log and use all pages
    if total_pages < max_pages:
        print(f"[extract_pdf_pages] PDF has only {total_pages} page(s), using all available pages.")
    return output_pdf_path


class EnhancedTextMerger:
    """
    Helper class for intelligent text merging in PDF-converted documents.
    Conservative merging logic to avoid creating incorrect sentence joins.
    """

    def __init__(self):
        # Precompiled patterns
        self._sentence_endings = re.compile(r"[.!?]+\s*$")
        self._hyphen_split = re.compile(r"(\w)-\s+(\w)", re.IGNORECASE)
        self._hyphen_trailing = re.compile(r"-\s*$")
        # Continuation words per language (small list, used for heuristics)
        self.continuation_words = {
            "en": ["and", "or", "but", "with", "without", "in", "on", "at", "to", "for", "of", "by", "the"],
            "it": ["e", "o", "ma", "con", "senza", "in", "su", "a", "per", "di"],
            "es": ["y", "o", "pero", "con", "sin", "en", "sobre", "a", "para", "de"],
            "fr": ["et", "ou", "mais", "avec", "sans", "dans", "sur", "à", "pour"],
            # add more as needed...
        }

        # Simple verb indicators for 'complete sentence' heuristic
        self._verb_indicators = {
            "en": ["is", "are", "was", "were", "have", "has", "had", "will", "would", "can", "could"],
            "it": ["è", "sono", "era", "erano", "ha", "hanno"],
        }

    def merge_hyphenated_words(self, text: str) -> str:
        """Merge hyphen-split words across lines."""
        if not text:
            return text
        return self._hyphen_split.sub(r"\1\2", text)

    def _looks_like_complete_sentence(self, text: str) -> bool:
        """Heuristic: ends with punctuation or contains a verb indicator and is long enough."""
        if not text:
            return False
        t = text.strip()
        if not t:
            return False
        if t[-1] in ".!?":
            return True
        if len(t.split()) < 3 or len(t) < 15:
            return False
        low = t.lower()
        # check any language indicators
        if any(v in low for v in self._verb_indicators.get("en", [])):
            return True
        return False

    def _looks_like_title_or_header(self, text: str) -> bool:
        """Avoid merging probable titles/headers."""
        if not text:
            return False
        t = text.strip()
        if len(t) > 3 and t.isupper():
            return True
        if len(t) < 80 and t.istitle() and t[-1] not in ".!?:;,":
            return True
        if re.search(r"^\d+\.?\s+[A-Z]", t):
            return True
        return False

    def should_merge_paragraphs(self, prev_text: str, curr_text: str, language: str = "en") -> bool:
        """Decide conservatively whether two fragments should be merged."""
        if not prev_text or not curr_text:
            return False
        prev = prev_text.strip()
        curr = curr_text.strip()
        # Unisci solo parole spezzate da trattino a fine riga
        return bool(self._hyphen_trailing.search(prev))