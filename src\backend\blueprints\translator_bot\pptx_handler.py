from asyncio import subprocess
import html
import json
import os
import sys
import gc
import psutil
from pathlib import Path
from typing import List, Dict, Optional
import logging
from dataclasses import dataclass

# Libraries for PowerPoint
from openai import AzureOpenAI
from pptx import Presentation
from pptx.shapes.base import BaseShape
from pptx.text.text import TextFrame
from pptx.shapes.shapetree import SlideShapes
from pptx.enum.shapes import MSO_SHAPE_TYPE

from src.backend.blueprints.translator_bot.memory_optimizer import MemoryMonitorContext
from src.backend.blueprints.translator_bot.translator import Translator
from utils.core import get_logger

import zipfile
import shutil
import tempfile
from lxml import etree

SMARTART_NS = {
    'dgm': 'http://schemas.openxmlformats.org/drawingml/2006/diagram',
    'a': 'http://schemas.openxmlformats.org/drawingml/2006/main'
}

# Logging configuration
FILE_DIR = os.path.dirname(__file__)
logger = get_logger(__file__)

class PPTXHandler:

    def __init__(self, pptx_path, file_context=None, source_language: str = 'auto'):
        self.pptx_path = pptx_path
        self.file_context = file_context
        self.source_language = source_language
        self._memory_threshold_mb = 500  # Memory threshold for cleanup
        self._batch_size_adaptive = True  # Enable adaptive batch sizing
        self._presentation_size_factor = 1.0  # Scaling factor based on presentation size
        self._performance_history = []  # Track performance for adaptive optimization
    
    def _get_memory_usage_mb(self) -> float:
        """Get current memory usage in MB"""
        try:
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except Exception:
            return 0.0

    def _force_garbage_collection(self):
        """Force garbage collection and log memory usage"""
        memory_before = self._get_memory_usage_mb()
        gc.collect()
        memory_after = self._get_memory_usage_mb()
        logger.debug(f"Memory cleanup: {memory_before:.1f}MB -> {memory_after:.1f}MB (freed {memory_before - memory_after:.1f}MB)")

    def _get_adaptive_batch_size(self, default_size: int = 50, operation_type: str = "general") -> int:
        """Calculate adaptive batch size based on available memory, system load, and presentation characteristics"""
        if not self._batch_size_adaptive:
            return default_size

        try:
            memory_usage = self._get_memory_usage_mb()
            memory_info = psutil.virtual_memory()
            available_memory = memory_info.available / 1024 / 1024
            memory_percent = memory_info.percent

            # Base size adjustment based on memory pressure
            size_factor = 1.0

            # Aggressive reduction for high memory usage
            if memory_usage > self._memory_threshold_mb * 1.5:
                size_factor *= 0.25  # Very small batches
            elif memory_usage > self._memory_threshold_mb:
                size_factor *= 0.5   # Small batches
            elif memory_percent > 85:
                size_factor *= 0.6   # Reduced batches for high system memory usage
            elif available_memory < 500:  # Less than 500MB available
                size_factor *= 0.4
            elif available_memory < 1000:  # Less than 1GB available
                size_factor *= 0.7

            # Adjust based on presentation size factor
            size_factor *= self._presentation_size_factor

            # Operation-specific adjustments
            if operation_type == "table":
                size_factor *= 0.6  # Tables are more memory intensive
            elif operation_type == "notes":
                size_factor *= 0.8  # Notes processing is lighter

            # Apply historical performance adjustments
            if self._performance_history:
                avg_performance = sum(self._performance_history) / len(self._performance_history)
                if avg_performance > 2.0:  # Slow performance, reduce batch size
                    size_factor *= 0.8
                elif avg_performance < 0.5:  # Fast performance, can increase batch size
                    size_factor *= 1.2

            # Calculate final batch size
            adaptive_size = max(5, int(default_size * size_factor))

            logger.debug(f"Adaptive batch size: {adaptive_size} (default: {default_size}, factor: {size_factor:.2f}, memory: {memory_usage:.1f}MB, available: {available_memory:.1f}MB)")

            return adaptive_size

        except Exception as e:
            logger.warning(f"Error calculating adaptive batch size: {e}")
            return default_size

    def _update_presentation_size_factor(self, total_slides: int, avg_shapes_per_slide: float):
        """Update the presentation size factor based on presentation characteristics"""
        # Large presentations need smaller batches
        if total_slides > 100:
            self._presentation_size_factor = 0.5
        elif total_slides > 50:
            self._presentation_size_factor = 0.7
        elif total_slides > 20:
            self._presentation_size_factor = 0.85
        else:
            self._presentation_size_factor = 1.0

        # Adjust for shape complexity
        if avg_shapes_per_slide > 20:
            self._presentation_size_factor *= 0.7
        elif avg_shapes_per_slide > 10:
            self._presentation_size_factor *= 0.85

        logger.debug(f"Presentation size factor: {self._presentation_size_factor:.2f} (slides: {total_slides}, avg shapes: {avg_shapes_per_slide:.1f})")
    def _record_performance(self, operation_time: float):
        """Record performance metrics for adaptive optimization"""
        self._performance_history.append(operation_time)
        # Keep only recent history
        if len(self._performance_history) > 10:
            self._performance_history.pop(0)

    def _memory_monitor_context(self, operation_name: str):
        """Context manager for monitoring memory usage during operations"""
        return MemoryMonitorContext(operation_name, self)

    def _extract_smartart_texts(self, pptx_path: str):
        """
        Estrae i testi dagli SmartArt sia da ppt/diagrams che da ppt/slides (cache).
        Restituisce un dict {filename: [testi...]}.
        """
        smartart_data = {}
        with zipfile.ZipFile(pptx_path, 'r') as zin:
            for name in zin.namelist():
                if (name.startswith('ppt/diagrams/') or name.startswith('ppt/slides/')) and name.endswith('.xml'):
                    xml = zin.read(name)
                    tree = etree.fromstring(xml)
                    texts = [t.text for t in tree.findall('.//a:t', namespaces=SMARTART_NS) if t.text]
                    if texts:
                        smartart_data[name] = texts
        return smartart_data

    def _replace_smartart_texts(self, pptx_path: str, replacements: dict, out_path: str):
        tmpdir = tempfile.mkdtemp()
        try:
            # Estrai tutto
            with zipfile.ZipFile(pptx_path, 'r') as zin:
                zin.extractall(tmpdir)

            # Aggiorna i testi
            for filename, new_texts in replacements.items():
                filepath = os.path.join(tmpdir, filename)
                if not os.path.exists(filepath):
                    continue
                tree = etree.parse(filepath)
                pts = tree.findall('.//a:t', namespaces=SMARTART_NS)
                i = 0
                for pt in pts:
                    if pt.text and pt.text.strip() and i < len(new_texts):
                        pt.text = new_texts[i]
                        i += 1
                tree.write(filepath, xml_declaration=True, encoding='UTF-8')

            # Scrivi su un file temporaneo
            tmp_pptx = pptx_path + ".tmp"
            with zipfile.ZipFile(tmp_pptx, 'w') as zout:
                for folder, _, files in os.walk(tmpdir):
                    for f in files:
                        full = os.path.join(folder, f)
                        rel = os.path.relpath(full, tmpdir)
                        zout.write(full, rel)

            # Sovrascrivi il file finale
            shutil.move(tmp_pptx, out_path)

        finally:
            shutil.rmtree(tmpdir)

    def _process_shape_recursively(self, shape, target_lang: str, depth: int = 0) -> None:
        indent = "  " * depth
        logger.debug(f"{indent}Processing shape type: {shape.shape_type}")
        
        # Handle grouped shapes
        if shape.shape_type == MSO_SHAPE_TYPE.GROUP:
            logger.info(f"{indent}Found GROUP shape with {len(shape.shapes)} child shapes")
            for child_shape in shape.shapes:
                self._process_shape_recursively(child_shape, target_lang, depth + 1)

        # Handle table shapes
        elif shape.shape_type == MSO_SHAPE_TYPE.TABLE:
            logger.info(f"{indent}Found TABLE shape, translating table content")
            self._translate_table_content(shape.table, target_lang)

        # Handle shapes with text_frame
        elif shape.has_text_frame:
            logger.debug(f"{indent}Found text shape, translating content")
            self._translate_text_frame_runs_preserve_layout(shape.text_frame, target_lang)

        # Fallback: shapes with `.text` but no `.text_frame`
        elif hasattr(shape, "text") and shape.text.strip():
            logger.debug(f"{indent}Fallback: shape has .text but no .text_frame — translating")
            try:
                translated_text = self.batch_translate([shape.text], target_lang)[0]
                shape.text = translated_text
            except Exception as e:
                logger.warning(f"{indent}Could not update shape.text: {e}")

        else:
            logger.debug(f"{indent}Shape type {shape.shape_type} - no translatable content")

    def _translate_table_content(self, table, target_lang: str) -> None:
        logger.info(f"Translating table with {len(table.rows)} rows and {len(table.columns)} columns")

        # Use streaming approach to avoid accumulating all text in memory
        self._translate_table_content_streaming(table, target_lang)

    def _translate_table_content_streaming(self, table, target_lang: str) -> None:
        """
        Stream-based table translation that processes cells in batches
        to reduce memory usage for large tables.
        """
        batch_size = self._get_adaptive_batch_size(30, "table")  # Table-specific batch sizing
        current_batch_runs = []
        current_batch_texts = []

        with self._memory_monitor_context(f"table translation ({len(table.rows)}x{len(table.columns)})") as monitor:
            for row_idx, row in enumerate(table.rows):
                for cell_idx, cell in enumerate(row.cells):
                    if cell.text and cell.text.strip():
                        for paragraph in cell.text_frame.paragraphs:
                            for run in paragraph.runs:
                                if run.text.strip():  # Only process non-empty runs
                                    current_batch_runs.append(run)
                                    current_batch_texts.append(run.text)

                                    # Process batch when it reaches the target size
                                    if len(current_batch_texts) >= batch_size:
                                        self._process_text_batch(current_batch_runs, current_batch_texts, target_lang, "table")
                                        current_batch_runs.clear()
                                        current_batch_texts.clear()
                                        monitor.update_peak()

                                        # Recalculate batch size periodically for large tables
                                        if row_idx % 10 == 0:
                                            new_batch_size = self._get_adaptive_batch_size(30, "table")
                                            if new_batch_size != batch_size:
                                                batch_size = new_batch_size
                                                logger.debug(f"Adjusted table batch size to {batch_size}")

            # Process any remaining texts in the final batch
            if current_batch_texts:
                self._process_text_batch(current_batch_runs, current_batch_texts, target_lang, "table")
                current_batch_runs.clear()
                current_batch_texts.clear()

    def _process_text_batch(self, runs: List, texts: List[str], target_lang: str, operation_type: str = "general") -> None:
        """Process a batch of text runs for translation"""
        if not texts:
            return

        try:
            translated_texts = self.batch_translate(texts, target_lang, operation_type=operation_type)
            for run, translated_text in zip(runs, translated_texts):
                run.text = translated_text
        except Exception as e:
            logger.error(f"Error processing text batch: {e}")
            # Keep original text on error

    def _translate_slide(self, slide, target_lang: str) -> None:
        with self._memory_monitor_context(f"slide translation ({len(slide.shapes)} shapes)") as monitor:
            shape_counts = {}
            for shape in slide.shapes:
                shape_type = shape.shape_type
                shape_counts[shape_type] = shape_counts.get(shape_type, 0) + 1
            if MSO_SHAPE_TYPE.GROUP in shape_counts:
                logger.info(f"Slide contains {shape_counts[MSO_SHAPE_TYPE.GROUP]} grouped element(s)")
            if MSO_SHAPE_TYPE.TABLE in shape_counts:
                logger.info(f"Slide contains {shape_counts[MSO_SHAPE_TYPE.TABLE]} table(s)")

            # Process shapes with memory monitoring
            for i, shape in enumerate(slide.shapes):
                self._process_shape_recursively(shape, target_lang)

                # Update peak memory tracking
                if i % 3 == 0:
                    monitor.update_peak()

                # Force cleanup every few shapes if memory usage is high
                if i % 5 == 0 and self._get_memory_usage_mb() > self._memory_threshold_mb:
                    self._force_garbage_collection()

    def batch_translate(self, texts: List[str], target_lang: str, batch_size: int = 50, operation_type: str = "general") -> List[str]:
        import time
        start_time = time.time()

        # Use enhanced adaptive batch sizing
        adaptive_batch_size = self._get_adaptive_batch_size(batch_size, operation_type)
        logger.debug(f"Using batch size: {adaptive_batch_size} (requested: {batch_size}, operation: {operation_type})")

        all_translations = []
        translator = Translator()
        prompt = translator.get_default_prompt(target_lang, self.source_language)

        with self._memory_monitor_context(f"batch translation ({len(texts)} texts)") as monitor:
            for i in range(0, len(texts), adaptive_batch_size):
                batch_start_time = time.time()
                batch = texts[i:i+adaptive_batch_size]
                non_empty_batch = []
                non_empty_indices = []

                for j, text in enumerate(batch):
                    if text.strip():
                        non_empty_batch.append(text)
                        non_empty_indices.append(j)

                if not non_empty_batch:
                    all_translations.extend(batch)
                    continue

                dict_data = {str(k+1): text for k, text in enumerate(non_empty_batch)}

                try:
                    result = translator.submit_to_gpt(dict_data, prompt, file_extension="pptx") if self.file_context is None else translator.submit_to_gpt(dict_data, prompt, self.file_context, file_extension="pptx")
                    if result == '{}':
                        translated_batch = batch[:]
                    else:
                        translated_texts = json.loads(result)
                        translated_batch = batch[:]
                        for k, original_idx in enumerate(non_empty_indices):
                            key = str(k+1)
                            if key in translated_texts:
                                translated_batch[original_idx] = translated_texts[key]
                    all_translations.extend(translated_batch)

                    # Clear intermediate variables to free memory
                    del dict_data, translated_batch
                    if 'translated_texts' in locals():
                        del translated_texts

                except Exception as e:
                    logger.error(f"Translation batch error: {e}")
                    all_translations.extend(batch)

                # Record batch performance
                batch_time = time.time() - batch_start_time
                self._record_performance(batch_time)

                # Update memory monitoring
                if i % 3 == 0:
                    monitor.update_peak()

                # Dynamic batch size adjustment based on performance
                if i > 0 and i % (adaptive_batch_size * 2) == 0:
                    # Recalculate batch size based on current conditions
                    new_batch_size = self._get_adaptive_batch_size(batch_size, operation_type)
                    if new_batch_size != adaptive_batch_size:
                        logger.debug(f"Adjusting batch size mid-operation: {adaptive_batch_size} -> {new_batch_size}")
                        adaptive_batch_size = new_batch_size

                # Force garbage collection every few batches if memory usage is high
                if i % (adaptive_batch_size * 3) == 0 and self._get_memory_usage_mb() > self._memory_threshold_mb:
                    self._force_garbage_collection()

        total_time = time.time() - start_time
        logger.info(f"Batch translation completed in {total_time:.2f}s for {len(texts)} texts")
        return all_translations

    def _translate_text_frame_runs_preserve_layout(self, text_frame, target_lang):
        """
        Optimized text frame translation that processes paragraphs individually
        to reduce memory usage and improve layout preservation.
        """
        for paragraph_idx, paragraph in enumerate(text_frame.paragraphs):
            runs = paragraph.runs
            if not runs:
                continue

            # Process runs in smaller groups to reduce memory usage
            self._translate_paragraph_runs(runs, target_lang)

            # Periodic cleanup for large text frames
            if paragraph_idx % 10 == 0 and paragraph_idx > 0:
                self._force_garbage_collection()

    def _translate_paragraph_runs(self, runs, target_lang):
        """Translate runs within a single paragraph while preserving layout"""
        original_texts = [run.text for run in runs]
        combined_text = " ".join(original_texts).strip()

        if not combined_text:
            return

        try:
            # Translate the combined text
            translated_text = self.batch_translate([combined_text], target_lang)[0]

            # Distribute translated text proportionally across runs
            total_original_chars = sum(len(t) for t in original_texts)
            if total_original_chars == 0:
                return

            translated_fragments = []
            start_idx = 0

            for original_text in original_texts:
                proportion = len(original_text) / total_original_chars
                chars_to_take = round(proportion * len(translated_text))
                translated_fragments.append(translated_text[start_idx:start_idx + chars_to_take])
                start_idx += chars_to_take

            # Handle any remaining characters
            if start_idx < len(translated_text):
                translated_fragments[-1] += translated_text[start_idx:]

            # Apply translated fragments to runs
            for run, new_text in zip(runs, translated_fragments):
                run.text = new_text

            # Clear intermediate variables
            del original_texts, translated_fragments, translated_text

        except Exception as e:
            logger.error(f"Error translating paragraph runs: {e}")
            # Keep original text on error

    def _translate_notes_slide(self, notes_slide, target_language: str) -> None:
        """Translate notes slide content with memory optimization and proper text handling"""
        
        # Collect all text frames to process them as complete units
        text_frames_data = []
        
        for shape in notes_slide.shapes:
            if shape.has_text_frame:
                # Extract complete text from each paragraph while preserving structure
                for paragraph_idx, paragraph in enumerate(shape.text_frame.paragraphs):
                    if paragraph.runs:  # Only process paragraphs that have runs
                        # Collect complete paragraph text and run information
                        paragraph_text = ""
                        runs_info = []
                        
                        for run in paragraph.runs:
                            if run.text:  # Include all text, even whitespace-only
                                run_start = len(paragraph_text)
                                paragraph_text += run.text
                                run_end = len(paragraph_text)
                                
                                runs_info.append({
                                    'run': run,
                                    'start': run_start,
                                    'end': run_end,
                                    'original_text': run.text
                                })
                        
                        if paragraph_text.strip():  # Only process non-empty paragraphs
                            text_frames_data.append({
                                'paragraph_text': paragraph_text,
                                'runs_info': runs_info,
                                'shape': shape,
                                'paragraph_idx': paragraph_idx
                            })
        
        if not text_frames_data:
            return
        
        with self._memory_monitor_context(f"notes translation ({len(text_frames_data)} paragraphs)") as monitor:
            # Process paragraphs in batches
            batch_size = self._get_adaptive_batch_size(10, "notes")  # Smaller batch for paragraphs
            
            for i in range(0, len(text_frames_data), batch_size):
                batch_data = text_frames_data[i:i+batch_size]
                batch_texts = [item['paragraph_text'] for item in batch_data]
                
                # Translate complete paragraphs
                translated_batch = self.batch_translate(batch_texts, target_language, operation_type="notes")
                
                # Apply translations back to runs
                for item, translated_text in zip(batch_data, translated_batch):
                    self._apply_translation_to_runs(item, translated_text)
                
                # Update memory monitoring
                monitor.update_peak()
                
                # Clear batch variables
                del batch_data, batch_texts, translated_batch

    def _apply_translation_to_runs(self, paragraph_data, translated_text):
        """Apply translated text back to runs while preserving formatting"""
        runs_info = paragraph_data['runs_info']
        
        if not runs_info:
            return
        
        # Simple approach: distribute translated text proportionally
        original_text = paragraph_data['paragraph_text']
        
        if not original_text.strip():
            return
        
        # If we have only one run, simple assignment
        if len(runs_info) == 1:
            runs_info[0]['run'].text = translated_text
            return
        
        # For multiple runs, we need to be more careful
        # Strategy: preserve the first and last run's formatting, 
        # put most content in the first substantial run
        
        # Find the first run with substantial content
        main_run_idx = 0
        for idx, run_info in enumerate(runs_info):
            if run_info['original_text'].strip():
                main_run_idx = idx
                break
        
        # Clear all runs first
        for run_info in runs_info:
            run_info['run'].text = ""
        
        # Put the translated text in the main run
        runs_info[main_run_idx]['run'].text = translated_text
        
        # Alternative approach: try to preserve spacing
        # You can uncomment this section if you need more sophisticated handling
        """
        # More sophisticated approach (optional)
        words_original = original_text.split()
        words_translated = translated_text.split()
        
        if len(words_original) == len(words_translated):
            # Try to map word by word
            word_idx = 0
            current_pos = 0
            
            for run_info in runs_info:
                run_text = run_info['original_text']
                if not run_text.strip():
                    continue  # Skip empty runs
                
                run_words = run_text.split()
                translated_run_words = []
                
                for _ in run_words:
                    if word_idx < len(words_translated):
                        translated_run_words.append(words_translated[word_idx])
                        word_idx += 1
                
                run_info['run'].text = ' '.join(translated_run_words)
        else:
            # Fallback to simple approach
            runs_info[main_run_idx]['run'].text = translated_text
        """
    
    # ---------------------- Multi-language handling---------------------- #
    def translate_presentation_multi_language(self, target_languages: List[str]) -> Dict[str, any]:
        """
        Optimized multi-language translation that loads the presentation once
        and creates copies for each language to reduce memory usage and I/O overhead.
        """
        results = {}
        original_presentation = None

        try:
            initial_memory = self._get_memory_usage_mb()
            logger.info(f"Starting multi-language translation for {len(target_languages)} languages (Memory: {initial_memory:.1f}MB)")

            # Load the original presentation once
            logger.info(f"Loading original presentation: {self.pptx_path}")
            original_presentation = Presentation(self.pptx_path)
            total_slides = len(original_presentation.slides)

            load_memory = self._get_memory_usage_mb()
            logger.info(f"Original presentation loaded with {total_slides} slides (Memory: {load_memory:.1f}MB)")

            for lang_index, target_language in enumerate(target_languages):
                logger.info(f"Processing language {lang_index + 1}/{len(target_languages)}: {target_language}")

                try:
                    # Create a copy of the presentation for this language
                    lang_presentation = self._create_presentation_copy(original_presentation)

                    # Translate the copy
                    translated_presentation = self._translate_presentation_in_place(lang_presentation, target_language)

                    if translated_presentation:
                        results[target_language] = translated_presentation
                        logger.info(f"Successfully translated presentation for {target_language}")
                    else:
                        logger.error(f"Failed to translate presentation for {target_language}")
                        results[target_language] = None

                    # Force cleanup after each language
                    self._force_garbage_collection()
                    current_memory = self._get_memory_usage_mb()
                    logger.info(f"Completed {target_language} (Memory: {current_memory:.1f}MB)")

                except Exception as e:
                    logger.error(f"Error translating to {target_language}: {e}")
                    results[target_language] = None

            final_memory = self._get_memory_usage_mb()
            logger.info(f"Multi-language translation complete. Memory: {initial_memory:.1f}MB -> {final_memory:.1f}MB")

            return results

        except Exception as e:
            logger.error(f"Error in multi-language translation: {e}")
            return {}
        finally:
            # Cleanup original presentation
            if original_presentation:
                del original_presentation
            self._force_garbage_collection()

    def _create_presentation_copy(self, original_presentation):
        """
        Create a copy of the presentation by saving to a temporary file and reloading.
        This is more memory efficient than deep copying the object structure.
        """
        import tempfile

        try:
            # Create a temporary file for the copy
            with tempfile.NamedTemporaryFile(suffix='.pptx', delete=False) as temp_file:
                temp_path = temp_file.name

            # Save the original to the temporary file
            original_presentation.save(temp_path)

            # Load the copy from the temporary file
            copy_presentation = Presentation(temp_path)

            # Clean up the temporary file
            try:
                os.unlink(temp_path)
            except Exception as e:
                logger.warning(f"Could not delete temporary file {temp_path}: {e}")

            return copy_presentation

        except Exception as e:
            logger.error(f"Error creating presentation copy: {e}")
            return None

    def _translate_presentation_in_place(self, presentation, target_language: str):
        """
        Translate a presentation object in place without reloading from file.
        """
        try:
            total_slides = len(presentation.slides)
            logger.info(f"Translating {total_slides} slides for {target_language}")

            for slide_num, slide in enumerate(presentation.slides, 1):
                logger.debug(f"Translating slide {slide_num}/{total_slides} for {target_language}")

                # Translate slide content
                self._translate_slide(slide, target_language)

                # Process notes slide if present
                if hasattr(slide, 'notes_slide') and slide.notes_slide:
                    self._translate_notes_slide(slide.notes_slide, target_language)

                # Periodic cleanup during translation
                if slide_num % 10 == 0:
                    self._force_garbage_collection()

            return presentation

        except Exception as e:
            logger.error(f"Error in in-place translation for {target_language}: {e}")
            return None

    # ---------------------- Analytics---------------------- #
    def get_slide_statistics(self, slide) -> Dict[str, int]:
        stats = {
            'total_shapes': 0,
            'grouped_shapes': 0,
            'tables': 0,
            'text_shapes': 0,
            'nested_depth': 0
        }
        def count_shapes_recursive(shapes, depth=0):
            stats['nested_depth'] = max(stats['nested_depth'], depth)
            for shape in shapes:
                stats['total_shapes'] += 1
                if shape.shape_type == MSO_SHAPE_TYPE.GROUP:
                    stats['grouped_shapes'] += 1
                    count_shapes_recursive(shape.shapes, depth + 1)
                elif shape.shape_type == MSO_SHAPE_TYPE.TABLE:
                    stats['tables'] += 1
                elif shape.has_text_frame and shape.text.strip():
                    stats['text_shapes'] += 1
        count_shapes_recursive(slide.shapes)
        return stats

    # ---------------------- Public Workflow---------------------- #
    def translate_presentation(self, target_language: str):
        import zipfile
        initial_memory = self._get_memory_usage_mb()
        logger.info(f"Loading presentation: {self.pptx_path} (Initial memory: {initial_memory:.1f}MB)")
        try:
            presentation = Presentation(self.pptx_path)
        except Exception as e:
            # Fallback: try to remove corrupted media and reload
            logger.error(f"Error loading presentation: {e}")
            if 'CRC' in str(e) or 'Bad' in str(e):
                logger.warning("Attempting to remove corrupted media and reload PPTX.")
                try:
                    fixed_path = self.pptx_path + ".fixed.pptx"
                    with zipfile.ZipFile(self.pptx_path, 'r') as zin:
                        with zipfile.ZipFile(fixed_path, 'w') as zout:
                            for item in zin.infolist():
                                # Skip corrupted media files (commonly mp4)
                                if item.filename.endswith('.mp4') or item.filename.startswith('ppt/media/'):
                                    try:
                                        data = zin.read(item.filename)
                                        zout.writestr(item, data)
                                    except Exception as media_e:
                                        logger.warning(f"Skipped corrupted media: {item.filename} ({media_e})")
                                else:
                                    try:
                                        data = zin.read(item.filename)
                                        zout.writestr(item, data)
                                    except Exception as other_e:
                                        logger.warning(f"Skipped file: {item.filename} ({other_e})")
                    logger.info(f"Reloading presentation from fixed file: {fixed_path}")
                    presentation = Presentation(fixed_path)
                except Exception as fix_e:
                    logger.error(f"Failed to fix PPTX: {fix_e}")
                    self._force_garbage_collection()
                    return None
            else:
                self._force_garbage_collection()
                return None

        total_slides = len(presentation.slides)

        # Calculate presentation characteristics for adaptive optimization
        total_shapes = 0
        for slide in presentation.slides:
            slide_stats = self.get_slide_statistics(slide)
            total_shapes += slide_stats['total_shapes']

        avg_shapes_per_slide = total_shapes / total_slides if total_slides > 0 else 0
        self._update_presentation_size_factor(total_slides, avg_shapes_per_slide)

        load_memory = self._get_memory_usage_mb()
        logger.info(f"Starting translation of {total_slides} slides, {total_shapes} total shapes (Memory after load: {load_memory:.1f}MB)")

        for slide_num, slide in enumerate(presentation.slides, 1):
            logger.info(f"Translating slide {slide_num}/{total_slides}")
            slide_stats = self.get_slide_statistics(slide)
            logger.debug(f"Slide {slide_num} statistics: {slide_stats}")

            # Translate slide content
            self._translate_slide(slide, target_language)

            # Process notes slide if present
            if hasattr(slide, 'notes_slide') and slide.notes_slide:
                logger.info(f"Processing notes for slide {slide_num}")
                self._translate_notes_slide(slide.notes_slide, target_language)

            # Adaptive cleanup frequency based on presentation size
            cleanup_frequency = max(3, min(10, total_slides // 10))
            if slide_num % cleanup_frequency == 0:
                self._force_garbage_collection()
                current_memory = self._get_memory_usage_mb()
                logger.info(f"Processed {slide_num}/{total_slides} slides (Memory: {current_memory:.1f}MB)")

        final_memory = self._get_memory_usage_mb()
        logger.info(f"Translation complete. Memory usage: {initial_memory:.1f}MB -> {final_memory:.1f}MB")
        return presentation

    def write_result_to_file(self, presentation: Presentation, lang: str):
        logger.info(f"Writing results to PowerPoint for language: {lang}")
        original_base, original_ext = os.path.splitext(self.pptx_path)
        translated_path = f"{original_base}_{lang}{original_ext}"
        
        # Ensure the directory exists
        directory = os.path.dirname(translated_path)
        if not os.path.exists(directory):
            logger.info(f"Creating directory: {directory}")
            os.makedirs(directory, exist_ok=True)
        
        try:
            presentation.save(translated_path)
            smartart_data = self._extract_smartart_texts(translated_path)
            if smartart_data:
                logger.info(f"Trovati {len(smartart_data)} SmartArt, avvio traduzione...")
                replacements = {}
                for filename, texts in smartart_data.items():
                    translated = self.batch_translate(texts, lang)
                    replacements[filename] = translated

                # scrive un nuovo file con SmartArt tradotti
                # smartart_out = translated_path
                self._replace_smartart_texts(translated_path, replacements, translated_path)
                logger.info(f"SmartArt tradotti salvati in: {translated_path}")
            logger.info(f"Successfully saved translated PowerPoint file: {translated_path}")
        except Exception as e:
            logger.error(f"Error saving translated PowerPoint file: {e}")
            raise

    # ---------------------- Preview Workflow ---------------------- #
    def get_preview_content(self, max_slides: int = 10) -> Dict[str, str]:
        """
        Extract the first meaningful text content from the PowerPoint presentation for preview translation.
        Returns a dictionary with slide numbers as keys and text content as values.
        Now selects the first portion of the presentation for a more coherent preview.
        """
        try:
            from pptx import Presentation
            
            presentation = Presentation(self.pptx_path)
            
            # Get the first meaningful slides from the beginning of the presentation
            selected_content = []
            slides_found = 0
            
            for i, slide in enumerate(presentation.slides):
                slide_text = self._extract_slide_text(slide)
                
                # Skip slides with no meaningful text content
                if not slide_text or len(slide_text.strip()) < 10:
                    continue
                
                # Add this slide to our selection
                selected_content.append((i, slide_text))
                slides_found += 1
                
                # Stop when we have enough slides
                if slides_found >= max_slides:
                    break
            
            # Convert to dictionary format expected by the translator
            preview_content = {}
            for slide_index, text in selected_content:
                # Use 1-based numbering for the key but store 0-based index info
                preview_content[f"slide_{slide_index + 1}"] = text
            
            logger.info(f"Extracted first {len(preview_content)} meaningful slides for preview from {len(presentation.slides)} total slides")
            logger.info(f"Selected slide indices: {[idx for idx, _ in selected_content]}")
            return preview_content
        
        except Exception as exc:
            logger.error("Error extracting preview content: %s", exc)
            return {}

    def create_shortened_presentation(self, max_slides: int = 5) -> str:
        """
        Create a shortened version of the original presentation (maximum 5 slides).
        Preserves images, charts, and embedded objects using presentation cloning approach.
        Returns the path to the temporary shortened presentation.
        """
        try:
            import tempfile
            import shutil
            import os
            from pptx import Presentation
            
            logger.info(f"Creating shortened presentation with media preservation (target: {max_slides} slides)")
            
            # Create a copy of the original presentation file to preserve all relationships
            with tempfile.NamedTemporaryFile(suffix='_temp_copy.pptx', delete=False) as temp_copy:
                temp_copy_path = temp_copy.name
            
            shutil.copy2(self.pptx_path, temp_copy_path)
            
            # Load the copied presentation
            shortened_pres = Presentation(temp_copy_path)
            
            # Copy presentation properties
            self._update_presentation_properties(shortened_pres)
            
            # Remove slides after the target number
            success = self._remove_slides_after_limit(shortened_pres, max_slides)
            
            if not success:
                logger.error("Failed to remove slides")
                # Clean up temp file
                try:
                    os.unlink(temp_copy_path)
                except:
                    pass
                return None
            
            # Save the modified presentation to final location
            with tempfile.NamedTemporaryFile(suffix='_shortened.pptx', delete=False) as temp_file:
                final_path = temp_file.name
            
            shortened_pres.save(final_path)
            
            # Verify and log media preservation
            media_preserved = self._preserve_media_in_shortened_presentation(shortened_pres)
            
            # Clean up temporary copy
            try:
                os.unlink(temp_copy_path)
            except:
                pass
            
            # Log statistics
            stats = self._get_presentation_stats(shortened_pres)
            logger.info(f"Created shortened presentation with preserved media: {final_path}")
            logger.info(f"Presentation stats: {stats}")
            logger.info(f"Media elements preserved: {media_preserved}")
            
            return final_path
        
        except Exception as exc:
            logger.error("Error creating shortened presentation: %s", exc)
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            return None

    def _update_presentation_properties(self, presentation):
        """Update presentation properties for the shortened version."""
        try:
            if presentation.core_properties:
                original_title = presentation.core_properties.title or 'Presentation'
                presentation.core_properties.title = f"Preview - {original_title}"
                presentation.core_properties.subject = f"Shortened Preview with Media"
        except Exception as e:
            logger.debug(f"Could not update presentation properties: {e}")

    def _remove_slides_after_limit(self, presentation, max_slides: int) -> bool:
        """
        Remove slides after the specified limit while preserving all relationships and media.
        """
        try:
            current_slide_count = len(presentation.slides)
            
            if current_slide_count <= max_slides:
                logger.info(f"Presentation already has {current_slide_count} slides, no removal needed")
                return True
            
            # Remove slides from the end (in reverse order to maintain indices)
            slides_to_remove = current_slide_count - max_slides
            
            for i in range(slides_to_remove):
                slide_index = current_slide_count - 1 - i
                try:
                    # Get slide reference before removal
                    slide = presentation.slides[slide_index]
                    
                    # Remove the slide (python-pptx handles relationship cleanup automatically)
                    rId = presentation.slides._sldIdLst[slide_index].rId
                    presentation.part.drop_rel(rId)
                    del presentation.slides._sldIdLst[slide_index]
                    
                    logger.debug(f"Removed slide at index {slide_index}")
                    
                except Exception as e:
                    logger.warning(f"Error removing slide {slide_index}: {e}")
                    continue
            
            final_slide_count = len(presentation.slides)
            logger.info(f"Removed {slides_to_remove} slides, final count: {final_slide_count}")
            
            return final_slide_count == max_slides
        
        except Exception as e:
            logger.error(f"Error removing slides: {e}")
            return False

    def _extract_slide_text(self, slide):
        """Extract all text content from a slide including text boxes and table cells."""
        try:
            text_content = []
            
            # Extract text from all shapes
            for shape in slide.shapes:
                if hasattr(shape, 'text') and shape.text.strip():
                    text_content.append(shape.text.strip())
                
                # Handle table content
                if shape.shape_type == 19:  # MSO_SHAPE_TYPE.TABLE
                    try:
                        table = shape.table
                        for row in table.rows:
                            for cell in row.cells:
                                if cell.text.strip():
                                    text_content.append(cell.text.strip())
                    except Exception as e:
                        logger.debug(f"Error extracting table text: {e}")
                
                # Handle grouped shapes
                elif hasattr(shape, 'shapes'):  # GroupShape
                    try:
                        for sub_shape in shape.shapes:
                            if hasattr(sub_shape, 'text') and sub_shape.text.strip():
                                text_content.append(sub_shape.text.strip())
                    except Exception as e:
                        logger.debug(f"Error extracting grouped shape text: {e}")
            
            return ' '.join(text_content)
        
        except Exception as e:
            logger.debug(f"Error extracting slide text: {e}")
            return ""

    def _slide_contains_media(self, slide) -> bool:
        """Check if a slide contains images, videos, or other media."""
        try:
            for shape in slide.shapes:
                # Check for pictures
                if shape.shape_type == 13:  # MSO_SHAPE_TYPE.PICTURE
                    return True
                
                # Check for media (video/audio)
                elif shape.shape_type == 16:  # MSO_SHAPE_TYPE.MEDIA
                    return True
                
                # Check for charts
                elif shape.shape_type == 3:  # MSO_SHAPE_TYPE.CHART
                    return True
                
                # Check for embedded objects
                elif shape.shape_type == 7:  # MSO_SHAPE_TYPE.EMBEDDED_OLE_OBJECT
                    return True
                
                # Check for linked objects
                elif shape.shape_type == 10:  # MSO_SHAPE_TYPE.LINKED_OLE_OBJECT
                    return True
            
            return False
        
        except Exception as e:
            logger.debug(f"Error checking for media in slide: {e}")
            return False

    def _preserve_media_in_shortened_presentation(self, presentation):
        """Ensure media elements are properly preserved in the shortened presentation."""
        try:
            media_count = 0
            image_count = 0
            chart_count = 0
            
            for slide in presentation.slides:
                for shape in slide.shapes:
                    if shape.shape_type == 13:  # Picture
                        image_count += 1
                    elif shape.shape_type == 3:  # Chart
                        chart_count += 1
                    elif shape.shape_type in [7, 10, 16]:  # Embedded/Linked/Media objects
                        media_count += 1
            
            total_media = image_count + chart_count + media_count
            logger.info(f"Preserved {total_media} media elements ({image_count} images, {chart_count} charts, {media_count} other media)")
            return total_media
        
        except Exception as e:
            logger.warning(f"Error preserving media: {e}")
            return 0

    def _get_presentation_stats(self, presentation):
        """Get basic statistics about the presentation including media count."""
        try:
            slides_with_text = 0
            total_text_chars = 0
            total_shapes = 0
            slides_with_media = 0
            
            for slide in presentation.slides:
                slide_text = self._extract_slide_text(slide)
                if slide_text.strip():
                    slides_with_text += 1
                    total_text_chars += len(slide_text)
                
                total_shapes += len(slide.shapes)
                
                if self._slide_contains_media(slide):
                    slides_with_media += 1
            
            return {
                'total_slides': len(presentation.slides),
                'slides_with_text': slides_with_text,
                'slides_with_media': slides_with_media,
                'total_shapes': total_shapes,
                'total_characters': total_text_chars,
                'avg_chars_per_slide': total_text_chars / slides_with_text if slides_with_text > 0 else 0,
                'avg_shapes_per_slide': total_shapes / len(presentation.slides) if presentation.slides else 0
            }
        
        except Exception as e:
            logger.warning(f"Error getting presentation stats: {e}")
            return {'error': str(e)}

    def create_preview_presentation(self, target_language: str, max_slides: int = 3) -> str:
        """
        Create a preview PowerPoint presentation with the same layout as original but with translated text.
        Returns the path to the temporary preview presentation.
        """
        try:
            logger.info(f"Creating preview presentation using shortened presentation approach for {target_language}")
            
            # Step 1: Create a shortened version of the original presentation (3-5 slides)
            shortened_pres_path = self.create_shortened_presentation(max_slides=max_slides)
            
            if not shortened_pres_path:
                logger.error("Failed to create shortened presentation")
                return None
            
            logger.info(f"Created shortened presentation: {shortened_pres_path}")
            
            # Step 2: Create a new PowerPointHandler for the shortened presentation
            shortened_handler = PPTXHandler(shortened_pres_path, self.file_context, self.source_language)
            
            # Step 3: Use the standard translation process on the shortened presentation
            logger.info("Translating shortened presentation using standard translation process")
            translated_presentation = shortened_handler.translate_presentation(target_language)
            
            if not translated_presentation:
                logger.error("Failed to translate shortened presentation")
                # Clean up shortened presentation
                try:
                    os.unlink(shortened_pres_path)
                except:
                    pass
                return None
            
            logger.info(f"Successfully translated shortened presentation")
            
            # Step 4: Save the translated presentation to a temporary file
            import tempfile
            with tempfile.NamedTemporaryFile(suffix='_preview.pptx', delete=False) as temp_file:
                preview_pres_path = temp_file.name
            
            translated_presentation.save(preview_pres_path)
            logger.info(f"Saved preview presentation to: {preview_pres_path}")
            
            # Step 5: Clean up the temporary shortened presentation
            try:
                os.unlink(shortened_pres_path)
                logger.info("Cleaned up temporary shortened presentation")
            except Exception as e:
                logger.warning(f"Could not clean up shortened presentation: {e}")
            
            # Step 6: Return the translated presentation path
            return preview_pres_path
        
        except Exception as exc:
            logger.error("Error creating preview presentation: %s", exc)
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            return None

    def get_translation_stats(self) -> Dict[str, object]:
        """Return statistics about the current presentation."""
        try:
            from pptx import Presentation
            
            presentation = Presentation(self.pptx_path)
            
            slides_with_text = 0
            total_text_length = 0
            slides_with_media = 0
            total_shapes = 0
            
            for slide in presentation.slides:
                slide_text = self._extract_slide_text(slide)
                if slide_text.strip():
                    slides_with_text += 1
                    total_text_length += len(slide_text)
                
                if self._slide_contains_media(slide):
                    slides_with_media += 1
                
                total_shapes += len(slide.shapes)
            
            stats = {
                "total_slides": len(presentation.slides),
                "slides_with_text": slides_with_text,
                "slides_with_media": slides_with_media,
                "total_shapes": total_shapes,
                "average_text_per_slide": (total_text_length / slides_with_text) if slides_with_text > 0 else 0,
                "slides_with_short_text": len([s for s in presentation.slides if len(self._extract_slide_text(s)) < 50]),
                "detected_language": getattr(self, "presentation_language", "unknown"),
                "file_size_bytes": os.path.getsize(self.pptx_path) if os.path.exists(self.pptx_path) else 0,
                "media_handling": "preserved"
            }
            
            return stats
        
        except Exception as exc:
            logger.error("Error getting translation stats: %s", exc)
            return {"error": str(exc)}
      
      
      
        
    def pptx_to_images(self, pptx_path, output_dir=None, max_slides=None):
        """Convert each slide in pptx to an image (PNG). Returns list of image paths."""
        import subprocess
        prs = Presentation(pptx_path)
        slides_to_convert = prs.slides

        output_dir = output_dir or tempfile.mkdtemp()
        image_paths = []

        for idx, slide in enumerate(slides_to_convert, start=1):
            # Save slide as EMF, then convert to PNG
            img_path = os.path.join(output_dir, f"slide_{idx}.png")
            libreoffice_path = "D:\LibreOffice\program"
            soffice_exe = os.path.join(libreoffice_path, "soffice.exe")
            command = [

            soffice_exe,

            "--headless",

            "--infilter=writer_pdf_import",

            "--convert-to", "docx",

            "--outdir", output_dir,

            pptx_path

        ]
            # Render slide as image
            # python-pptx non esporta immagini direttamente → possiamo usare 'unoconv' o LibreOffice headless:
            # soffice --headless --convert-to png --outdir output_dir pptx_path
            # Qui supponiamo che sia disponibile 'soffice' in PATH
            result = subprocess.run(

                command,

                capture_output=True,

                text=True,

                check=True,

                timeout=300  # Timeout di 5 minuti

            )

            # In genere soffice converte tutte le slide come slide1.png, slide2.png...
            # Aggiungi path di ogni slide generata
            for file in sorted(os.listdir(output_dir)):
                if file.lower().endswith(".png"):
                    image_paths.append(os.path.join(output_dir, file))

            break  # soffice converte tutte le slide in un colpo

        return image_paths
    


    def convert_pptx_to_html(self, pptx_file, output_dir=None):
        """
        Converte un file PowerPoint in HTML mantenendo il più possibile il layout originale
        """
        prs = Presentation(pptx_file)
        
        # Directory per salvare le immagini estratte
        if output_dir:
            images_dir = os.path.join(output_dir, 'images')
            os.makedirs(images_dir, exist_ok=True)
        else:
            images_dir = None
        
        # CSS per mantenere il layout
        css_styles = """
        <style>
            body {
                font-family: 'Segoe UI', Arial, sans-serif;
                margin: 0;
                padding: 20px;
                background-color: #f5f5f5;
            }
            .slide-container {
                background: white;
                margin-bottom: 30px;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                border-radius: 8px;
                overflow: hidden;
            }
            .slide {
                position: relative;
                width: 960px;
                height: 720px;
                margin: 0 auto;
                overflow: hidden;
            }
            .slide-number {
                position: absolute;
                top: -30px;
                left: 0;
                background: #007acc;
                color: white;
                padding: 5px 15px;
                border-radius: 4px 4px 0 0;
                font-size: 12px;
                font-weight: bold;
            }
            .shape {
                position: absolute;
                word-wrap: break-word;
                overflow-wrap: break-word;
            }
            .shape img {
                max-width: 100%;
                height: auto;
            }
            .shape table {
                border-collapse: collapse;
                width: 100%;
            }
            .shape table td, .shape table th {
                border: 1px solid #ddd;
                padding: 8px;
                text-align: left;
            }
            .shape table th {
                background-color: #f2f2f2;
                font-weight: bold;
            }
        </style>
        """
        
        html_content = [
            '<!DOCTYPE html>',
            '<html>',
            '<head>',
            '<meta charset="utf-8">',
            '<title>PowerPoint Preview</title>',
            css_styles,
            '</head>',
            '<body>'
        ]
        
        for i, slide in enumerate(prs.slides, 1):
            # Container della slide
            html_content.append('<div class="slide-container">')
            html_content.append(f'<div class="slide-number">Slide {i}</div>')
            
            # Slide con dimensioni fisse
            slide_style = 'position: relative; width: 960px; height: 720px; margin: 0 auto;'
            
            # Aggiungi background se presente
            if hasattr(slide, 'background') and slide.background.fill.type is not None:
                try:
                    if hasattr(slide.background.fill, 'fore_color'):
                        bg_color = slide.background.fill.fore_color.rgb
                        slide_style += f' background-color: rgb({bg_color.r}, {bg_color.g}, {bg_color.b});'
                except:
                    pass
            
            html_content.append(f'<div class="slide" style="{slide_style}">')
            
            # Processa ogni forma nella slide
            for shape in slide.shapes:
                shape_html = self.process_shape(shape, i, images_dir)
                if shape_html:
                    html_content.append(shape_html)
            
            html_content.append('</div>')  # Fine slide
            html_content.append('</div>')  # Fine slide-container
        
        html_content.extend(['</body>', '</html>'])
        return '\n'.join(html_content)

    def process_shape(self, shape, slide_number, images_dir=None):
        """
        Processa una singola forma della slide e la converte in HTML
        """
        if not hasattr(shape, 'left') or not hasattr(shape, 'top'):
            return ''
        
        # Calcola posizione e dimensioni
        left_px = int(shape.left.pt) if hasattr(shape.left, 'pt') else 0
        top_px = int(shape.top.pt) if hasattr(shape.top, 'pt') else 0
        width_px = int(shape.width.pt) if hasattr(shape.width, 'pt') else 'auto'
        height_px = int(shape.height.pt) if hasattr(shape.height, 'pt') else 'auto'
        
        base_style = f'position: absolute; left: {left_px}px; top: {top_px}px;'
        if width_px != 'auto':
            base_style += f' width: {width_px}px;'
        if height_px != 'auto':
            base_style += f' height: {height_px}px;'
        
        # Gestione diversi tipi di forme
        if shape.shape_type == MSO_SHAPE_TYPE.PICTURE:
            return self.process_picture(shape, base_style, slide_number, images_dir)
        
        elif shape.shape_type == MSO_SHAPE_TYPE.TABLE:
            return self.process_table(shape, base_style)
        
        elif hasattr(shape, "text") and shape.text.strip():
            return self.process_text_shape(shape, base_style)
        
        return ''

    def process_text_shape(self, shape, base_style):
        """
        Processa forme di testo mantenendo la formattazione
        """
        text_content = []
        
        for paragraph in shape.text_frame.paragraphs:
            para_style = ''
            
            # Allineamento del paragrafo
            if hasattr(paragraph, 'alignment') and paragraph.alignment is not None:
                alignment_map = {0: 'left', 1: 'center', 2: 'right', 3: 'justify'}
                if paragraph.alignment.value in alignment_map:
                    para_style += f'text-align: {alignment_map[paragraph.alignment.value]};'
            
            para_content = []
            
            for run in paragraph.runs:
                run_text = html.escape(run.text)
                if not run_text.strip():
                    continue
                    
                run_style = ''
                
                # Font
                if hasattr(run.font, 'name') and run.font.name:
                    run_style += f'font-family: "{run.font.name}";'
                
                # Dimensione font
                if hasattr(run.font, 'size') and run.font.size:
                    run_style += f'font-size: {run.font.size.pt}pt;'
                
                # Grassetto
                if hasattr(run.font, 'bold') and run.font.bold:
                    run_style += 'font-weight: bold;'
                
                # Corsivo
                if hasattr(run.font, 'italic') and run.font.italic:
                    run_style += 'font-style: italic;'
                
                # Sottolineato
                if hasattr(run.font, 'underline') and run.font.underline:
                    run_style += 'text-decoration: underline;'
                
                # Colore del testo
                try:
                    if hasattr(run.font, 'color') and run.font.color.rgb:
                        rgb = run.font.color.rgb
                        run_style += f'color: rgb({rgb.r}, {rgb.g}, {rgb.b});'
                except:
                    pass
                
                if run_style:
                    para_content.append(f'<span style="{run_style}">{run_text}</span>')
                else:
                    para_content.append(run_text)
            
            if para_content:
                para_text = ''.join(para_content)
                if para_style:
                    text_content.append(f'<p style="{para_style}">{para_text}</p>')
                else:
                    text_content.append(f'<p>{para_text}</p>')
        
        if text_content:
            return f'<div class="shape" style="{base_style}">{"".join(text_content)}</div>'
        
        return ''

    def process_table(self, shape, base_style):
        """
        Processa tabelle PowerPoint
        """
        if not hasattr(shape, 'table'):
            return ''
        
        table = shape.table
        table_html = ['<table style="width: 100%;">']
        
        for row_idx, row in enumerate(table.rows):
            table_html.append('<tr>')
            
            for cell_idx, cell in enumerate(row.cells):
                cell_text = html.escape(cell.text.strip()) if cell.text else ''
                
                # Determina se è header (prima riga)
                tag = 'th' if row_idx == 0 else 'td'
                
                table_html.append(f'<{tag}>{cell_text}</{tag}>')
            
            table_html.append('</tr>')
        
        table_html.append('</table>')
        
        return f'<div class="shape" style="{base_style}">{"".join(table_html)}</div>'

    def process_picture(self, shape, base_style, slide_number, images_dir=None):
        """
        Processa immagini PowerPoint
        """
        try:
            if not hasattr(shape, 'image'):
                return ''
            
            image = shape.image
            
            if images_dir:
                # Salva l'immagine
                image_filename = f'slide_{slide_number}_image_{id(shape)}.{image.ext}'
                image_path = os.path.join(images_dir, image_filename)
                
                with open(image_path, 'wb') as img_file:
                    img_file.write(image.blob)
                
                img_src = f'images/{image_filename}'
            else:
                # Usa data URL se non c'è directory di output
                import base64
                img_data = base64.b64encode(image.blob).decode()
                img_src = f'data:image/{image.ext};base64,{img_data}'
            
            return f'<div class="shape" style="{base_style}"><img src="{img_src}" alt="PowerPoint Image"></div>'
        
        except Exception as e:
            print(f"Errore nel processare l'immagine: {e}")
            return ''
        
class MemoryMonitorContext:
    """Context manager for monitoring memory usage during operations"""

    def __init__(self, operation_name: str, handler):
        self.operation_name = operation_name
        self.handler = handler
        self.start_memory = 0
        self.peak_memory = 0

    def __enter__(self):
        self.start_memory = self.handler._get_memory_usage_mb()
        self.peak_memory = self.start_memory
        logger.debug(f"Starting {self.operation_name} (Memory: {self.start_memory:.1f}MB)")
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        end_memory = self.handler._get_memory_usage_mb()
        self.peak_memory = max(self.peak_memory, end_memory)

        if exc_type is None:
            logger.debug(f"Completed {self.operation_name} (Memory: {self.start_memory:.1f}MB -> {end_memory:.1f}MB, Peak: {self.peak_memory:.1f}MB)")
        else:
            logger.error(f"Failed {self.operation_name} (Memory: {self.start_memory:.1f}MB -> {end_memory:.1f}MB, Peak: {self.peak_memory:.1f}MB)")

        # Force cleanup if memory usage increased significantly
        if end_memory > self.start_memory + 50:  # More than 50MB increase
            self.handler._force_garbage_collection()

    def update_peak(self):
        """Update peak memory usage"""
        current_memory = self.handler._get_memory_usage_mb()
        self.peak_memory = max(self.peak_memory, current_memory)