import ast
import asyncio
from argparse import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Namespace
import os
import tempfile
from typing import Iterator, <PERSON>, Tuple, Union

from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers.string import StrOutputParser
from langchain_core.output_parsers import JsonOutputParser
from langchain.schema.runnable import RunnablePassthrough

from src.backend.contracts.chat_data import BotType, AgentName
from src.backend.contracts.chat_data import AgentName

from pydantic import BaseModel, Field

from src.agents.agent import Agent
from src.agents.answer import Answer

from src.bots.conversation_session import ConversationSession
from src.common_tools.history.history import ConversationHistory
from config.config import BlobStorageConfig
from utils.clients import AzureBlobClient, DatabaseClient
from config.config import ImageGenConfig
from io import BytesIO


from utils.core import get_logger
import base64

import re

logger = get_logger(__file__)
# Use system temp directory for generated images
GENERATED_IMAGE_FOLDER = os.path.join(tempfile.gettempdir(), "compli-bot", "generated_images") + os.sep
# Ensure the temp directory exists
os.makedirs(GENERATED_IMAGE_FOLDER, exist_ok=True)

class ImageGenAnswer(Answer):
    
    image: str = None
    confidence: float = 1.0
    

    def __init__(self, image: str) -> None:
        self.image = image

    def add_images(self, images: List[str]) -> None:
        self.images = images

    def to_json(self):
        return {
            "agent": self.agent_name,
            "confidence": 1.0,
            "status": "complete"
        }

class image_gen(BaseModel):
    """"This function should be called when the user asks questions that will require the generation of image Typical queries might include:

    - Requests for image generation ("Generate a photorealistic image of an oven", "Generate a conceptual image of a fryer", "Can you generate a photo realistic image of a microwave oven?").
    """

    query: str =  Field(description=" The question that will be used to generate an image")

class GeneratedAnswer(BaseModel):
    ANSWER: str = Field(description="The answer, as the genrated image, you provide for the question")

class ImageGen(Agent):
    def __init__(self, bot_name: str, name=AgentName.IMAGE_GEN.value, model = None) -> None:
        """Initialize the  ImageGen agent that is responsable of generating image given an input description

        """
        super().__init__(name, bot_name)
        config = ImageGenConfig()
        self.llm = model._client

        self.prompt = """
            You are an assistant for image generation tasks. You are an image generator agent for Electrolux Professional, a multinational company which manufactures a comprehensive range of innovative products for food service, beverage, and laundry solutions: their food service products include ovens, blast chillers, cooking ranges, refrigerated cabinets and counters, freezers, cutters, mixers, and dishwashing equipment; their beverage products encompass coffee machines, hot and cold beverage dispensers, and frozen drink and soft-serve products; finally, their laundry solutions include front-load washers, efficient dispensing systems, barrier washers, ironers, finishing machines, tumble dryers, and drying cabinets.
            You should generate photorealistic or concept images considering what the user asks.

            Give as the Answer only the generated image.
            """

        self.question_prompt = """
            Question: {question}  
            Answer:
            """

    def ask(self, inquiry:str, history: ConversationHistory, stream:bool, conversation_session: ConversationSession = None, show_explanation: bool = False, images:list = []) -> Tuple[ImageGenAnswer, str]:

        self.session = conversation_session
        self.session.checkpoint()

        response = self.generate_image(inquiry, images, history, stream)

        answer = response

        self.session.checkpoint()

        image_gen_answer = self.build_image_gen_answer(answer, show_explanation)

        self.session.checkpoint()

        return image_gen_answer
    
    def generate_image(self, query: str, images:list, history: ConversationHistory, stream:bool):
        prompt_string = self.prompt + query

        # prompt_string = ChatPromptTemplate.from_template(prompt_string)
        
        prompt = prompt_string

        if stream:
            chain = prompt | self.llm | StrOutputParser()
            answer = chain.stream(query)
            return answer    
        else:
            isFollowUp = is_followup_on_same_topic(history)
            if not isFollowUp:
                if images == None:
                    answer = self.llm.images.generate(
                        model='gpt-image-1',
                        prompt = prompt,
                        n = 1
                    )
                else:
                    image  = images[0]
                    image = image.split(",")[1]
                    image_data = base64.b64decode(image)
                    tmp_file = GENERATED_IMAGE_FOLDER + "tmp.png"
                    with open(tmp_file, "wb") as f:
                        f.write(image_data)
                    with open(tmp_file, "rb") as image_file:
                        answer = self.llm.images.edit(
                            model='gpt-image-1',
                            image=image_file,
                            prompt = prompt,
                            n = 1
                        )   
                    
                image_base64 = answer.data[0].b64_json
                image_bytes = base64.b64decode(image_base64)
                image_name = GENERATED_IMAGE_FOLDER + "image.png"
                with open(image_name, "wb") as f:
                    f.write(image_bytes)
            else:
                if images == None:
                    previous_image = GENERATED_IMAGE_FOLDER + "image.png"
                    with open(previous_image, "rb") as image_file:
                        answer = self.llm.images.edit(
                            model='gpt-image-1',
                            image=image_file,
                            prompt=prompt,
                            n=1
                        )
                else:
                    image  = images[0]
                    image = image.split(",")[1]
                    image_data = base64.b64decode(image)
                    tmp_file = GENERATED_IMAGE_FOLDER + "tmp.png"
                    previous_image = GENERATED_IMAGE_FOLDER + "image.png"
                    with open(tmp_file, "wb") as f:
                        f.write(image_data)
                    
                    with open(tmp_file, "rb") as image_file:
                        answer = self.llm.images.edit(
                            model='gpt-image-1',
                            image=image_file,
                            prompt=prompt,
                            n=1
                        )

                image_base64 = answer.data[0].b64_json
                image_bytes = base64.b64decode(image_base64)
                image_name = GENERATED_IMAGE_FOLDER + "image.png"
                with open(image_name, "wb") as f:
                    f.write(image_bytes)
            answer = image_base64
            return answer
        
    def build_image_gen_answer(self, answer, show_explanation):

        image_gen_answer = ImageGenAnswer(image=answer)
        image_gen_answer.add_agent_name(self.agent_name)

        return image_gen_answer
    
def is_followup_on_same_topic(history: ConversationHistory) -> bool:
    return len(history.messages) > 0 and history.messages[-1]["agent"] == AgentName.IMAGE_GEN.name 
    
    