.questionInputContainer {
    border-radius: 8px;
    box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.14), 0px 0px 2px rgba(0, 0, 0, 0.12);
    height: 90px;
    width: 100%;
    padding: 15px;
    background: white;
}

.questionInputTextArea {
    width: 100%;
    line-height: 40px;
    font-size: 30px !important;
    color: brown !important;
    
}

.questionInputButtonsContainer {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
}

.questionInputSendButton {
    cursor: pointer;
}

.questionInputSendButtonDisabled {
    opacity: 0.4;
}

.previewContainer {
  display: flex;
  flex-direction: column;
  gap: 4px;
  max-height: 120px;
  overflow-y: auto;
  margin-right: 8px;
}

.previewImageWrapper {
  position: relative;
  width: 48px;
  height: 48px;
  border-radius: 4px;
  overflow: hidden;
}

.previewImage {
  width: 100%;
  height: 100%;
  border-radius: 4px;
  object-fit: cover;
}

.previewRemove {
  position: absolute;
  top: -4px;
  right: 2px;
  background: white;
  border-radius: 50%;
  font-size: 14px;
  line-height: 16px;
  cursor: pointer;
  padding: 0 4px;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);
}
