from azure.core.credentials import AzureKeyCredential
from azure.search.documents.indexes import SearchIndexClient
from azure.search.documents.indexes.models import (
    ExhaustiveKnnAlgorithmConfiguration,
    ExhaustiveKnnParameters,
    SearchableField,
    SearchField,
    SearchFieldDataType,
    SearchIndex,
    SimpleField,
    VectorSearch,
    VectorSearchAlgorithmKind,
    VectorSearchAlgorithmMetric,
    VectorSearchProfile,
)

from config.config import Text2SQLConfig

__config = Text2SQLConfig()

__field_id_name = "example_id"
__field_inquiry_content_name = "plain_inquiry"
__field_sql_content_name = "plain_sql"
__field_inquiry_vector_name = "embedded_inquiry"
__field_sql_vector_name = "embedded_sql"
__field_bot_category = "bot"

__index_fields = [
    SimpleField(
        name=__field_id_name,
        type=SearchFieldDataType.String,
        key=True,
        filterable=True,
    ),
    SearchableField(
        name=__field_inquiry_content_name,
        type=SearchFieldDataType.String,
        analyzer_name="standard.lucene",
    ),
    SearchField(
        name=__field_inquiry_vector_name,
        type=SearchFieldDataType.Collection(SearchFieldDataType.Single),
        searchable=True,
        vector_search_dimensions=3072,
        vector_search_profile_name="inquiry_profile",
    ),
    SearchableField(
        name=__field_sql_content_name,
        type=SearchFieldDataType.String,
        analyzer_name="en.lucene",
    ),
    SearchField(
        name=__field_sql_vector_name,
        type=SearchFieldDataType.Collection(SearchFieldDataType.Single),
        searchable=True,
        vector_search_dimensions=186,
        vector_search_profile_name="sql_profile",
    ),
    SimpleField(
        name=__field_bot_category,
        type=SearchFieldDataType.Collection(SearchFieldDataType.String),
        filterable=True,
    ),
]

__vector_profile = [
    VectorSearchProfile(
        name="inquiry_profile",
        algorithm_configuration_name="knn-cosine",
    ),
    VectorSearchProfile(
        name="sql_profile",
        algorithm_configuration_name="knn-euclidean",
    ),
]

__vector_algorithm = [
    ExhaustiveKnnAlgorithmConfiguration(
        name="knn-cosine",
        kind=VectorSearchAlgorithmKind.EXHAUSTIVE_KNN,
        parameters=ExhaustiveKnnParameters(metric=VectorSearchAlgorithmMetric.COSINE),
    ),
    ExhaustiveKnnAlgorithmConfiguration(
        name="knn-euclidean",
        kind=VectorSearchAlgorithmKind.EXHAUSTIVE_KNN,
        parameters=ExhaustiveKnnParameters(
            metric=VectorSearchAlgorithmMetric.EUCLIDEAN
        ),
    ),
]

__vector_search = VectorSearch(algorithms=__vector_algorithm, profiles=__vector_profile)

# __semantic_configuration = [
#     SemanticConfiguration(
#         name=__semantic_reranker,
#         prioritized_fields=SemanticPrioritizedFields(
#             keywords_fields=[SemanticField(field_name=__field_metadata_name)],
#             content_fields=[SemanticField(field_name=__field_content_name)],
#         ),
#     )
# ]

if __name__ == "__main__":
    print(__config.compli_bot_azure_ai_search_endpoint)

    index = SearchIndex(
        name=__config.compli_bot_azure_ai_search_index,
        fields=__index_fields,
        vector_search=__vector_search,
    )
    client = SearchIndexClient(
        __config.compli_bot_azure_ai_search_endpoint,
        AzureKeyCredential(__config.compli_bot_azure_ai_search_admin_key),
    )

    if __config.compli_bot_azure_ai_search_index in client.list_index_names():
        client.delete_index(index)

    client.create_or_update_index(index)
    print("Index has been created!")
