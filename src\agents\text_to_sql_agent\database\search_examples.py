import argparse
import requests
from os.path import dirname, join
from typing import Dict, List, Optional
from openai import AzureOpenAI
from azure.core.credentials import AzureKeyCredential
from azure.search.documents import SearchClient
from azure.search.documents.models import VectorizedQuery

from config.config import Text2SQLConfig
from azure.core.pipeline.transport import RequestsTransport
from src.agents.text_to_sql_agent.database.relevant_schemas import get_relevant_schemas
from src.agents.text_to_sql_agent.models.preliminary import PreliminaryModel
from src.backend.contracts.chat_data import BotType
from src.common_tools.embedders.inquiry_embedder import InquiryEmbedder
from src.common_tools.embedders.sql_embedder import SqlEmbedder
from utils.core import Singleton, get_logger

logger = get_logger(__file__)


class ExampleFinder(metaclass=Singleton):
    def __init__(self, embedder_model, model) -> None:
        """
        Init the client to perform a search on the endpoint Azure AI Search resource.
        """
        logger.debug("Initialising clients...")
        self.cfg = Text2SQLConfig()
        self.inquiry_embedder = InquiryEmbedder(embedder_model)
        self.sql_embedder = SqlEmbedder(
            join(dirname(__file__), "../../../common_tools/embedders/keywords_dictionary.json")
        )
        self.preliminary = PreliminaryModel(model)

        logger.info("Clients initialised!")

    def search(
        self,
        inquiry: str,
        quantity: Optional[int] = 7,
        bot: str = BotType.COMPLI_BOT,
        example_id_filter: Optional[list] = []
    ) -> List[Dict[str, str]]:
        """
        Performs a vector search on the resource by first finding the most similar inquiries and selects the top `quantity` based on query similiarity.
        """
        embedded_inquiry = self.inquiry_embedder.embed(inquiry)

        # Set up the inquiry vector search query
        inquiry_search = VectorizedQuery(
            vector=embedded_inquiry,
            k_nearest_neighbors=2 * quantity,
            fields="embedded_inquiry",
        )
        # Search the ids of the tuples with most similar inquiries
        logger.debug("Searching client based on inquiry...")

        if bot == "CALL_CENTER_BOT_IMPLOSION" or bot == "CALL_CENTER_BOT_EXPLOSION":
            client = self._initialize_search_client(BotType.CALL_CENTER_BOT.name)
            filter = f"bot/any(x: x eq '{bot}')"
        else:
            client = self._initialize_search_client(bot.name)
            filter = f"bot/any(x: x eq '{bot.name}')"


        results = client.search(
            search_text=inquiry,
            vector_queries=[inquiry_search],
            top=2 * quantity,
            filter=filter
        )

        results = list(results)
        results = [
            {
                "id": example["example_id"],
                "inquiry": example["plain_inquiry"],
                "sql": example["plain_sql"],
                "score": example["@search.score"],
            }
            for example in results
        ]

        # Storing the search scores of the fetched documents
        inquiry_scores = {document["id"]: document["score"] for document in results}

        logger.info(
            f"Successfully fetched {len(inquiry_scores)} inquiry search results!"
        )

        if bot != "CALL_CENTER_BOT_IMPLOSION" and bot != "CALL_CENTER_BOT_EXPLOSION":
            path = bot.db_schema_path
        else:
            path = BotType.CALL_CENTER_BOT.db_schema_path

        # Generating preliminary query...
        sql = self.preliminary.predict(
            inquiry.split("\n")[-1],
            get_relevant_schemas(
                embedded_inquiry, path=path
            ),
            [(example["inquiry"], example["sql"]) for example in results],
        )
        embedded_sql = self.sql_embedder.embed(sql)

        # Set up the sql vector search query
        sql_search = VectorizedQuery(
            vector=embedded_sql, k_nearest_neighbors=2 * quantity, fields="embedded_sql"
        )

        # Force to consider only the results from the previous query
        sql_filter = " or ".join(
            [f"example_id eq '{result['id']}'" for result in results]
        )
        # Query the resource for the significant (inquiry; sql) pairs
        logger.debug(
            f"Searching client based on SQL, with following inquiry-generated filters: {sql_filter}"
        )
        results = client.search(
            search_text=sql,
            vector_queries=[sql_search],
            filter=sql_filter,
            top=2 * quantity,
        )
        results = list(results)
        # Updating the scores
        scores = []
        for document in results:
            id = document["example_id"]
            scores.append(
                (id, 0.8 * inquiry_scores[id] + 0.2 * document["@search.score"])
            )
        logger.info(f"Successfully fetched {len(results)} SQL search results!")

        # Finding the top `quantity` results
        scores = sorted(scores, key=lambda x: x[1], reverse=True)[:quantity]
        scores = [score[0] for score in scores]
        results = [data for data in results if data["example_id"] in scores]
        
        logger.info(f"The following example ids have been selected: {", ".join([example["example_id"] for example in results])}")

        if len(example_id_filter) > 0:
            results = [result for result in results if any(example_id in result["example_id"] for example_id in example_id_filter)]
        # Return the resulting examples in dictionary form
        examples = [
            {"inquiry": result["plain_inquiry"], "sql": result["plain_sql"]}
            for result in results
        ]
        return examples


    def _initialize_search_client(self,bot_name: str) -> SearchClient: 
        if bot_name == BotType.COMPLI_BOT.name: 
            endpoint = self.cfg.compli_bot_azure_ai_search_endpoint
            index = self.cfg.compli_bot_azure_ai_search_index
            key = AzureKeyCredential(str(self.cfg.compli_bot_azure_ai_search_query_key))
        elif bot_name == BotType.CALL_CENTER_BOT.name:
            endpoint = self.cfg.call_center_bot_azure_ai_search_endpoint
            index = self.cfg.call_center_bot_azure_ai_search_index
            key = AzureKeyCredential(str(self.cfg.call_center_bot_azure_ai_search_query_key))

        session = requests.Session()
        session.trust_env = False
        session.proxies = {}        

        transport = RequestsTransport(session=session)

        client = SearchClient(endpoint, index, key, transport = transport) 
        return client

def get_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("inquiry", type=str)
    return parser.parse_args()


if __name__ == "__main__":
    # logging.basicConfig(filename="./example_loading.log", level=logger.DEBUG)
    args = get_args()

    print(ExampleFinder().search(args.inquiry, bot=BotType.CALL_CENTER_BOT))
