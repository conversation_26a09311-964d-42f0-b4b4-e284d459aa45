import flask
from functools import wraps
from flask import jsonify, request, current_app
from src.backend.utils.api_key_manager import ApiKeyManager
import logging


def login_epr(f):
    """
    Decorator for flask endpoints, ensuring that the user is authenticated and redirecting to log-in page if not.
    Example:
    ```
        from flask import current_app as app
        @login_epr
        @app.route("/")
        def index():
            return 'route protected'
    ```
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):

        if not flask.session.get("user"):
            return flask.redirect(flask.url_for('auth.login'))

        return f(*args, **kwargs)
    return decorated_function


def login_api(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):

        if not flask.session.get("user"):
            return jsonify({"error": "Unauthorized"}), 401

        return f(*args, **kwargs)
    return decorated_function


def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        user = flask.session.get("user")
        if not user or not flask.session.get("is_admin"):
            return jsonify({"error": "Forbidden"}), 403
        return f(*args, **kwargs)
    return decorated_function


def api_key_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        """
        Look at api_key_manager.py for configuration details
        """
        auth_header = request.headers.get("Authorization", "")
        if not auth_header.startswith("Bearer "):
            logging.warning("API key authentication failed: missing or invalid Authorization header")
            return jsonify({"error": "Missing or invalid Authorization header"}), 401
        api_key = auth_header.replace("Bearer ", "", 1).strip()
        endpoint_name = f.__name__
        manager = ApiKeyManager()
        key_info = manager.get_by_api_key(api_key)
        authorized_routes = key_info.get("authorized_routes", []) if key_info else []
        if not key_info or (endpoint_name not in authorized_routes and "*" not in authorized_routes):
            logging.warning(f"API key authentication failed for key '{api_key}' on endpoint '{endpoint_name}'")
            return jsonify({"error": "Invalid or unauthorized API key"}), 401
        logging.info(f"API key authenticated: {key_info.get('name', 'Unknown')} (endpoint: {endpoint_name})")
        return f(*args, **kwargs)
    return decorated_function