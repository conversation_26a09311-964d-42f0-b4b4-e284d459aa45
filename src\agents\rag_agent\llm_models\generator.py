import json
from typing import Iterator, List, Union
import ast

from langchain.schema.runnable import RunnablePassthrough
from langchain_core.documents.base import Document
from langchain_core.output_parsers.string import StrOutputParser
from langchain_core.output_parsers import <PERSON><PERSON><PERSON>utputParser
from pydantic import BaseModel, Field
from langchain_core.prompts import ChatPromptTemplate

from utils.clients import AzureBlobClient
from src.agents.rag_agent.document_type_filter import DocumentType
from src.backend.contracts.chat_data import BotType



class GeneratedAnswer(BaseModel):
    ANSWER: str = Field(description="The answer you provide for the question")
    DOCUMENT: List[str] = Field(description="A list of strings, every string is structured as a concatenation of 'SOURCE FILE' and 'HEADER' field of the chunks you used for answering the question. consider only the chunks you really used and not all provided as input.")
    EXPLANATION: str = Field(description= "The reason why the answer to the question is provided.'I can't give an explanation' is the response if the answer cannot be found")
    CHUNKS: List[str] = Field(description="the list of chunks used for the answer referred with the number of chunk")
    
class AugmentedGenerator():
    def __init__(self, bot_name, model) -> None:

        self.llm = model._client
        
        self.base_answer_prompt = """
            You are an assistant for question-answering tasks. Use the following pieces of retrieved context to answer the question. If you don't know the answer, just say that you don't know. 
            Keep your answer very detailed with all the information necessary to answer the question and grounded in the documents provided.
            If the context is empty, answer that you can't find the answer because no context is provided.
            Take all information from the different chunks by adding all the possible details that are relevant to the question. 
            The longer the answer, the better it is. 
            The answer must come only from the context: the context is a text composed by different chunks that have this format:
            
            CHUNK N
            SOURCE FILE: <<the file from which the chunk comes from>>
            HEADER: <<the header of the chunk>>
            TEXT: << the text of the chunk, this is the part that you MUST consider when you answer the question>>
            """
        
        self.answer_image_prompt="""
            IMAGES: << the list of images included in the chunk of text >>  
            """
        
        self.multimodal_answer_image_prompt = """
            IMAGE: << the summaries of the images contained in the chunk, this a part that MUST be considered when you answer the question>>
            """
  
        self.context_prompt = """
            The answer must be a JSON with the following fields:
            - DOCUMENT: A list of strings, every string is the "SOURCE FILE" field of the chunks you used for answering the question. Consider only the chunks you really used for the answer and not all provided as input. Duplicates must not be present.
            - EXPLANATION: Why you provide the answer: explain very clear how you answered to the question and which files you used referring it with SOURCE FILE and HEADER section. If you can't fill this field, write "I can't give an explanation"
            - CHUNKS: the list of chunks used for the answer referred with the number of chunk and the portion of text that is used for the answer from the TEXT section. It must be a list of json where the key is "CHUNK N" and the value is the portion of text from TEXT section is used for the answer.
            
            answer ONLY with the JSON and don't provide other details on the answer.

            """
        
        self.table_prompt = """
            Before answering the question, consider if the answer of the question is contained in one of following tables written in markdown format:
            {tables}
            """
        
        self.images_prompt = """
            Before answering the question, consider if the answer of the question is contained in one of following images encoded in base64:
            {images}
        """
        
        self.question_prompt = """
            Question: {question} 
            Context: {context} 
            Answer:
            """
        

    def retrieve_tables_from_storage(self, blob_client: AzureBlobClient, tables_to_extract: set) -> List[str]:
        tables = []
        for table in tables_to_extract:
            if "table_name" not in table:
                tables.append(blob_client.read_table_blob(table))
            else:
                tables.append(blob_client.read_table_blob(table['table_name']))
        return tables
    
    def retrieve_images_from_storage(self, blob_client: AzureBlobClient, images_to_extract: set) -> List[str]:
        images = []
        for image in images_to_extract:
            images.append(blob_client.read_image_blob(image['image_name']))
            
        return images
        
    def get_header_from_chunk(self,document: Document):

        if 'Header 3' in document.metadata:
           return document.metadata['Header 3']
          
        elif 'Header 2' in document.metadata:
           return document.metadata['Header 2']
          
        elif 'Header 1' in document.metadata:
           return document.metadata['Header 1']
           
    def _prepare_chunks(self, relevant_documents: List[Document], bot_name: str) -> tuple:
        """
        Prepare document chunks and extract tables/images for the prompt.
        
        Args:
            relevant_documents (List[Document]): List of documents to process
            bot_name (str): Name of the bot processing the documents
        
        Returns:
            tuple: (docs, tables_to_extract, images_to_extract)
        """
        chunk_list = []
        chunk_index = 1
        
        for doc in relevant_documents:
            source_file = doc.metadata["url"]
            header = self.get_header_from_chunk(doc)
            text = doc.page_content
            
            if bot_name == BotType.JDAILO.name:
                images_summaries = [image["summary"] for image in doc.metadata["images"]] if doc.metadata["images"] else []
                chunk_for_prompt = (
                    f"CHUNK {chunk_index}\n"
                    f"SOURCE FILE: {source_file}\n"
                    f"HEADER: {header}\n"
                    f"TEXT: {text}\n"
                    f"IMAGES:{images_summaries}"
                )
            else:
                chunk_for_prompt = (
                    f"CHUNK {chunk_index}\n"
                    f"SOURCE FILE: {source_file}\n"
                    f"HEADER: {header}\n"
                    f"TEXT: {text}\n"
                    f"IMAGES:{doc.metadata['images'] if 'images' in doc.metadata else []}"
                )
            
            chunk_list.append(chunk_for_prompt)
            chunk_index += 1
        
        # Build the 'chunks' part of the prompt
        docs = "\n\n".join(chunk_list)

        # Handle table and image extraction
        if bot_name == BotType.JDAILO.name:
            # Process tables
            tables_to_extract = self.process_tables(relevant_documents, bot_name)

            # Process images
            images_array = [doc.metadata['images'] for doc in relevant_documents if 'images' in doc.metadata]
            flattened_images = [image for image_list in images_array for image in image_list]
            unique_images = list({json.dumps(image, sort_keys=True) for image in flattened_images})
            images_to_extract = [json.loads(image) for image in unique_images]
        elif bot_name == BotType.SEO_BOT.name:
            # Process tables
            tables_to_extract = self.process_tables(relevant_documents, bot_name)
            images_to_extract = []
        else:
            # Process tables for CALL_CENTER_BOT
            tables_to_extract = self.process_tables(relevant_documents, bot_name)
            images_to_extract = []

        return docs, tables_to_extract, images_to_extract
    
    def process_tables(self, relevant_documents:List[Document], bot_name) -> List:
        if bot_name ==  BotType.CALL_CENTER_BOT.name or bot_name == BotType.APPLICATION_HOWTO_BOT.name:
            tables_array = [ast.literal_eval(doc.metadata['tables']) for doc in relevant_documents if 'tables' in doc.metadata and isinstance(doc.metadata['tables'], str)]
            tables_to_extract = list(set([table for table_list in tables_array for table in table_list]))
        else:
            tables_array = [doc.metadata['tables'] for doc in relevant_documents if 'tables' in doc.metadata]
            flattened_tables = [table for table_list in tables_array for table in table_list]
            unique_tables = list({json.dumps(table, sort_keys=True) for table in flattened_tables})
            tables_to_extract = [json.loads(table) for table in unique_tables]
        return tables_to_extract

    # Generate function for the RAG agent 
    def generate(self, query: str, relevant_documents: List[Document], to_translate: str, blob_client: AzureBlobClient, bot_name: str, isContext:bool, stream: bool) -> Union[Iterator[str], str]:

        # Prepare chunks and extract tables/images
        docs, tables_to_extract, _ = self._prepare_chunks(relevant_documents, bot_name)
        
        prompt = {
            "context": lambda x: docs,
            "question": RunnablePassthrough(),
        }
        
        if bot_name == BotType.CALL_CENTER_BOT.name:
            # Behaviour for the CALL_CENTER_BOT
            # Retrieve tables from the blob to insert them in the prompt
            answer_prompt = self.base_answer_prompt + self.answer_image_prompt if isContext == False else self.base_answer_prompt + self.answer_image_prompt + self.context_prompt
            if len(tables_to_extract) > 0:
                if relevant_documents[0].metadata["document_type"] == DocumentType.SPC.name:
                    prompt_string = answer_prompt  + self.question_prompt
                else:
                    table_list = self.retrieve_tables_from_storage(blob_client, tables_to_extract)
                    tables = "\n\n".join(table_list)
                    prompt_string = answer_prompt  + self.table_prompt + self.question_prompt  
                    prompt["tables"] = lambda x: tables
            else:
                prompt_string = answer_prompt + self.question_prompt
        elif  bot_name == BotType.JDAILO.name:
            # Behaviour for the JDAILO
            # Retrieve tables from the blob to insert them in the prompt
            answer_prompt = self.base_answer_prompt + self.multimodal_answer_image_prompt if isContext == False else self.base_answer_prompt + self.multimodal_answer_image_prompt + self.context_prompt
            if len(tables_to_extract) > 0:
                table_list = self.retrieve_tables_from_storage(blob_client, tables_to_extract)
                tables = "\n\n".join(table_list)
                prompt_string = answer_prompt  + self.table_prompt   
                prompt["tables"] = lambda x: tables
            else:
                prompt_string = answer_prompt
            prompt_string = prompt_string + self.question_prompt
        else:
            # Behaviour for the SEO_Bot
            answer_prompt = self.base_answer_prompt  if isContext == False else self.base_answer_prompt + self.context_prompt
            if len(tables_to_extract) > 0:
                table_list = self.retrieve_tables_from_storage(blob_client, tables_to_extract)
                tables = "\n\n".join(table_list)
                prompt_string = answer_prompt  + self.table_prompt   
                prompt["tables"] = lambda x: tables
            else:
                prompt_string = answer_prompt

            # Retrieve images from the blob to insert them in the prompt
            # if len(images_to_extract) > 0:
            #     image_list = self.retrieve_images_from_storage(blob_client, images_to_extract)
            #     image_list = [image[0] for image in image_list]
            #     image_list=image_list[0]
            #     for  image in image_list:
            #         image_message = {
            #             "type": "image_url",
            #             "image_url": {
            #                 "url": image,
            #                 "detail": "low",
            #             },
                        
            #         }
            #         images.append(image_message)
            #     # images = "\n\n".join(image_list)
            #     prompt_string = prompt_string  + self.images_prompt + self.question_prompt 
            #     prompt["images"] = lambda x: images
            # else:
            prompt_string = prompt_string + self.question_prompt

        if to_translate != 'None': #To manage the case if the required language is not found in documentation, translate from the english document to the language requested
            prompt_string += f"\nTranslate the answer in the following language: {to_translate}"
        
        prompt_string = ChatPromptTemplate.from_template(prompt_string)
        
        prompt = prompt | prompt_string

        # chain = prompt | self.llm | StrOutputParser() if isContext == False else prompt | self.llm | JsonOutputParser(pydantic_object=GeneratedAnswer)
        if stream:
            chain = prompt | self.llm | StrOutputParser()
            answer = chain.stream(query)
            return answer    
        else:
            chain = prompt | self.llm | JsonOutputParser(pydantic_object=GeneratedAnswer) if isContext == True else prompt | self.llm | StrOutputParser()
            answer = chain.invoke(query)
            return answer

    