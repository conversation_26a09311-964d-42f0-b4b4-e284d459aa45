[common]
run_mode = TEST
logout_url = https://electroluxprofessional.unily.com

[log]
loglevel = DEBUG
log_folder = C:\Users\<USER>\compli-bot\pride_indexer

[ad]
ad_schema_callback=http

[db]
db_driver = ODBC Driver 17 for SQL Server

[txt2sql.oracle_oci]
oci_client_path = C:\Users\<USER>\oracle\instantclient_21_14
oci_dsn = dwhtest_high


[txt2sql.llm]
endpoint = 	https://epr002-oai-cmn-dev-weu-001.int.electroluxprofessional.com/


[txt2sql.embedding]
endpoint = https://epr002-oai-cmn-dev-weu-001.int.electroluxprofessional.com/


[txt2sql.preliminary]
deployment= gpt-4o-mini

[rag.llm]
endpoint = 	https://epr002-oai-cmn-dev-weu-001.int.electroluxprofessional.com/

[rag.embedding]
endpoint = https://epr002-oai-cmn-dev-weu-001.int.electroluxprofessional.com/


[rag_indexer.document_intelligence]
endpoint = https://epr-ai-rag-di-weu-dev-loader.cognitiveservices.azure.com/

[rag_indexer.llm]
endpoint = https://epr002-oai-cmn-dev-weu-001.int.electroluxprofessional.com/


[call_center_bot.rag.storage]
endpoint = https://epr-ai-srch-rag.search.windows.net
index_name = it-documents-pride-dev

[call_center_bot.rag_indexer.fetcher]
local_root =
pride_products_root = \\fs_prod.epr.electroluxprofessional.com\pride_fs_prod\Attach\
pride_document_types = PDS 

[call_center_bot.azure_blob_storage]
url = https://epr002stcmnallweu001.int.electroluxprofessional.com/
name = epr002stcmnallweu001
table_container = pride-tables-rag
image_container = pride-images-rag

[how_to_bot.rag_indexer.fetcher]
local_root = "C:\Users\<USER>\Desktop\PROJECTS\ELETTROLUX PROFESSIONAL\compli-bot\pride_indexer\test\documents\IT-learning\JDE"
pride_products_root = 

