<!-- templates/base.html -->

<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="shortcut icon" href="{{ url_for('static', filename='favicon.ico') }}">
    <title>Compli-Bot Example</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <!--<script src="/docs/5.3/assets/js/color-modes.js"></script>-->
{% block head_css %} {% endblock %}
{% block head_js %}  {% endblock %}

</head>

<body class="d-flex flex-column h-100">

    <header>
        <nav class="navbar navbar-expand-md navbar-dark fixed-top bg-epr">
            <div class="container-fluid">
                <!-- Brand and main navigation -->
                <a class="navbar-brand" href="{{ url_for('rbot.index') }}">
                    <img src="{{url_for('static', filename='img/logo_white.svg')}}" height="40px" alt="Compli-Bot Logo" />
                </a>

                <!-- Mobile toggle button -->
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarCollapse" aria-controls="navbarCollapse" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <!-- Collapsible navbar content -->
                <div class="collapse navbar-collapse" id="navbarCollapse">
                    <!-- Main navigation links -->
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link active" href="{{ url_for('rbot.index') }}" aria-current="page">
                                <i class="fas fa-home me-1"></i>Home
                            </a>
                        </li>
                        <!-- Add more navigation items here as needed -->
                    </ul>

                    <!-- Right-aligned items -->
                    <ul class="navbar-nav ms-auto align-items-center">
                        <li class="nav-item me-3">
                            <span id="runningEnvDisplay" class="badge bg-secondary" style="display: none;" title="Running Environment"></span>
                        </li>
                        <li class="nav-item">
                            <span id="versionDisplay" class="navbar-text" style="cursor: pointer;" title="Click to view changelog">
                                <i class="fas fa-code-branch me-1"></i><span class="version-text"></span>
                            </span>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <!-- Begin page content -->
    <main class="flex-shrink-0">
        <div class="container">
            {% with messages = get_flashed_messages(with_categories=true) %}
              {% if messages %}
                <div aria-live="polite" aria-atomic="true" class="position-fixed top-0 end-0 p-3" style="z-index: 1080;">
                  {% for category, message in messages %}
                    <div class="toast align-items-center text-bg-{{ 'success' if category == 'success' else 'danger' if category == 'danger' else 'info' }} border-0 mb-2" role="alert" aria-live="assertive" aria-atomic="true" data-bs-delay="3500">
                      <div class="d-flex">
                        <div class="toast-body">
                          {{ message }}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                      </div>
                    </div>
                  {% endfor %}
                </div>
              {% endif %}
            {% endwith %}
            {% block content %}
            {% endblock %}
        </div>
    </main>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" crossorigin="anonymous"></script>
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script>
      document.addEventListener('DOMContentLoaded', function() {
        // Initialize toasts
        var toastElList = [].slice.call(document.querySelectorAll('.toast'));
        toastElList.forEach(function(toastEl) {
          var toast = new bootstrap.Toast(toastEl);
          toast.show();
        });
        
        // Load version information
        loadVersionInfo();
      });
      
      function loadVersionInfo() {
        // Try translator bot endpoint first, fallback to main app endpoint
        let versionUrl = window.location.pathname.startsWith('/translator') ? 
          '/translator/api/version' : '/version';
        
        fetch(versionUrl)
          .then(response => response.json())
          .then(data => {
            // Display running environment
            const envDisplay = document.getElementById('runningEnvDisplay');
            if (data.running_env && envDisplay) {
              envDisplay.textContent = data.running_env;
              envDisplay.style.display = 'inline';
            }
            
            // Display git version
            const versionDisplay = document.getElementById('versionDisplay');
            const versionText = versionDisplay?.querySelector('.version-text');
            if (data.git && versionDisplay && versionText) {
              versionText.textContent = data.git;
              versionDisplay.addEventListener('click', function() {
                showChangelog(data.git);
              });
            }
          })
          .catch(error => {
            console.log('Error loading version info:', error);
          });
      }
      
      function showChangelog(version) {
        // Try to get changelog preview
        let changelogUrl = window.location.pathname.startsWith('/translator') ? 
          '/translator/api/changelog/preview' : '/changelog/preview';
          
        fetch(changelogUrl)
          .then(response => response.text())
          .then(changelog => {
            showChangelogModal(changelog);
          })
          .catch(error => {
            showChangelogModal('Changelog not available');
          });
      }
      
      function showChangelogModal(changelog) {
        // Create modal for changelog
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
          <div class="modal-dialog modal-lg">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title">Changelog</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
              </div>
              <div class="modal-body">
                <pre style="white-space: pre-wrap; margin: 0; padding: 16px; background-color: #f8f9fa; border-radius: 4px; max-height: 400px; overflow-y: auto;">${changelog}</pre>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
              </div>
            </div>
          </div>
        `;
        
        document.body.appendChild(modal);
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();
        
        // Remove modal from DOM when hidden
        modal.addEventListener('hidden.bs.modal', () => {
          document.body.removeChild(modal);
        });
      }
    </script>
    {% block tail_js %} {% endblock %}
</body>

</html>